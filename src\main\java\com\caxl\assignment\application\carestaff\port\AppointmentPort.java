package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.entities.Appointment;

import java.util.List;
import java.util.UUID;

/**
 * Port interface for appointment persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface AppointmentPort {

    /**
     * Save appointment.
     * 
     * @param appointment Appointment to save
     * @return Saved appointment
     */
    Appointment save(Appointment appointment);

    /**
     * Save all appointments.
     * 
     * @param appointments List of appointments to save
     * @return List of saved appointments
     */
    List<Appointment> saveAll(List<Appointment> appointments);
}

