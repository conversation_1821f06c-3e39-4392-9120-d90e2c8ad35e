# Enhanced Homecare Scheduling Service

A comprehensive US homecare scheduling solution built with Timefold (community edition) optimization engine, featuring state-specific compliance rules, geofencing-based clinician assignment, and scalable domain modeling.

## 🏥 Features

### Core Scheduling Capabilities
- **Timefold-powered optimization** - Advanced constraint solving for optimal visit assignments
- **Multi-state compliance** - California, Texas, and extensible state-specific regulations
- **Geofencing integration** - Geographic zone-based clinician assignment
- **Real-time scheduling** - Dynamic rescheduling and optimization
- **Skill-based matching** - Automatic clinician-patient skill alignment

### US Healthcare Compliance
- **State licensing validation** - Automatic verification of clinician credentials
- **Working hour regulations** - State-specific overtime and break requirements
- **Documentation compliance** - Medicare/Medicaid documentation standards
- **Clinical supervision** - Hierarchical care team management

### Enterprise Architecture
- **Hexagonal architecture** - Clean separation of concerns
- **Event-driven design** - Reactive scheduling updates
- **Microservice ready** - Docker containerization with monitoring
- **Production monitoring** - Prometheus metrics and Grafana dashboards

## 🚀 Quick Start

### Prerequisites
- Java 17+
- Docker & Docker Compose
- Maven 3.8+

### Development Setup
```bash
# Clone the repository
git clone <repository-url>
cd caxl-optaplanner-assignment-service

# Start infrastructure services
docker-compose up -d postgres redis prometheus grafana

# Build and run the application
mvn clean compile
mvn spring-boot:run
```

### Docker Deployment
```bash
# Build and start all services
docker-compose up --build

# Scale the application
docker-compose up --scale homecare-service=3
```

### API Endpoints
- **Health Check**: `GET /actuator/health`
- **Schedule Optimization**: `POST /api/v1/healthcare/schedule/optimize`
- **State Compliance**: `POST /api/v1/healthcare/compliance/validate`
- **Geofencing**: `POST /api/v1/healthcare/geofencing/validate`

## 🏗️ Architecture

### Domain Model
```
Organization
├── Teams
│   ├── CareStaff (with state licenses & zones)
│   └── Equipment
└── Patients
    └── HomecareVisits (planning entities)
```

### Planning Variables
- **assignedCareStaffId** - Which clinician is assigned to each visit
- **scheduledStartTime** - When each visit should start

### Constraint Categories
1. **Hard Constraints** (must be satisfied)
   - Care staff skill matching
   - State licensing compliance
   - Geofencing boundaries
   - Working hour regulations

2. **Soft Constraints** (optimization goals)
   - Travel time minimization
   - Patient preferences
   - Care continuity
   - Workload balancing

## 📊 State Compliance

### Supported States
- **California (CA)** - Detailed overtime, licensing, and supervision rules
- **Texas (TX)** - Rural area considerations and documentation requirements
- **Extensible** - Easy addition of new state regulations

### Compliance Features
- Automatic license verification
- Working hour tracking
- Break time enforcement
- Documentation deadline monitoring
- Clinical supervision requirements

## 🗺️ Geofencing

### Zone Management
- **Operating zones** - Define service areas for each clinician
- **Travel distance limits** - Maximum travel between visits
- **Geographic boundaries** - Polygon-based zone definitions
- **Rural area handling** - Special considerations for remote areas

### Geographic Features
- ZIP code coverage areas
- GPS coordinate validation
- Distance calculations
- Zone priority management

## 🔧 Configuration

### Application Properties
```yaml
# Timefold Solver Configuration
timefold:
  solver:
    termination:
      spent-limit: 30s
    move-thread-count: AUTO

# State Compliance
homecare:
  compliance:
    states:
      enabled: [CA, TX, NY, FL]
      default-overtime-threshold: 40h
```

### Environment Variables
- `ENVIRONMENT` - deployment environment (development/production)
- `DATABASE_URL` - PostgreSQL connection string
- `REDIS_URL` - Redis cache connection
- `PROMETHEUS_ENABLED` - Enable metrics collection

## 📈 Monitoring & Observability

### Metrics Available
- Optimization performance (`homecare_optimization_duration_seconds`)
- State compliance violations (`homecare_compliance_violations_total`)
- Geofencing violations (`homecare_geofencing_violations_total`)
- Staff utilization (`homecare_staff_utilization_ratio`)
- Visit assignment rates (`homecare_visits_assigned_total`)

### Dashboards
- **Grafana Dashboard** - Real-time scheduling performance
- **Prometheus Alerts** - Critical system notifications
- **Structured Logging** - JSON-formatted application logs

### Access Points
- **Grafana**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Application Metrics**: http://localhost:8080/actuator/prometheus

## 🧪 Testing

### Run Unit Tests
```bash
mvn test
```

### Run Integration Tests
```bash
mvn test -Dtest=*IntegrationTest
```

### Constraint Testing
```bash
mvn test -Dtest=EnhancedHomecareConstraintProviderTest
```

### Test Scenarios
Located in `src/test/resources/scenarios/`:
- Single patient, single staff
- Multiple patients, multiple staff
- State compliance scenarios
- Geofencing validation
- Skill hierarchy fallback

## 🏥 Sample Usage

### Schedule Optimization Request
```json
{
  "homecareVisits": [
    {
      "visitId": "visit-001",
      "patientId": "patient-001",
      "visitSchedule": {
        "estimatedDuration": "PT1H",
        "earliestStartTime": "2025-05-28T08:00:00",
        "latestStartTime": "2025-05-28T17:00:00"
      },
      "clinicalRequirements": {
        "requiredSkills": ["nursing", "wound-care"]
      }
    }
  ],
  "careStaff": [
    {
      "id": "staff-001",
      "stateLicensing": {
        "state": "CA",
        "licenseNumber": "RN123456",
        "expirationDate": "2025-12-31"
      },
      "operatingZones": [
        {
          "zoneId": "sf-central",
          "maxTravelDistanceMiles": 25.0
        }
      ]
    }
  ]
}
```

### State Compliance Validation
```json
{
  "visit": {
    "visitId": "visit-001",
    "scheduledStartTime": "2025-05-28T14:00:00",
    "duration": "PT2H"
  },
  "state": {
    "stateCode": "CA",
    "maxDailyHours": 12,
    "overtimeThreshold": 8,
    "mandatoryBreakMinutes": 30
  }
}
```

## 🔒 Security

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (RBAC)
- State-specific data isolation
- Audit logging for compliance

### Data Protection
- Encrypted data at rest
- TLS for data in transit
- PII anonymization capabilities
- HIPAA compliance considerations

## 🚀 Deployment

### Production Checklist
- [ ] Configure state-specific compliance rules
- [ ] Set up geographic zones and boundaries
- [ ] Import care staff licenses and certifications
- [ ] Configure monitoring and alerting
- [ ] Set up backup and disaster recovery
- [ ] Configure log aggregation
- [ ] Security audit and penetration testing

### Scaling Considerations
- **Horizontal scaling** - Multiple service instances
- **Database optimization** - Read replicas and partitioning
- **Cache strategy** - Redis for frequently accessed data
- **Load balancing** - Nginx reverse proxy configuration

## 📚 Documentation

### API Documentation
- **OpenAPI/Swagger** - Available at `/swagger-ui.html`
- **Postman Collection** - Import ready API collection
- **cURL Examples** - Command-line usage examples

### Architecture Documentation
- **Domain Model** - Detailed class diagrams
- **Constraint Rules** - Business rule documentation
- **State Compliance** - Regulatory requirement mapping
- **Deployment Guide** - Infrastructure setup instructions

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch (`feature/new-state-compliance`)
3. Implement changes with tests
4. Run full test suite
5. Submit pull request

### Adding New States
1. Create state configuration in `USState` enum
2. Implement state-specific rules in `StateComplianceService`
3. Add constraint validations
4. Create test scenarios
5. Update documentation

## 📞 Support

### Getting Help
- **Documentation**: Check the `/docs` directory
- **Issues**: GitHub issue tracker
- **Stack Overflow**: Tag `timefold` and `homecare-scheduling`

### Enterprise Support
For enterprise deployments with SLA requirements:
- Professional services for implementation
- Custom state compliance rule development
- Performance optimization consulting
- 24/7 production support

---

**Built with ❤️ for US Healthcare** - Optimizing homecare delivery through intelligent scheduling
