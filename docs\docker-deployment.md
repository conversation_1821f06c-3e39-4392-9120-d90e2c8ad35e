# Docker Deployment Guide

## Overview

The CAXL Assignment Service is a unified application that combines both assignment optimization and carestaff matching capabilities. This guide covers Docker deployment for the consolidated service.

## Architecture

The unified service includes:
- **Assignment Optimization**: Timefold-based scheduling optimization
- **CareStaff Matching**: Spatial filtering and constraint-based matching
- **PostGIS Integration**: Geographic data processing
- **Redis Caching**: Performance optimization
- **Monitoring Stack**: Prometheus + Grafana

## Quick Start

### 1. Prerequisites

```bash
# Ensure Docker and Docker Compose are installed
docker --version
docker-compose --version
```

### 2. Start the Complete Stack

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f caxl-app

# Check service health
docker-compose ps
```

### 3. Access Services

- **Assignment Service**: http://localhost:8080
- **Health Check**: http://localhost:8080/actuator/health
- **API Documentation**: http://localhost:8080/swagger-ui.html
- **Grafana Dashboard**: http://localhost:3000 (admin/admin)
- **Prometheus**: http://localhost:9090
- **Database**: localhost:5432 (caxl_user/caxl_password)

## Service Configuration

### Database (PostgreSQL + PostGIS)

```yaml
postgres:
  image: postgis/postgis:16-3.4-alpine
  environment:
    POSTGRES_DB: homecare_scheduling
    POSTGRES_USER: caxl_user
    POSTGRES_PASSWORD: caxl_password
  ports:
    - "5432:5432"
```

### Application Service

```yaml
caxl-app:
  build:
    context: .
    dockerfile: Dockerfile
  environment:
    # Database Configuration
    SPRING_DATASOURCE_URL: ***************************************************
    SPRING_DATASOURCE_USERNAME: caxl_user
    SPRING_DATASOURCE_PASSWORD: caxl_password
    SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: org.hibernate.spatial.dialect.postgis.PostgisPG10Dialect
    
    # Assignment Service Configuration
    ASSIGNMENT_DEFAULT_MAX_TRAVEL_DISTANCE: 25
    ASSIGNMENT_GEOFENCING_ENABLED: true
    
    # CareStaff Matching Configuration
    CARESTAFF_MATCHING_DEFAULT_SEARCH_RADIUS_KM: 50.0
    CARESTAFF_MATCHING_GEOFENCE_STRICT_CONTAINMENT: true
```

## Environment Variables

### Core Configuration

| Variable | Description | Default |
|----------|-------------|---------|
| `SPRING_PROFILES_ACTIVE` | Active Spring profile | `docker` |
| `SERVER_PORT` | Application port | `8080` |
| `SPRING_DATASOURCE_URL` | Database connection URL | - |
| `SPRING_DATASOURCE_USERNAME` | Database username | - |
| `SPRING_DATASOURCE_PASSWORD` | Database password | - |

### Assignment Service

| Variable | Description | Default |
|----------|-------------|---------|
| `ASSIGNMENT_DEFAULT_MAX_TRAVEL_DISTANCE` | Max travel distance (km) | `25` |
| `ASSIGNMENT_GEOFENCING_ENABLED` | Enable geofencing | `true` |
| `TIMEFOLD_SOLVER_TERMINATION_SPENT_LIMIT` | Solver time limit | `30s` |

### CareStaff Matching

| Variable | Description | Default |
|----------|-------------|---------|
| `CARESTAFF_MATCHING_DEFAULT_SEARCH_RADIUS_KM` | Search radius | `50.0` |
| `CARESTAFF_MATCHING_MIN_SCORE_THRESHOLD` | Minimum match score | `0.0` |
| `CARESTAFF_MATCHING_GEOFENCE_STRICT_CONTAINMENT` | Strict geofencing | `true` |

## Monitoring

### Health Checks

```bash
# Application health
curl http://localhost:8080/actuator/health

# Database health
docker-compose exec postgres pg_isready -U caxl_user

# Redis health
docker-compose exec redis redis-cli ping
```

### Logs

```bash
# Application logs
docker-compose logs -f caxl-app

# Database logs
docker-compose logs -f postgres

# All services
docker-compose logs -f
```

### Metrics

- **Prometheus**: http://localhost:9090/targets
- **Grafana**: http://localhost:3000/dashboards
- **Application Metrics**: http://localhost:8080/actuator/prometheus

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :8080
   
   # Change ports in docker-compose.yml if needed
   ```

2. **Database Connection Issues**
   ```bash
   # Check database logs
   docker-compose logs postgres
   
   # Verify database is ready
   docker-compose exec postgres pg_isready -U caxl_user
   ```

3. **Memory Issues**
   ```bash
   # Increase memory limits in docker-compose.yml
   environment:
     JAVA_OPTS: "-Xmx2g -Xms1g"
   ```

### Debug Mode

```bash
# Start with debug logging
docker-compose up -d
docker-compose exec caxl-app sh -c "
  export LOGGING_LEVEL_COM_CAXL_ASSIGNMENT=DEBUG
  java -jar app.jar
"
```

## Production Deployment

### Security Considerations

1. **Change Default Passwords**
   ```yaml
   environment:
     POSTGRES_PASSWORD: your-secure-password
     SPRING_SECURITY_USER_PASSWORD: your-admin-password
   ```

2. **Use Secrets Management**
   ```yaml
   secrets:
     db_password:
       file: ./secrets/db_password.txt
   ```

3. **Enable SSL/TLS**
   ```yaml
   environment:
     SPRING_DATASOURCE_URL: *******************************************************************
   ```

### Performance Tuning

1. **JVM Options**
   ```yaml
   environment:
     JAVA_OPTS: "-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC"
   ```

2. **Database Tuning**
   ```yaml
   postgres:
     command: postgres -c shared_preload_libraries=pg_stat_statements -c max_connections=200
   ```

## Backup and Recovery

### Database Backup

```bash
# Create backup
docker-compose exec postgres pg_dump -U caxl_user homecare_scheduling > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U caxl_user homecare_scheduling < backup.sql
```

### Volume Backup

```bash
# Backup volumes
docker run --rm -v caxl-postgres-data:/data -v $(pwd):/backup alpine tar czf /backup/postgres-backup.tar.gz /data
```

## Scaling

### Horizontal Scaling

```yaml
caxl-app:
  deploy:
    replicas: 3
  environment:
    SPRING_PROFILES_ACTIVE: docker,cluster
```

### Load Balancing

```yaml
nginx:
  image: nginx:alpine
  volumes:
    - ./nginx-lb.conf:/etc/nginx/nginx.conf
  ports:
    - "80:80"
  depends_on:
    - caxl-app
```

This unified Docker deployment provides a complete, production-ready environment for the CAXL Assignment Service with all its capabilities.
