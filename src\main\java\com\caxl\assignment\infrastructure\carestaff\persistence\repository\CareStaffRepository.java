package com.caxl.assignment.infrastructure.persistence.repository;

import com.caxl.assignment.application.port.out.persistence.CareStaffPort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Spring Data JPA repository for care staff entities with spatial queries.
 * Extends existing CareStaff entity from the main system.
 */
@Repository
public interface CareStaffRepository extends JpaRepository<Object, UUID> {

    /**
     * Find potential care staff by skills and proximity using spatial queries.
     * Uses ST_DWithin for geographic filtering and joins with skill mappings.
     */
    @Query(value = """
        SELECT DISTINCT cs.carestaff_id, cs.enc_first_name, cs.enc_last_name,
               l.coordinates, cs.experience_years, cs.active,
               ARRAY_AGG(DISTINCT csm.skill_id) as skill_ids,
               ARRAY_AGG(DISTINCT cs.operating_zones) as operating_zones,
               ARRAY_AGG(DISTINCT cs.languages) as languages
        FROM caresstaff_pe cs
        JOIN location l ON cs.location_id = l.location_id
        LEFT JOIN carestaff_has_skill csm ON cs.carestaff_id = csm.carestaff_id
        WHERE cs.active = true
        AND (:requireAllSkills = false OR 
             (SELECT COUNT(DISTINCT skill_id) FROM carestaff_has_skill 
              WHERE carestaff_id = cs.carestaff_id AND skill_id = ANY(:skillIds)) = :skillCount)
        AND (:requireAllSkills = true OR 
             EXISTS(SELECT 1 FROM carestaff_has_skill 
                    WHERE carestaff_id = cs.carestaff_id AND skill_id = ANY(:skillIds)))
        AND ST_DWithin(l.coordinates, ST_SetSRID(ST_Point(:longitude, :latitude), 4326), :radiusMeters)
        GROUP BY cs.carestaff_id, cs.enc_first_name, cs.enc_last_name, 
                 l.coordinates, cs.experience_years, cs.active
        ORDER BY ST_Distance(l.coordinates, ST_SetSRID(ST_Point(:longitude, :latitude), 4326))
        """, nativeQuery = true)
    List<Object[]> findPotentialStaffBySkillsAndProximityNative(
            @Param("skillIds") UUID[] skillIds,
            @Param("skillCount") int skillCount,
            @Param("latitude") double latitude,
            @Param("longitude") double longitude,
            @Param("radiusMeters") double radiusMeters,
            @Param("requireAllSkills") boolean requireAllSkills
    );

    /**
     * Find service geofences for a care staff member.
     */
    @Query(value = """
        SELECT g.boundary
        FROM caresstaff_pe cs
        JOIN location l ON cs.location_id = l.location_id
        JOIN geofences g ON ST_Contains(g.boundary, l.coordinates)
        WHERE cs.carestaff_id = :staffId
        AND g.geofence_type = ANY(:geofenceTypes)
        AND g.is_active = true
        """, nativeQuery = true)
    List<Object> findServiceGeofencesForStaffNative(
            @Param("staffId") UUID staffId,
            @Param("geofenceTypes") String[] geofenceTypes
    );

    /**
     * Find overlapping appointments for a care staff member.
     */
    @Query(value = """
        SELECT a.appointment_id, a.scheduled_start_time, a.scheduled_end_time,
               a.service_request_id, a.patient_id, a.status
        FROM appointment a
        WHERE a.carestaff_id = :staffId
        AND a.status IN ('scheduled', 'in_progress')
        AND (
            (a.scheduled_start_time <= :endTime AND a.scheduled_end_time >= :startTime)
            OR (a.scheduled_start_time >= :startTime AND a.scheduled_start_time <= :endTime)
        )
        AND EXTRACT(EPOCH FROM (
            LEAST(a.scheduled_end_time, :endTime) - GREATEST(a.scheduled_start_time, :startTime)
        )) / 60 >= :overlapThresholdMinutes
        """, nativeQuery = true)
    List<Object[]> findOverlappingAppointmentsNative(
            @Param("staffId") UUID staffId,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("overlapThresholdMinutes") int overlapThresholdMinutes
    );
}
