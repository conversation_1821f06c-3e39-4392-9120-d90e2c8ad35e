package com.caxl.assignment.application.carestaff.port;

import java.util.UUID;

/**
 * Use case interface for overriding match suggestions with manual selections.
 * Driven port in hexagonal architecture.
 */
public interface OverrideMatchUseCase {

    /**
     * Override match suggestion with manual carestaff selection.
     * 
     * @param requestId The service request ID
     * @param overrideDetails Details of the override
     * @param schedulerId ID of the scheduler making the override
     * @return Success indicator
     */
    boolean overrideMatch(UUID requestId, OverrideRequestDto overrideDetails, UUID schedulerId);

    /**
     * DTO for override request details.
     */
    record OverrideRequestDto(
        UUID selectedCareStaffId,
        String reason,
        String explanation
    ) {}
}
