package com.caxl.carestaff.application.port.in;

import java.util.List;
import java.util.UUID;

/**
 * Use case interface for bulk assignment of match suggestions.
 * Driven port in hexagonal architecture.
 */
public interface AssignBulkMatchesUseCase {

    /**
     * Assign multiple match suggestions in bulk.
     * 
     * @param suggestionIds List of suggestion IDs to assign
     * @param schedulerId ID of the scheduler making the assignments
     * @return Bulk assignment result
     */
    BulkAssignmentResult assignBulk(List<UUID> suggestionIds, UUID schedulerId);

    /**
     * Result of bulk assignment operation.
     */
    record BulkAssignmentResult(
        int totalRequested,
        int successfulAssignments,
        int failedAssignments,
        List<String> errors
    ) {}
}
