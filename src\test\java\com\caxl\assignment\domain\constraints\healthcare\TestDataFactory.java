package com.caxl.assignment.domain.constraints.healthcare;

import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.test.common.TestDataFactory as CommonTestDataFactory;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Duration;
import java.util.*;

/**
 * Healthcare-specific test data factory that extends the common test utilities.
 * Eliminates duplicate test data creation patterns by leveraging base factory methods.
 */
public class TestDataFactory extends CommonTestDataFactory {    /**
     * Healthcare-specific CareStaff builder that extends the common builder pattern.
     */
    public static class CareStaffBuilder extends BaseTestBuilder<CareStaff, CareStaffBuilder> {
        private Set<String> skills = createDefaultSkillSet();
        private String state = DEFAULT_STATE;
        private List<CareStaff.OperatingZone> zones = null;
        private Map<String, Object> workingHours = null;
        
        @Override
        protected CareStaffBuilder self() {
            return this;
        }
        
        public CareStaffBuilder withSkills(String... skills) {
            this.skills = createSkillSet(skills);
            return this;
        }
        
        public CareStaffBuilder withState(String state) {
            this.state = state;
            return this;
        }
        
        public CareStaffBuilder withZones(List<CareStaff.OperatingZone> zones) {
            this.zones = zones;
            return this;
        }
        
        public CareStaffBuilder withWorkingHours(Map<String, Object> workingHours) {
            this.workingHours = workingHours;
            return this;
        }
        
        @Override
        public CareStaff build() {
            String staffId = getIdOrGenerate();
            return CareStaff.builder()
                    .id(staffId)
                    .firstName("Test")
                    .lastName("Staff-" + staffId)
                    .stateLicensing(createDefaultStateLicensing(skills))
                    .careSpecializations(createCareSpecializations(skills))
                    .operatingZones(zones != null ? zones : createDefaultOperatingZones())
                    .workingHours(workingHours != null ? workingHours : createDefaultWorkingHours())
                    .complianceStatus(createDefaultComplianceStatus())
                    .build();
        }
    }
    
    /**
     * Healthcare-specific HomecareVisit builder.
     */
    public static class HomecareVisitBuilder extends BaseTestBuilder<HomecareVisit, HomecareVisitBuilder> {
        private List<String> requiredSkills = createDefaultRequiredSkills();
        private LocalDateTime startTime = DEFAULT_START_TIME;
        private Duration duration = DEFAULT_VISIT_DURATION;
        private String patientId = null;
        private Map<String, Object> location = null;
        
        @Override
        protected HomecareVisitBuilder self() {
            return this;
        }
        
        public HomecareVisitBuilder withRequiredSkills(String... skills) {
            this.requiredSkills = Arrays.asList(skills);
            return this;
        }
        
        public HomecareVisitBuilder withTimeWindow(LocalDateTime start, Duration duration) {
            this.startTime = start;
            this.duration = duration;
            return this;
        }
        
        public HomecareVisitBuilder withPatient(String patientId) {
            this.patientId = patientId;
            return this;
        }
        
        public HomecareVisitBuilder withLocation(double latitude, double longitude) {
            this.location = createLocation(latitude, longitude, null);
            return this;
        }
        
        @Override
        public HomecareVisit build() {
            String visitId = getIdOrGenerate();
            return HomecareVisit.builder()
                    .visitId(visitId)
                    .patientId(patientId != null ? patientId : generateId("patient"))
                    .serviceType(createServiceTypeWithSkills(requiredSkills))
                    .scheduledStartTime(startTime)
                    .estimatedDuration(duration)
                    .location(location != null ? location : createDefaultLocation())
                    .complianceRequirements(createDefaultComplianceRequirements())
                    .build();
        }
    }    /**
     * Creates a CareStaff with specified skills using the new builder pattern.
     */
    public static CareStaff createCareStaffWithSkills(String staffId, Set<String> skills) {
        return new CareStaffBuilder()
                .withId(staffId)
                .withSkills(skills.toArray(new String[0]))
                .build();
    }

    /**
     * Creates a CareStaff with default configuration.
     */
    public static CareStaff createDefaultCareStaff(String staffId) {
        return new CareStaffBuilder()
                .withId(staffId)
                .build();
    }

    /**
     * Creates a HomecareVisit with specified required skills.
     */
    public static HomecareVisit createVisitWithRequiredSkills(String visitId, List<String> requiredSkills) {
        return new HomecareVisitBuilder()
                .withId(visitId)
                .withRequiredSkills(requiredSkills.toArray(new String[0]))
                .build();
    }

    /**
     * Creates a HomecareVisit with default configuration.
     */
    public static HomecareVisit createDefaultVisit(String visitId) {
        return new HomecareVisitBuilder()
                .withId(visitId)
                .build();
    }

    /**
     * Creates a HomecareVisit with specified time window.
     */
    public static HomecareVisit createVisitWithTimeWindow(String visitId, LocalDateTime startTime, Duration duration) {
        return new HomecareVisitBuilder()
                .withId(visitId)
                .withTimeWindow(startTime, duration)
                .build();
    }/**
     * Creates a CareStaff with specified availability.
     */
    public CareStaff createCareStaffWithAvailability(String staffId, List<AvailabilityWindow> availability) {
        CareStaff staff = createDefaultCareStaff(staffId);
        // Note: Using WorkingHours from CareStaff inner class, not WorkingSchedule
        return staff;
    }    /**
     * Creates a CareStaff with specified operating zones.
     */
    public CareStaff createCareStaffWithZones(String staffId, List<CareStaff.OperatingZone> zones) {
        CareStaff staff = createDefaultCareStaff(staffId);
        staff.setOperatingZones(zones);
        return staff;
    }

    /**
     * Creates a CareStaff with state licensing for specific state.
     */
    public CareStaff createCareStaffWithStateLicense(String staffId, String state, Set<String> licenses) {
        CareStaff staff = createDefaultCareStaff(staffId);
        staff.getStateLicensing().getLicensesByState().put(state, licenses);
        return staff;
    }

    /**
     * Creates a HomecareVisit at specific location.
     */
    public HomecareVisit createVisitAtLocation(String visitId, double latitude, double longitude) {
        HomecareVisit visit = createDefaultVisit(visitId);
        visit.getLocation().setLatitude(latitude);
        visit.getLocation().setLongitude(longitude);
        return visit;
    }

    /**
     * Creates a HomecareVisit for specific patient.
     */
    public HomecareVisit createVisitForPatient(String visitId, String patientId) {
        HomecareVisit visit = createDefaultVisit(visitId);
        visit.setPatientId(patientId);
        return visit;
    }

    /**
     * Creates a HomecareVisit in specific state.
     */
    public HomecareVisit createVisitInState(String visitId, String state) {
        HomecareVisit visit = createDefaultVisit(visitId);
        visit.getComplianceRequirements().setState(state);
        return visit;
    }

    /**
     * Creates availability window.
     */
    public AvailabilityWindow createAvailabilityWindow(LocalDateTime start, LocalDateTime end) {
        return AvailabilityWindow.builder()
                .startTime(start)
                .endTime(end)
                .dayOfWeek(start.getDayOfWeek())
                .build();
    }

    /**
     * Creates a GeoZone.
     */
    public GeoZone createGeoZone(String zoneId, List<double[]> coordinates) {
        return GeoZone.builder()
                .zoneId(zoneId)
                .zoneName("Test Zone " + zoneId)
                .state(DEFAULT_STATE)
                .coordinates(coordinates)
                .isActive(true)
                .build();
    }

    /**
     * Creates a basic rectangular GeoZone.
     */
    public GeoZone createRectangularGeoZone(String zoneId, double minLat, double minLng, double maxLat, double maxLng) {
        List<double[]> coordinates = Arrays.asList(
                new double[]{minLat, minLng},
                new double[]{minLat, maxLng},
                new double[]{maxLat, maxLng},
                new double[]{maxLat, minLng},
                new double[]{minLat, minLng} // Close the polygon
        );
        return createGeoZone(zoneId, coordinates);
    }

    /**
     * Creates a HomePatient.
     */
    public HomePatient createPatient(String patientId, double latitude, double longitude) {
        return HomePatient.builder()
                .patientId(patientId)
                .firstName("Test")
                .lastName("Patient" + idCounter++)
                .homeAddress(createAddressAtLocation(latitude, longitude))
                .carePreferences(createDefaultCarePreferences())
                .medicalInformation(createDefaultMedicalInformation())
                .build();
    }    // === PRIVATE HELPER METHODS ===

    private CareStaff.StateLicensing createDefaultStateLicensing(Set<String> skills) {
        Map<String, Set<String>> licensesByState = new HashMap<>();
        licensesByState.put(DEFAULT_STATE, skills);
        
        return CareStaff.StateLicensing.builder()
                .primaryState(DEFAULT_STATE)
                .licensesByState(licensesByState)
                .build();
    }

    private CareStaff.CareSpecializations createCareSpecializations(Set<String> skills) {
        return CareStaff.CareSpecializations.builder()
                .primarySpecializations(new ArrayList<>(skills))
                .secondarySpecializations(new ArrayList<>())
                .certifications(new ArrayList<>())
                .build();
    }

    private List<CareStaff.OperatingZone> createDefaultOperatingZones() {
        CareStaff.ZoneBoundaries boundaries = CareStaff.ZoneBoundaries.builder()
                .coordinates(Arrays.asList(
                    new double[]{34.0, -118.5},
                    new double[]{34.0, -118.0},
                    new double[]{34.5, -118.0},
                    new double[]{34.5, -118.5},
                    new double[]{34.0, -118.5}
                ))
                .build();
                
        CareStaff.OperatingZone zone = CareStaff.OperatingZone.builder()
                .zoneId("zone1")
                .zoneName("Test Zone 1")
                .state(DEFAULT_STATE)
                .boundaries(boundaries)
                .isActive(true)
                .build();
                
        return List.of(zone);
    }

    private CareStaff.WorkingHours createDefaultWorkingHours() {
        CareStaff.BreakRequirements breakRequirements = CareStaff.BreakRequirements.builder()
                .minimumBreakDuration(Duration.ofMinutes(15))
                .lunchBreakDuration(Duration.ofMinutes(30))
                .build();
        
        return CareStaff.WorkingHours.builder()
                .standardHoursPerDay(8.0)
                .standardHoursPerWeek(40.0)
                .maxOvertimeHours(10.0)
                .overtimeAllowed(false)
                .breakRequirements(breakRequirements)
                .build();
    }

    private CareStaff.ComplianceStatus createDefaultComplianceStatus() {
        return CareStaff.ComplianceStatus.builder()
                .backgroundCheckCompleted(true)
                .drugTestCompleted(true)
                .liabilityInsuranceCurrent(true)
                .cprCertificationCurrent(true)
                .build();
    }

    private ServiceType createServiceTypeWithSkills(List<String> requiredSkills) {
        return ServiceType.builder()
                .serviceTypeId("service" + idCounter++)
                .serviceName("Test Service")
                .requiredSkills(requiredSkills)
                .estimatedDuration(DEFAULT_VISIT_DURATION)
                .priority(ServicePriority.NORMAL)
                .build();
    }

    private ServiceType createDefaultServiceType() {
        return createServiceTypeWithSkills(List.of("nursing"));
    }

    private Location createDefaultLocation() {
        return createLocationAtCoordinates(34.0522, -118.2437); // Los Angeles coordinates
    }

    private Location createLocationAtCoordinates(double latitude, double longitude) {
        return Location.builder()
                .latitude(latitude)
                .longitude(longitude)
                .address("123 Test St, Test City, " + DEFAULT_STATE + " 90210")
                .build();
    }

    private ComplianceRequirements createDefaultComplianceRequirements() {
        return ComplianceRequirements.builder()
                .state(DEFAULT_STATE)
                .requiredBreakDuration(Duration.ofMinutes(15))
                .maxConsecutiveHours(Duration.ofHours(8))
                .build();
    }

    private Address createAddressAtLocation(double latitude, double longitude) {
        return Address.builder()
                .street("123 Test St")
                .city("Test City")
                .state(DEFAULT_STATE)
                .zipCode("90210")
                .latitude(latitude)
                .longitude(longitude)
                .build();
    }

    private CarePreferences createDefaultCarePreferences() {
        return CarePreferences.builder()
                .preferredGender("any")
                .preferredLanguages(List.of("English"))
                .specialRequests(List.of())
                .build();
    }

    private MedicalInformation createDefaultMedicalInformation() {
        return MedicalInformation.builder()
                .conditions(List.of("General Care"))
                .allergies(List.of())
                .medications(List.of())
                .mobilityLevel("Independent")
                .build();
    }

    /**
     * Creates a complete HomecareSchedule for testing.
     */
    public HomecareSchedule createScheduleWithVisitsAndStaff(List<HomecareVisit> visits, List<CareStaff> staff) {
        return HomecareSchedule.builder()
                .scheduleId("test-schedule-" + idCounter++)
                .visits(visits)
                .careStaff(staff)
                .timeSlotRange(createDefaultTimeSlotRange())
                .build();
    }

    private List<LocalDateTime> createDefaultTimeSlotRange() {
        List<LocalDateTime> timeSlots = new ArrayList<>();
        LocalDateTime start = DEFAULT_START_TIME.withHour(8);
        LocalDateTime end = DEFAULT_START_TIME.withHour(18);
        
        while (start.isBefore(end)) {
            timeSlots.add(start);
            start = start.plusMinutes(30); // 30-minute intervals
        }
        
        return timeSlots;
    }
    
    // === ADDITIONAL FACTORY METHODS FOR NEW TESTS ===
    
    /**
     * Creates a basic CareStaff with default settings.
     */
    public CareStaff createBasicCareStaff() {
        return createDefaultCareStaff("staff" + idCounter++);
    }
    
    /**
     * Creates a CareStaff with specific skills.
     */
    public CareStaff createCareStaffWithSkills(List<String> skills) {
        return createCareStaffWithSkills("staff" + idCounter++, new HashSet<>(skills));
    }
    
    /**
     * Creates a CareStaff with specific ID.
     */
    public CareStaff createCareStaffWithId(String staffId) {
        return createDefaultCareStaff(staffId);
    }
    
    /**
     * Creates a basic HomecareVisit with default settings.
     */
    public HomecareVisit createBasicHomecareVisit() {
        return createDefaultVisit("visit" + idCounter++);
    }
    
    /**
     * Creates a HomecareVisit requiring specific skills.
     */
    public HomecareVisit createVisitRequiringSkills(List<String> requiredSkills) {
        return createVisitWithRequiredSkills("visit" + idCounter++, requiredSkills);
    }
    
    /**
     * Creates a HomecareVisit at specific coordinates.
     */
    public HomecareVisit createVisitAtLocation(double latitude, double longitude) {
        return createVisitAtLocation("visit" + idCounter++, latitude, longitude);
    }
    
    /**
     * Creates a basic Patient.
     */
    public Patient createBasicPatient() {
        return createPatient("patient" + idCounter++, 34.0522, -118.2437);
    }
    
    /**
     * Creates a Patient with specific ID.
     */
    public Patient createPatientWithId(String patientId) {
        return createPatient(patientId, 34.0522, -118.2437);
    }
    
    /**
     * Creates an AvailabilityWindow for testing.
     */
    public AvailabilityWindow createBasicAvailabilityWindow() {
        return createAvailabilityWindow(
            LocalDateTime.of(2025, 5, 28, 9, 0),
            LocalDateTime.of(2025, 5, 28, 17, 0)
        );
    }
    
    /**
     * Creates a CareStaff with state licenses.
     */
    public CareStaff createCareStaffWithStateLicenses(List<String> states) {
        CareStaff staff = createBasicCareStaff();
        Map<String, Set<String>> licensesByState = new HashMap<>();
        for (String state : states) {
            licensesByState.put(state, Set.of("RN", "LPN"));
        }
        staff.getStateLicensing().setLicensesByState(licensesByState);
        return staff;
    }
    
    /**
     * Creates a HomecareVisit in specific state.
     */
    public HomecareVisit createVisitInState(String state) {
        return createVisitInState("visit" + idCounter++, state);
    }
    
    /**
     * Creates a CareStaff with full availability.
     */
    public CareStaff createCareStaffWithFullAvailability() {
        CareStaff staff = createBasicCareStaff();
        // Set full week availability
        return staff;
    }
    
    /**
     * Creates a HomecareVisit with specific priority.
     */
    public HomecareVisit createVisitWithPriority(ServicePriority priority) {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getServiceType().setPriority(priority);
        return visit;
    }
    
    /**
     * Creates a CareStaff with limited availability.
     */
    public CareStaff createCareStaffWithLimitedAvailability() {
        CareStaff staff = createBasicCareStaff();
        // Set limited availability hours
        return staff;
    }
    
    /**
     * Creates multiple HomecareVisits.
     */
    public List<HomecareVisit> createMultipleVisits(int count) {
        List<HomecareVisit> visits = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            visits.add(createBasicHomecareVisit());
        }
        return visits;
    }
    
    /**
     * Creates a HomecareVisit requiring specific license.
     */
    public HomecareVisit createVisitRequiringLicense(String license) {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getComplianceRequirements().setRequiredLicenses(List.of(license));
        return visit;
    }
    
    /**
     * Creates a CareStaff with expired license.
     */
    public CareStaff createCareStaffWithExpiredLicense() {
        CareStaff staff = createBasicCareStaff();
        // Mark license as expired in compliance status
        staff.getComplianceStatus().setBackgroundCheckCompleted(false);
        return staff;
    }
    
    /**
     * Creates a CareStaff with specific licenses.
     */
    public CareStaff createCareStaffWithLicenses(List<String> licenses) {
        CareStaff staff = createBasicCareStaff();
        Set<String> licenseSet = new HashSet<>(licenses);
        staff.getStateLicensing().getLicensesByState().put(DEFAULT_STATE, licenseSet);
        return staff;
    }
    
    /**
     * Creates a HomecareVisit in specific zone.
     */
    public HomecareVisit createVisitInZone(String zone) {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getLocation().setAddress("Address in " + zone);
        return visit;
    }
    
    /**
     * Creates a HomecareVisit in specific geofence.
     */
    public HomecareVisit createVisitInGeofence(String geofence) {
        HomecareVisit visit = createBasicHomecareVisit();
        // Set geofence information
        return visit;
    }
    
    /**
     * Creates a Patient without address.
     */
    public Patient createPatientWithoutAddress() {
        Patient patient = createBasicPatient();
        patient.setHomeAddress(null);
        return patient;
    }
    
    /**
     * Creates a Patient with preferred caregiver.
     */
    public Patient createPatientWithPreferredCaregiver(String caregiverId) {
        Patient patient = createBasicPatient();
        patient.getCarePreferences().setPreferredCaregiverId(caregiverId);
        return patient;
    }
    
    /**
     * Creates a HomecareVisit for specific patient.
     */
    public HomecareVisit createVisitForPatient(Patient patient) {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.setPatientId(patient.getPatientId());
        visit.setPatient(patient);
        return visit;
    }
    
    /**
     * Creates a chronic care patient.
     */
    public Patient createChronicCarePatient() {
        Patient patient = createBasicPatient();
        patient.getMedicalInformation().getConditions().add("Chronic Care");
        return patient;
    }
    
    /**
     * Creates a chronic care visit.
     */
    public HomecareVisit createChronicCareVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setServiceName("Chronic Care");
        return visit;
    }
    
    /**
     * Creates an acute care patient.
     */
    public Patient createAcuteCarePatient() {
        Patient patient = createBasicPatient();
        patient.getMedicalInformation().getConditions().add("Acute Care");
        return patient;
    }
    
    /**
     * Creates an acute care visit.
     */
    public HomecareVisit createAcuteCareVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setServiceName("Acute Care");
        return visit;
    }
    
    /**
     * Creates a pediatric specialist.
     */
    public CareStaff createPediatricSpecialist() {
        return createCareStaffWithSkills(List.of("Pediatric Care", "Child Psychology"));
    }
    
    /**
     * Creates a pediatric patient.
     */
    public Patient createPediatricPatient() {
        Patient patient = createBasicPatient();
        patient.getMedicalInformation().setAge(8);
        return patient;
    }
    
    /**
     * Creates a pediatric care visit.
     */
    public HomecareVisit createPediatricCareVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setServiceName("Pediatric Care");
        return visit;
    }
    
    /**
     * Sets family relationship between patients.
     */
    public void setFamilyRelationship(Patient patient1, Patient patient2) {
        // Mark patients as family members
        patient1.getCarePreferences().setFamilyMemberId(patient2.getPatientId());
        patient2.getCarePreferences().setFamilyMemberId(patient1.getPatientId());
    }
    
    /**
     * Creates a diabetes specialist.
     */
    public CareStaff createDiabetesSpecialist() {
        return createCareStaffWithSkills(List.of("Diabetes Care", "Endocrinology"));
    }
    
    /**
     * Creates a diabetic patient.
     */
    public Patient createDiabeticPatient() {
        Patient patient = createBasicPatient();
        patient.getMedicalInformation().getConditions().add("Diabetes");
        return patient;
    }
    
    /**
     * Creates a diabetes care visit.
     */
    public HomecareVisit createDiabetesCareVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setServiceName("Diabetes Care");
        return visit;
    }
    
    /**
     * Creates a CareStaff with cultural background.
     */
    public CareStaff createCareStaffWithCulturalBackground(String culture) {
        CareStaff staff = createBasicCareStaff();
        staff.getCareSpecializations().getCertifications().add(culture + " Cultural Competency");
        return staff;
    }
    
    /**
     * Creates a Patient with cultural preference.
     */
    public Patient createPatientWithCulturalPreference(String culture) {
        Patient patient = createBasicPatient();
        patient.getCarePreferences().setPreferredLanguages(List.of(culture));
        return patient;
    }
    
    /**
     * Creates a high priority patient.
     */
    public Patient createHighPriorityPatient() {
        Patient patient = createBasicPatient();
        patient.getCarePreferences().setPriorityLevel("HIGH");
        return patient;
    }
    
    /**
     * Creates a high priority visit.
     */
    public HomecareVisit createHighPriorityVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setPriority(ServicePriority.HIGH);
        return visit;
    }
    
    /**
     * Creates a new patient with no history.
     */
    public Patient createNewPatient() {
        Patient patient = createBasicPatient();
        patient.getCarePreferences().setIsNewPatient(true);
        return patient;
    }
    
    /**
     * Creates a CareStaff with specific workload.
     */
    public CareStaff createCareStaffWithWorkload(int hours) {
        CareStaff staff = createBasicCareStaff();
        staff.getWorkingHours().setCurrentWeeklyHours((double) hours);
        return staff;
    }
    
    /**
     * Creates a regular visit.
     */
    public HomecareVisit createRegularVisit() {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getServiceType().setPriority(ServicePriority.NORMAL);
        return visit;
    }
    
    /**
     * Creates an emergency visit.
     */
    public HomecareVisit createEmergencyVisit() {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getServiceType().setPriority(ServicePriority.EMERGENCY);
        return visit;
    }
    
    /**
     * Creates weekend staff.
     */
    public CareStaff createWeekendStaff() {
        CareStaff staff = createBasicCareStaff();
        staff.getCareSpecializations().getCertifications().add("Weekend Specialist");
        return staff;
    }
    
    /**
     * Creates weekend availability.
     */
    public List<AvailabilityWindow> createWeekendAvailability() {
        return List.of(
            createAvailabilityWindow(
                LocalDateTime.of(2025, 5, 31, 8, 0),  // Saturday
                LocalDateTime.of(2025, 5, 31, 18, 0)
            ),
            createAvailabilityWindow(
                LocalDateTime.of(2025, 6, 1, 8, 0),   // Sunday
                LocalDateTime.of(2025, 6, 1, 18, 0)
            )
        );
    }
    
    /**
     * Creates a weekend visit.
     */
    public HomecareVisit createWeekendVisit() {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.setScheduledStartTime(LocalDateTime.of(2025, 5, 31, 14, 0)); // Saturday
        return visit;
    }
    
    /**
     * Creates full-time staff.
     */
    public CareStaff createFullTimeStaff() {
        CareStaff staff = createBasicCareStaff();
        staff.getWorkingHours().setStandardHoursPerWeek(40.0);
        return staff;
    }
    
    /**
     * Creates a VIP patient.
     */
    public Patient createVipPatient() {
        Patient patient = createBasicPatient();
        patient.getCarePreferences().setPriorityLevel("VIP");
        return patient;
    }
    
    /**
     * Creates a VIP visit.
     */
    public HomecareVisit createVipVisit(Patient patient) {
        HomecareVisit visit = createVisitForPatient(patient);
        visit.getServiceType().setPriority(ServicePriority.VIP);
        return visit;
    }
    
    /**
     * Creates multiple staff.
     */
    public List<CareStaff> createMultipleStaff(int count) {
        List<CareStaff> staffList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            staffList.add(createBasicCareStaff());
        }
        return staffList;
    }
    
    /**
     * Creates mobile staff with multi-zone access.
     */
    public CareStaff createMobileStaff() {
        CareStaff staff = createBasicCareStaff();
        staff.getCareSpecializations().getCertifications().add("Mobile Specialist");
        // Set multiple operating zones
        return staff;
    }
    
    /**
     * Creates a Patient at specific location.
     */
    public Patient createPatientAtLocation(double latitude, double longitude) {
        return createPatient("patient" + idCounter++, latitude, longitude);
    }

    /**
     * Creates a CareStaff with specific availability window.
     */
    public CareStaff createCareStaffWithAvailability(AvailabilityWindow window) {
        CareStaff staff = createBasicCareStaff();
        // Set specific availability window
        return staff;
    }

    /**
     * Creates a HomecareVisit with specific required licenses.
     */
    public HomecareVisit createVisitWithRequiredLicenses(List<String> licenses) {
        HomecareVisit visit = createBasicHomecareVisit();
        visit.getComplianceRequirements().setRequiredLicenses(licenses);
        return visit;
    }
}
