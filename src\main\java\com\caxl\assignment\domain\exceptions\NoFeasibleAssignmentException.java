package com.caxl.assignment.domain.exceptions;

/**
 * Exception thrown when no feasible assignment can be found.
 */
public class NoFeasibleAssignmentException extends AssignmentException {
    
    public NoFeasibleAssignmentException(String message) {
        super(message);
    }
    
    public NoFeasibleAssignmentException(String message, Throwable cause) {
        super(message, cause);
    }
}
