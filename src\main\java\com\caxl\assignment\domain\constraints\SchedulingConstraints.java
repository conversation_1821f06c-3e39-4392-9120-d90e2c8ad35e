package com.caxl.assignment.domain.constraints;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintCollectors;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.scheduling.ClinicianShift;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Set;

/**
 * Optimal Timefold constraint provider for clinician scheduling.
 * Implements enterprise-grade constraints using Timefold best practices.
 */
@Component
public class SchedulingConstraints implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory factory) {
        return new Constraint[]{
            // === HARD CONSTRAINTS (Feasibility) ===
            shiftCapacityConstraint(factory),
            clinicianAvailabilityConstraint(factory),
            skillRequirementConstraint(factory),
            workloadLimitConstraint(factory),
            oneShiftPerClinicianPerDayConstraint(factory),
            
            // === SOFT CONSTRAINTS (Optimization) ===
            balanceWorkloadConstraint(factory),
            minimizeTravelTimeConstraint(factory),
            continuityOfCareConstraint(factory),
            preferenceMatchingConstraint(factory),
            maximizeUtilizationConstraint(factory),
            minimizeUnassignedPatientsConstraint(factory)
        };
    }

    // =========================================================================
    // HARD CONSTRAINTS - Feasibility Requirements
    // =========================================================================

    /**
     * Hard constraint: Shift cannot exceed maximum patient capacity.
     */
    private Constraint shiftCapacityConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getPatientCount() > shift.getMaxPatients())
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> shift.getPatientCount() - shift.getMaxPatients())
                .asConstraint("Shift patient capacity exceeded");
    }

    /**
     * Hard constraint: Clinician must be available for assigned shift.
     */
    private Constraint clinicianAvailabilityConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> !isClinicianAvailableForShift(shift))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Clinician not available for shift");
    }

    /**
     * Hard constraint: Clinician must have required skills for all patients.
     */
    private Constraint skillRequirementConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> !hasRequiredSkillsForAllPatients(shift))
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> countMissingSkills(shift))
                .asConstraint("Missing required skills");
    }

    /**
     * Hard constraint: Shift workload cannot exceed clinician's daily limit.
     */
    private Constraint workloadLimitConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> shift.getCurrentWorkload() > 
                        shift.getAssignedClinician().getMaxDailyWorkloadPoints())
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> shift.getCurrentWorkload() - 
                                shift.getAssignedClinician().getMaxDailyWorkloadPoints())
                .asConstraint("Daily workload limit exceeded");
    }

    /**
     * Hard constraint: One shift per clinician per day.
     */
    private Constraint oneShiftPerClinicianPerDayConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .groupBy(ClinicianShift::getAssignedClinician, 
                        ClinicianShift::getShiftDate, 
                        ConstraintCollectors.count())
                .filter((clinician, date, count) -> count > 1)
                .penalize(HardSoftScore.ONE_HARD,
                        (clinician, date, count) -> count - 1)
                .asConstraint("Multiple shifts per clinician per day");
    }

    // =========================================================================
    // SOFT CONSTRAINTS - Optimization Objectives
    // =========================================================================

    /**
     * Soft constraint: Balance workload across clinicians (minimize variance).
     */
    private Constraint balanceWorkloadConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .groupBy(ClinicianShift::getAssignedClinician,
                        ConstraintCollectors.sum(ClinicianShift::getCurrentWorkload))
                .penalize(HardSoftScore.ONE_SOFT,
                        (clinician, totalWorkload) -> totalWorkload * totalWorkload / 100)
                .asConstraint("Balance workload across clinicians");
    }

    /**
     * Soft constraint: Minimize travel time between consecutive visits.
     */
    private Constraint minimizeTravelTimeConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getPatientVisits().size() > 1)
                .penalize(HardSoftScore.ONE_SOFT,
                        shift -> (int) shift.getTotalTravelTime().toMinutes())
                .asConstraint("Minimize travel time");
    }

    /**
     * Soft constraint: Reward continuity of care (same clinician for returning patients).
     */
    private Constraint continuityOfCareConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculateContinuityScore(shift))
                .asConstraint("Continuity of care");
    }

    /**
     * Soft constraint: Reward patient and clinician preference matching.
     */
    private Constraint preferenceMatchingConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculatePreferenceScore(shift))
                .asConstraint("Preference matching");
    }

    /**
     * Soft constraint: Maximize shift utilization.
     */
    private Constraint maximizeUtilizationConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> shift.getCurrentWorkload())
                .asConstraint("Maximize utilization");
    }

    /**
     * Soft constraint: Heavily penalize unassigned patients.
     */
    private Constraint minimizeUnassignedPatientsConstraint(ConstraintFactory factory) {
        return factory.forEach(ClinicianShift.class)
                .filter(shift -> shift.getAssignedClinician() == null && shift.hasPatients())
                .penalize(HardSoftScore.ONE_SOFT,
                        shift -> shift.getPatientCount() * 1000)
                .asConstraint("Minimize unassigned patients");
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    /**
     * Check if clinician is available for the shift time period.
     */
    private boolean isClinicianAvailableForShift(ClinicianShift shift) {
        Clinician clinician = shift.getAssignedClinician();
        LocalDate shiftDate = shift.getShiftDate();
        LocalTime shiftStart = shift.getStartTime();
        LocalTime shiftEnd = shift.getEndTime();

        return clinician.getAvailableSlots().stream()
                .anyMatch(slot -> {
                    return slot.getDate().equals(shiftDate) &&
                           !slot.getStartTime().isAfter(shiftStart) &&
                           !slot.getEndTime().isBefore(shiftEnd);
                });
    }

    /**
     * Check if clinician has all required skills for patients in shift.
     */
    private boolean hasRequiredSkillsForAllPatients(ClinicianShift shift) {
        Set<String> clinicianSkills = Set.copyOf(shift.getAssignedClinician().getSkills());
        
        return shift.getPatientVisits().stream()
                .allMatch(patient -> {
                    Set<String> requiredSkills = Set.copyOf(patient.getRequiredSkills());
                    return clinicianSkills.containsAll(requiredSkills);
                });
    }

    /**
     * Count missing skills for patients in shift.
     */
    private int countMissingSkills(ClinicianShift shift) {
        Set<String> clinicianSkills = Set.copyOf(shift.getAssignedClinician().getSkills());
        
        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    Set<String> requiredSkills = Set.copyOf(patient.getRequiredSkills());
                    return (int) requiredSkills.stream()
                            .filter(skill -> !clinicianSkills.contains(skill))
                            .count();
                })
                .sum();
    }

    /**
     * Calculate continuity of care score for shift.
     */
    private int calculateContinuityScore(ClinicianShift shift) {
        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    if (patient.getPreferences() != null) {
                        // High bonus for preferred clinicians
                        if (patient.getPreferences().getPreferredCarestaffIds()
                                .contains(shift.getAssignedClinician().getId())) {
                            return 20;
                        }
                        // Medium bonus for previous clinicians
                        if (patient.getPreferences().getPreviousCarestaffIds()
                                .contains(shift.getAssignedClinician().getId())) {
                            return 10;
                        }
                    }
                    return 0;
                })
                .sum();
    }

    /**
     * Calculate preference matching score for shift.
     */
    private int calculatePreferenceScore(ClinicianShift shift) {
        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    int score = 0;
                    
                    if (patient.getPreferences() != null) {
                        // Gender preference
                        if (patient.getPreferences().getPreferredCarestaffGender() != null &&
                            patient.getPreferences().getPreferredCarestaffGender()
                                   .equals(shift.getAssignedClinician().getPii().getGender())) {
                            score += 10;
                        }
                        
                        // Language preference
                        if (patient.getPreferences().getPreferredLanguages().stream()
                                .anyMatch(lang -> shift.getAssignedClinician().getLanguages().contains(lang))) {
                            score += 8;
                        }
                    }
                    
                    return score;
                })
                .sum();
    }
}
