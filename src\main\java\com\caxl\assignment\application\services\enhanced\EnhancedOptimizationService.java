package com.caxl.assignment.application.services.enhanced;

import ai.timefold.solver.core.api.solver.Solver;
import ai.timefold.solver.core.api.solver.SolverFactory;
import com.caxl.assignment.domain.exceptions.NoFeasibleAssignmentException;
import com.caxl.assignment.domain.models.Assignment;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.Rule;
import com.caxl.assignment.application.ports.RuleRepository;
import com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.EnhancedAssignmentSolution;
import com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.Shift;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Enhanced optimization service using advanced Timefold features for clinician scheduling.
 * Features:
 * - Shift-based scheduling with visit sequencing
 * - Multi-day optimization
 * - Advanced constraint modeling
 * - Workload balancing and fairness
 * - Travel time optimization
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EnhancedOptimizationService {

    private final SolverFactory<EnhancedAssignmentSolution> enhancedSolverFactory;
    private final RuleRepository ruleRepository;

    /**
     * Create optimal shift-based assignments for multiple days.
     *
     * @param patients List of patients to be assigned
     * @param clinicians List of available clinicians
     * @param schedulingPeriod List of dates to schedule
     * @param shiftTemplates Predefined shift templates
     * @return Enhanced assignment solution
     */
    public EnhancedAssignmentSolution createShiftBasedAssignments(
            List<Patient> patients,
            List<Clinician> clinicians,
            List<LocalDate> schedulingPeriod,
            List<ShiftTemplate> shiftTemplates) {

        // Create initial solution with shift-based structure
        EnhancedAssignmentSolution initialSolution = createInitialShiftSolution(
                patients, clinicians, schedulingPeriod, shiftTemplates);

        // Solve using enhanced Timefold configuration
        Solver<EnhancedAssignmentSolution> solver = enhancedSolverFactory.buildSolver();
        EnhancedAssignmentSolution solvedSolution = solver.solve(initialSolution);

        // Validate and log results
        validateAndLogSolution(solvedSolution, patients.size());

        return solvedSolution;
    }

    /**
     * Create assignments for a single day with advanced optimization.
     */
    public List<Assignment> createDailyAssignments(
            List<Patient> patients,
            List<Clinician> clinicians,
            LocalDate date) {

        List<LocalDate> singleDay = List.of(date);
        List<ShiftTemplate> defaultTemplates = createDefaultShiftTemplates();

        EnhancedAssignmentSolution solution = createShiftBasedAssignments(
                patients, clinicians, singleDay, defaultTemplates);

        return convertSolutionToAssignments(solution);
    }

    /**
     * Create initial solution with shift structure.
     */
    private EnhancedAssignmentSolution createInitialShiftSolution(
            List<Patient> patients,
            List<Clinician> clinicians,
            List<LocalDate> schedulingPeriod,
            List<ShiftTemplate> shiftTemplates) {

        // Generate shifts for all dates and templates
        List<Shift> shifts = generateShifts(schedulingPeriod, shiftTemplates, clinicians);

        // Create skill hierarchy for advanced matching
        Map<String, Set<String>> skillHierarchy = buildSkillHierarchy();

        // Create distance matrix for travel optimization
        Map<String, Map<String, Double>> distanceMatrix = buildDistanceMatrix(patients);

        // Create workload targets for fairness
        Map<String, EnhancedAssignmentSolution.WorkloadTarget> workloadTargets = 
                buildWorkloadTargets(clinicians);

        return EnhancedAssignmentSolution.builder()
                .shifts(shifts)
                .clinicians(clinicians)
                .patients(patients)
                .rules(ruleRepository.getAllRules())
                .skillHierarchy(skillHierarchy)
                .distanceMatrix(distanceMatrix)
                .clinicianWorkloadTargets(workloadTargets)
                .schedulingPeriod(schedulingPeriod)
                .build();
    }

    /**
     * Generate shifts based on templates and scheduling period.
     */
    private List<Shift> generateShifts(
            List<LocalDate> schedulingPeriod,
            List<ShiftTemplate> shiftTemplates,
            List<Clinician> clinicians) {

        List<Shift> shifts = new ArrayList<>();
        int shiftIdCounter = 1;

        for (LocalDate date : schedulingPeriod) {
            for (ShiftTemplate template : shiftTemplates) {
                // Create multiple shifts of same type if needed
                int shiftsNeeded = calculateShiftsNeeded(template, clinicians.size());
                
                for (int i = 0; i < shiftsNeeded; i++) {
                    Shift shift = Shift.builder()
                            .id("shift_" + shiftIdCounter++)
                            .shiftDate(date)
                            .shiftStartTime(template.getStartTime())
                            .shiftEndTime(template.getEndTime())
                            .shiftType(template.getShiftType())
                            .maxPatients(template.getMaxPatients())
                            .maxWorkloadPoints(template.getMaxWorkloadPoints())
                            .serviceArea(template.getServiceArea())
                            .requiredSkills(template.getRequiredSkills())
                            .patientVisits(new ArrayList<>())
                            .build();
                    
                    shifts.add(shift);
                }
            }
        }

        return shifts;
    }

    /**
     * Calculate number of shifts needed based on template and available clinicians.
     */
    private int calculateShiftsNeeded(ShiftTemplate template, int clinicianCount) {
        // Simple heuristic: create enough shifts to accommodate all clinicians
        return Math.min(clinicianCount / 2, 5); // Max 5 shifts per type per day
    }

    /**
     * Build skill hierarchy for advanced skill matching.
     */
    private Map<String, Set<String>> buildSkillHierarchy() {
        Map<String, Set<String>> hierarchy = new HashMap<>();
        
        // Example skill hierarchies (would be loaded from configuration)
        hierarchy.put("nursing", Set.of("basic_care", "medication_admin"));
        hierarchy.put("advanced_nursing", Set.of("nursing", "wound_care", "iv_therapy"));
        hierarchy.put("physiotherapy", Set.of("mobility_assessment", "exercise_therapy"));
        hierarchy.put("occupational_therapy", Set.of("daily_living_assessment", "adaptive_equipment"));
        
        return hierarchy;
    }

    /**
     * Build distance matrix for travel time optimization.
     */
    private Map<String, Map<String, Double>> buildDistanceMatrix(List<Patient> patients) {
        Map<String, Map<String, Double>> matrix = new HashMap<>();
        
        // Simplified distance calculation based on postal codes
        for (Patient from : patients) {
            String fromPostal = from.getLocation().getPostalCode();
            Map<String, Double> distances = new HashMap<>();
            
            for (Patient to : patients) {
                String toPostal = to.getLocation().getPostalCode();
                double distance = calculateDistance(fromPostal, toPostal);
                distances.put(toPostal, distance);
            }
            
            matrix.put(fromPostal, distances);
        }
        
        return matrix;
    }

    /**
     * Calculate distance between postal codes (simplified).
     */
    private double calculateDistance(String fromPostal, String toPostal) {
        if (fromPostal.equals(toPostal)) return 0.0;
        if (fromPostal.substring(0, 3).equals(toPostal.substring(0, 3))) return 5.0;
        return 15.0; // Different regions
    }

    /**
     * Build workload targets for fairness constraints.
     */
    private Map<String, EnhancedAssignmentSolution.WorkloadTarget> buildWorkloadTargets(
            List<Clinician> clinicians) {
        
        Map<String, EnhancedAssignmentSolution.WorkloadTarget> targets = new HashMap<>();
        
        for (Clinician clinician : clinicians) {
            EnhancedAssignmentSolution.WorkloadTarget target = 
                    EnhancedAssignmentSolution.WorkloadTarget.builder()
                            .clinicianId(clinician.getId())
                            .targetDailyWorkload(clinician.getMaxDailyWorkloadPoints() * 80 / 100) // 80% target
                            .targetWeeklyWorkload(clinician.getMaxWeeklyWorkloadPoints() * 80 / 100)
                            .minDailyWorkload(clinician.getMaxDailyWorkloadPoints() * 50 / 100) // 50% minimum
                            .maxDailyWorkload(clinician.getMaxDailyWorkloadPoints())
                            .fairnessWeight(1.0)
                            .build();
            
            targets.put(clinician.getId(), target);
        }
        
        return targets;
    }

    /**
     * Create default shift templates.
     */
    private List<ShiftTemplate> createDefaultShiftTemplates() {
        return List.of(
                ShiftTemplate.builder()
                        .shiftType(Shift.ShiftType.MORNING)
                        .startTime(LocalTime.of(8, 0))
                        .endTime(LocalTime.of(16, 0))
                        .maxPatients(6)
                        .maxWorkloadPoints(80)
                        .serviceArea("general")
                        .requiredSkills(List.of())
                        .build(),
                ShiftTemplate.builder()
                        .shiftType(Shift.ShiftType.AFTERNOON)
                        .startTime(LocalTime.of(14, 0))
                        .endTime(LocalTime.of(22, 0))
                        .maxPatients(5)
                        .maxWorkloadPoints(70)
                        .serviceArea("general")
                        .requiredSkills(List.of())
                        .build()
        );
    }

    /**
     * Convert solution to assignment list.
     */
    private List<Assignment> convertSolutionToAssignments(EnhancedAssignmentSolution solution) {
        List<Assignment> assignments = new ArrayList<>();
        
        for (Shift shift : solution.getShifts()) {
            if (shift.getAssignedClinician() != null && !shift.getPatientVisits().isEmpty()) {
                for (Patient patient : shift.getPatientVisits()) {
                    Assignment assignment = createAssignmentFromShift(shift, patient);
                    assignments.add(assignment);
                }
            }
        }
        
        return assignments;
    }

    /**
     * Create assignment from shift and patient.
     */
    private Assignment createAssignmentFromShift(Shift shift, Patient patient) {
        // Find appropriate time slot for patient
        Patient.TimeSlot timeSlot = patient.getRequiredSlots().isEmpty() 
                ? createDefaultTimeSlot(shift) 
                : patient.getRequiredSlots().get(0);

        return Assignment.builder()
                .id("assignment_" + shift.getId() + "_" + patient.getId())
                .patientId(patient.getId())
                .clinicianId(shift.getAssignedClinician().getId())
                .visitDate(shift.getShiftDate().toString())
                .startTime(timeSlot.getStartTime().toString())
                .endTime(timeSlot.getEndTime().toString())
                .status(Assignment.AssignmentStatus.PENDING)
                .workloadPoints(patient.getWorkloadPoints())
                .build();
    }

    /**
     * Create default time slot within shift hours.
     */
    private Patient.TimeSlot createDefaultTimeSlot(Shift shift) {
        return Patient.TimeSlot.builder()
                .date(shift.getShiftDate())
                .startTime(shift.getShiftStartTime())
                .endTime(shift.getShiftStartTime().plusHours(1))
                .build();
    }

    /**
     * Validate and log solution results.
     */
    private void validateAndLogSolution(EnhancedAssignmentSolution solution, int totalPatients) {
        if (!solution.isFeasible()) {
            throw new NoFeasibleAssignmentException(
                    "No feasible shift-based assignments found. " +
                    "Assigned " + solution.getAssignedPatients().size() + " of " + totalPatients + " patients.");
        }

        log.info("Enhanced optimization completed successfully:");
        log.info("- Total patients: {}", totalPatients);
        log.info("- Assigned patients: {}", solution.getAssignedPatients().size());
        log.info("- Assignment rate: {:.2f}%", solution.getAssignmentRate() * 100);
        log.info("- Total shifts: {}", solution.getShifts().size());
        log.info("- Assigned shifts: {}", solution.getAssignedShiftCount());
        log.info("- Final score: {}", solution.getScore());
        log.info("- Workload fairness: {:.2f}", solution.getWorkloadFairnessScore());
    }

    /**
     * Shift template for generating shifts.
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ShiftTemplate {
        private Shift.ShiftType shiftType;
        private LocalTime startTime;
        private LocalTime endTime;
        private int maxPatients;
        private int maxWorkloadPoints;
        private String serviceArea;
        private List<String> requiredSkills;
    }
}
