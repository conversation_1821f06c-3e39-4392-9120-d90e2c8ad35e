package com.caxl.assignment.domain.valueobjects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * Value object representing matching criteria configuration parsed from JSON.
 * Framework-agnostic domain model for constraint configuration.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MatchingCriteria {

    @JsonProperty("filters")
    private Filters filters;

    @JsonProperty("scoring_weights")
    private ScoringWeights scoringWeights;

    @JsonProperty("geography")
    private Geography geography;

    @JsonProperty("availability")
    private Availability availability;

    /**
     * Hard constraint filters that must be satisfied.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Filters {

        @JsonProperty("requiredSkillsMustMatchAll")
        @Builder.Default
        private boolean requiredSkillsMustMatchAll = false;

        @JsonProperty("proximitySearchRadiusKm")
        @Builder.Default
        private double proximitySearchRadiusKm = 50.0;

        @JsonProperty("mustRespectGeoServiceArea")
        @Builder.Default
        private boolean mustRespectGeoServiceArea = true;

        @JsonProperty("mustRespectAvailability")
        @Builder.Default
        private boolean mustRespectAvailability = true;

        @JsonProperty("mustNotBeBarred")
        @Builder.Default
        private boolean mustNotBeBarred = true;

        @JsonProperty("minScoreThreshold")
        @Builder.Default
        private double minScoreThreshold = 0.0;
    }

    /**
     * Soft constraint scoring weights for optimization.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ScoringWeights {

        @JsonProperty("skillMatchBonusPerRequiredSkill")
        @Builder.Default
        private double skillMatchBonusPerRequiredSkill = 10.0;

        @JsonProperty("skillMatchAllBonus")
        @Builder.Default
        private double skillMatchAllBonus = 50.0;

        @JsonProperty("proximityKmPenalty")
        @Builder.Default
        private double proximityKmPenalty = -1.0;

        @JsonProperty("geoServiceAreaBonus")
        @Builder.Default
        private double geoServiceAreaBonus = 20.0;

        @JsonProperty("availabilityWindowFitPenaltyPerMinuteDeviation")
        @Builder.Default
        private double availabilityWindowFitPenaltyPerMinuteDeviation = -0.5;

        @JsonProperty("preferredCareStaffBonus")
        @Builder.Default
        private double preferredCareStaffBonus = 15.0;

        @JsonProperty("continuityBonusPerRecentVisit")
        @Builder.Default
        private double continuityBonusPerRecentVisit = 5.0;

        @JsonProperty("languageMatchBonus")
        @Builder.Default
        private double languageMatchBonus = 8.0;

        @JsonProperty("experienceLevelBonusPerYear")
        @Builder.Default
        private double experienceLevelBonusPerYear = 2.0;
    }

    /**
     * Geographic constraint configuration.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Geography {

        @JsonProperty("staffServiceGeofenceTypes")
        @Builder.Default
        private List<String> staffServiceGeofenceTypes = List.of("service_area", "county_area");

        @JsonProperty("geofenceStrictContainmentOnly")
        @Builder.Default
        private boolean geofenceStrictContainmentOnly = true;
    }

    /**
     * Availability constraint configuration.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Availability {

        @JsonProperty("overlapThresholdMinutes")
        @Builder.Default
        private int overlapThresholdMinutes = 1;

        @JsonProperty("minTimeBeforeVisitMinutes")
        @Builder.Default
        private int minTimeBeforeVisitMinutes = 30;

        @JsonProperty("minTimeAfterVisitMinutes")
        @Builder.Default
        private int minTimeAfterVisitMinutes = 30;
    }
}
