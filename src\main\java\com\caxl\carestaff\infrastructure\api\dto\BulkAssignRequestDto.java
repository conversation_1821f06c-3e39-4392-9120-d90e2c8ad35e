package com.caxl.carestaff.infrastructure.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

/**
 * DTO for bulk assignment requests.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkAssignRequestDto {

    @NotEmpty(message = "Suggestion IDs cannot be empty")
    @JsonProperty("suggestion_ids")
    private List<UUID> suggestionIds;

    @JsonProperty("scheduler_id")
    private UUID schedulerId;
}
