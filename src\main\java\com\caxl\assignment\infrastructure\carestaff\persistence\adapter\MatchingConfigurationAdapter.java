package com.caxl.assignment.infrastructure.carestaff.persistence.adapter;

import com.caxl.assignment.application.carestaff.port.out.persistence.MatchingConfigurationPort;
import com.caxl.assignment.domain.carestaff.valueobjects.MatchingCriteria;
import com.caxl.assignment.infrastructure.carestaff.persistence.entity.MatchingConfigurationEntity;
import com.caxl.assignment.infrastructure.carestaff.persistence.repository.MatchingConfigurationRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Persistence adapter for matching configuration operations.
 * Implements the MatchingConfigurationPort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class MatchingConfigurationAdapter implements MatchingConfigurationPort {

    private final MatchingConfigurationRepository repository;
    private final ObjectMapper objectMapper;

    @Override
    public Optional<MatchingCriteria> findActiveConfiguration() {
        log.debug("Finding active matching configuration");
        
        return repository.findActiveConfiguration()
                .map(this::mapToDomain);
    }

    @Override
    public Optional<MatchingCriteria> findById(UUID configId) {
        log.debug("Finding matching configuration by ID: {}", configId);
        
        return repository.findById(configId)
                .map(this::mapToDomain);
    }

    /**
     * Map JPA entity to domain value object.
     */
    private MatchingCriteria mapToDomain(MatchingConfigurationEntity entity) {
        try {
            return objectMapper.readValue(entity.getCriteriaJson(), MatchingCriteria.class);
        } catch (Exception e) {
            log.error("Error parsing matching criteria JSON for config {}: {}", 
                     entity.getConfigId(), e.getMessage());
            // Return default criteria if parsing fails
            return MatchingCriteria.builder()
                    .filters(MatchingCriteria.Filters.builder().build())
                    .scoringWeights(MatchingCriteria.ScoringWeights.builder().build())
                    .geography(MatchingCriteria.Geography.builder().build())
                    .availability(MatchingCriteria.Availability.builder().build())
                    .build();
        }
    }
}
