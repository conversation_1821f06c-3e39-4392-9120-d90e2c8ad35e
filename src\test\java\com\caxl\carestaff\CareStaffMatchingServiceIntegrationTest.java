package com.caxl.carestaff;

import com.caxl.carestaff.application.port.in.*;
import com.caxl.carestaff.domain.entities.MatchSuggestion;
import com.caxl.carestaff.domain.services.*;
import com.caxl.carestaff.infrastructure.persistence.entity.MatchingConfigurationEntity;
import com.caxl.carestaff.infrastructure.persistence.entity.ServiceRequestEntity;
import com.caxl.carestaff.infrastructure.persistence.repository.MatchingConfigurationRepository;
import com.caxl.carestaff.infrastructure.persistence.repository.ServiceRequestRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the CareStaff Matching Service.
 * Tests the complete flow from API to database using the hexagonal architecture.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@Slf4j
class CareStaffMatchingServiceIntegrationTest {

    @Autowired
    private SuggestMatchesUseCase suggestMatchesUseCase;

    @Autowired
    private OverrideMatchUseCase overrideMatchUseCase;

    @Autowired
    private AssignBulkMatchesUseCase assignBulkMatchesUseCase;

    @Autowired
    private RetrieveSuggestionsUseCase retrieveSuggestionsUseCase;

    @Autowired
    private SkillExtractionService skillExtractionService;

    @Autowired
    private ContinuityOfCareService continuityOfCareService;

    @Autowired
    private ConflictDetectionService conflictDetectionService;

    @Autowired
    private MatchingConfigurationRepository matchingConfigRepository;

    @Autowired
    private ServiceRequestRepository serviceRequestRepository;

    @Test
    void testSuggestMatches_WithValidConfiguration_ShouldReturnSuggestions() {
        // Given: A valid matching configuration
        String criteriaJson = """
            {
              "filters": {
                "requiredSkillsMustMatchAll": false,
                "proximitySearchRadiusKm": 100.0,
                "mustRespectGeoServiceArea": false,
                "mustRespectAvailability": false,
                "mustNotBeBarred": false,
                "minScoreThreshold": 0.0
              },
              "scoring_weights": {
                "skillMatchBonusPerRequiredSkill": 10.0,
                "skillMatchAllBonus": 50.0,
                "proximityKmPenalty": -1.0,
                "geoServiceAreaBonus": 20.0,
                "preferredCareStaffBonus": 15.0,
                "continuityBonusPerRecentVisit": 5.0,
                "languageMatchBonus": 8.0,
                "experienceLevelBonusPerYear": 2.0
              },
              "geography": {
                "staffServiceGeofenceTypes": ["service_area"],
                "geofenceStrictContainmentOnly": false
              },
              "availability": {
                "overlapThresholdMinutes": 1,
                "minTimeBeforeVisitMinutes": 30,
                "minTimeAfterVisitMinutes": 30
              }
            }
            """;

        MatchingConfigurationEntity config = MatchingConfigurationEntity.builder()
                .configId(UUID.randomUUID())
                .configName("test_config")
                .criteriaJson(criteriaJson)
                .isActive(true)
                .createdBy("test")
                .build();

        matchingConfigRepository.save(config);

        // And: A valid service request
        ServiceRequestEntity serviceRequest = ServiceRequestEntity.builder()
                .requestId(UUID.randomUUID())
                .patientId(UUID.randomUUID())
                .status("pending")
                .requiredSkillIds(List.of(UUID.randomUUID()))
                .arrivalWindowStart(LocalDateTime.now().plusDays(1))
                .arrivalWindowEnd(LocalDateTime.now().plusDays(1).plusHours(8))
                .visitDurationMinutes(60)
                .visitType("test_visit")
                .priority(3)
                .workloadPoints(2)
                .build();

        serviceRequestRepository.save(serviceRequest);

        // When: Suggesting matches
        List<MatchSuggestion> suggestions = suggestMatchesUseCase.suggestMatches(serviceRequest.getRequestId());

        // Then: The system should handle the request gracefully
        assertNotNull(suggestions);
        // Note: Suggestions may be empty if no matching carestaff exist in test data
        // The important thing is that the system doesn't throw exceptions
    }

    @Test
    void testSuggestMatches_WithNoActiveConfiguration_ShouldThrowException() {
        // Given: No active configuration exists
        matchingConfigRepository.deleteAll();

        // And: A valid service request
        ServiceRequestEntity serviceRequest = ServiceRequestEntity.builder()
                .requestId(UUID.randomUUID())
                .patientId(UUID.randomUUID())
                .status("pending")
                .requiredSkillIds(List.of(UUID.randomUUID()))
                .arrivalWindowStart(LocalDateTime.now().plusDays(1))
                .arrivalWindowEnd(LocalDateTime.now().plusDays(1).plusHours(8))
                .visitDurationMinutes(60)
                .visitType("test_visit")
                .build();

        serviceRequestRepository.save(serviceRequest);

        // When & Then: Suggesting matches should throw ConfigurationException
        assertThrows(Exception.class, () -> {
            suggestMatchesUseCase.suggestMatches(serviceRequest.getRequestId());
        });
    }

    @Test
    void testSuggestMatches_WithInvalidRequestId_ShouldThrowException() {
        // Given: A valid configuration exists (from sample data initializer)
        // And: An invalid request ID
        UUID invalidRequestId = UUID.randomUUID();

        // When & Then: Suggesting matches should throw ResourceNotFoundException
        assertThrows(Exception.class, () -> {
            suggestMatchesUseCase.suggestMatches(invalidRequestId);
        });
    }

    @Test
    void testSkillExtractionService_ShouldExtractSkillsFromVisitType() {
        // Given: A service request with wound care visit type
        ServiceRequestEntity serviceRequest = ServiceRequestEntity.builder()
                .requestId(UUID.randomUUID())
                .patientId(UUID.randomUUID())
                .status("pending")
                .visitType("wound_care")
                .priority(2)
                .workloadPoints(3)
                .build();

        // And: A patient with diabetes condition
        var patient = new com.caxl.carestaff.application.port.out.persistence.PatientPort.PatientDomain(
                UUID.randomUUID(),
                "Test Patient",
                null, // coordinates
                "English",
                List.of(),
                List.of(),
                "diabetes, wound care needed",
                "Special care instructions"
        );

        // When: Extracting skills
        var serviceRequestDomain = mapToServiceRequestDomain(serviceRequest);
        List<UUID> extractedSkills = skillExtractionService.extractRequiredSkills(serviceRequestDomain, patient);

        // Then: Should extract skills for both wound care and diabetes
        assertNotNull(extractedSkills);
        assertTrue(extractedSkills.size() > 0);
        log.info("Extracted {} skills for wound care + diabetes case", extractedSkills.size());
    }

    @Test
    void testContinuityOfCareService_ShouldCalculateScore() {
        // Given: A care staff and patient
        UUID careStaffId = UUID.randomUUID();
        UUID patientId = UUID.randomUUID();

        var patient = new com.caxl.carestaff.application.port.out.persistence.PatientPort.PatientDomain(
                patientId,
                "Test Patient",
                null,
                "English",
                List.of(careStaffId), // Preferred staff
                List.of(),
                "Regular patient",
                ""
        );

        // When: Calculating continuity score
        ContinuityOfCareService.ContinuityScore score =
                continuityOfCareService.calculateContinuityScore(careStaffId, patientId, patient, 30);

        // Then: Should return a valid score
        assertNotNull(score);
        assertTrue(score.score() >= 0);
        assertNotNull(score.rationale());
        log.info("Continuity score: {} - {}", score.score(), score.rationale());
    }

    // Helper method to convert JPA entity to domain entity
    private com.caxl.carestaff.domain.entities.ServiceRequest mapToServiceRequestDomain(ServiceRequestEntity entity) {
        var timeWindow = com.caxl.carestaff.domain.entities.ServiceRequest.TimeWindow.builder()
                .arrivalWindowStart(LocalDateTime.now().plusDays(1))
                .arrivalWindowEnd(LocalDateTime.now().plusDays(1).plusHours(8))
                .visitDurationMinutes(60)
                .build();

        return com.caxl.carestaff.domain.entities.ServiceRequest.builder()
                .requestId(entity.getRequestId())
                .patientId(entity.getPatientId())
                .status(entity.getStatus())
                .requiredSkillIds(entity.getRequiredSkillIds())
                .requiredCertificationIds(entity.getRequiredCertificationIds())
                .timeWindow(timeWindow)
                .visitType(entity.getVisitType())
                .priority(entity.getPriority())
                .workloadPoints(entity.getWorkloadPoints())
                .specialInstructions(entity.getSpecialInstructions())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}
