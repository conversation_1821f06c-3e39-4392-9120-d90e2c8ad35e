package com.caxl.assignment.infrastructure.optaplanner.domain.enhanced;

import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty;
import ai.timefold.solver.core.api.domain.solution.PlanningScore;
import ai.timefold.solver.core.api.domain.solution.PlanningSolution;
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.Rule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Enhanced planning solution that uses shift-based scheduling with advanced Timefold features.
 * This solution supports:
 * - Multi-day scheduling
 * - Shift-based planning with visit sequencing
 * - Advanced constraint modeling
 * - Fairness and workload balancing
 */
@PlanningSolution
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EnhancedAssignmentSolution {

    /**
     * List of shifts to be optimized (planning entities).
     */
    @PlanningEntityCollectionProperty
    private List<Shift> shifts;

    /**
     * Available clinicians (value range for shift assignment).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clinicianRange")
    private List<Clinician> clinicians;

    /**
     * Patients to be assigned to shifts (value range for visit sequencing).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "patientRange")
    private List<Patient> patients;

    /**
     * Business rules for constraint evaluation.
     */
    @ProblemFactCollectionProperty
    private List<Rule> rules;

    /**
     * Skill requirements matrix for advanced constraint modeling.
     */
    @ProblemFactCollectionProperty
    private Map<String, Set<String>> skillHierarchy;

    /**
     * Geographic distance matrix for travel time optimization.
     */
    @ProblemFactCollectionProperty
    private Map<String, Map<String, Double>> distanceMatrix;

    /**
     * Historical assignment data for continuity of care.
     */
    @ProblemFactCollectionProperty
    private Map<String, List<String>> patientClinicianHistory;

    /**
     * Clinician workload targets for fairness.
     */
    @ProblemFactCollectionProperty
    private Map<String, WorkloadTarget> clinicianWorkloadTargets;

    /**
     * Planning score calculated by Timefold.
     */
    @PlanningScore
    private HardSoftScore score;

    /**
     * Scheduling period (multiple days).
     */
    private List<LocalDate> schedulingPeriod;

    /**
     * Maximum travel time between consecutive visits (minutes).
     */
    @Builder.Default
    private int maxTravelTimeMinutes = 45;

    /**
     * Get all assigned patients across all shifts.
     */
    public List<Patient> getAssignedPatients() {
        return shifts.stream()
                .flatMap(shift -> shift.getPatientVisits().stream())
                .collect(Collectors.toList());
    }

    /**
     * Get all unassigned patients.
     */
    public List<Patient> getUnassignedPatients() {
        Set<Patient> assignedPatients = Set.copyOf(getAssignedPatients());
        return patients.stream()
                .filter(patient -> !assignedPatients.contains(patient))
                .collect(Collectors.toList());
    }

    /**
     * Calculate assignment completion rate.
     */
    public double getAssignmentRate() {
        if (patients.isEmpty()) {
            return 1.0;
        }
        return (double) getAssignedPatients().size() / patients.size();
    }

    /**
     * Get total workload points assigned.
     */
    public int getTotalAssignedWorkload() {
        return shifts.stream()
                .mapToInt(Shift::getTotalWorkloadPoints)
                .sum();
    }

    /**
     * Calculate workload distribution fairness (lower is better).
     */
    public double getWorkloadFairnessScore() {
        Map<Clinician, Integer> clinicianWorkloads = shifts.stream()
                .filter(shift -> shift.getAssignedClinician() != null)
                .collect(Collectors.groupingBy(
                    Shift::getAssignedClinician,
                    Collectors.summingInt(Shift::getTotalWorkloadPoints)
                ));

        if (clinicianWorkloads.size() < 2) {
            return 0.0;
        }

        double mean = clinicianWorkloads.values().stream()
                .mapToInt(Integer::intValue)
                .average()
                .orElse(0.0);

        double variance = clinicianWorkloads.values().stream()
                .mapToDouble(workload -> Math.pow(workload - mean, 2))
                .average()
                .orElse(0.0);

        return Math.sqrt(variance);
    }

    /**
     * Check if the solution is feasible (no hard constraint violations).
     */
    public boolean isFeasible() {
        return score != null && score.hardScore() >= 0;
    }

    /**
     * Get the number of shifts with assigned clinicians.
     */
    public long getAssignedShiftCount() {
        return shifts.stream()
                .filter(shift -> shift.getAssignedClinician() != null)
                .count();
    }

    /**
     * Get shifts for a specific date.
     */
    public List<Shift> getShiftsForDate(LocalDate date) {
        return shifts.stream()
                .filter(shift -> shift.getShiftDate().equals(date))
                .collect(Collectors.toList());
    }

    /**
     * Get shifts assigned to a specific clinician.
     */
    public List<Shift> getShiftsForClinician(Clinician clinician) {
        return shifts.stream()
                .filter(shift -> clinician.equals(shift.getAssignedClinician()))
                .collect(Collectors.toList());
    }

    /**
     * Workload target for fairness constraints.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkloadTarget {
        private String clinicianId;
        private int targetDailyWorkload;
        private int targetWeeklyWorkload;
        private int minDailyWorkload;
        private int maxDailyWorkload;
        private double fairnessWeight;
    }
}
