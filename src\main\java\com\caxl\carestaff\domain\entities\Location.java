package com.caxl.carestaff.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.UUID;

/**
 * Domain entity representing a geographic location.
 * Framework-agnostic domain model for location data.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Location {
    
    private UUID locationId;
    private String locationType;
    private String address;
    private String city;
    private String state;
    private String country;
    private String postalCode;
    private Point coordinates;
    private Polygon geofenceBoundary;
    private String description;
    private boolean isActive;
    
    /**
     * Check if this location contains the given point within its geofence boundary.
     */
    public boolean containsPoint(Point point) {
        return geofenceBoundary != null && geofenceBoundary.contains(point);
    }
    
    /**
     * Calculate distance to another location in kilometers.
     */
    public double distanceTo(Location other) {
        if (coordinates == null || other.getCoordinates() == null) {
            return Double.MAX_VALUE;
        }
        // Distance in degrees, approximate conversion to kilometers
        return coordinates.distance(other.getCoordinates()) * 111.0;
    }
    
    /**
     * Calculate distance to a point in kilometers.
     */
    public double distanceTo(Point point) {
        if (coordinates == null || point == null) {
            return Double.MAX_VALUE;
        }
        // Distance in degrees, approximate conversion to kilometers
        return coordinates.distance(point) * 111.0;
    }
}
