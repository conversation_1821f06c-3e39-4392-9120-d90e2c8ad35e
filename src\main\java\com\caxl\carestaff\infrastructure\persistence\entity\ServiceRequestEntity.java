package com.caxl.carestaff.infrastructure.persistence.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * JPA entity for service request storage.
 * Maps to SERVICE_REQUEST table.
 */
@Entity
@Table(name = "service_request")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceRequestEntity {

    @Id
    @Column(name = "request_id")
    private UUID requestId;

    @Column(name = "patient_id", nullable = false)
    private UUID patientId;

    @Column(name = "status", nullable = false)
    @Builder.Default
    private String status = "pending";

    @Type(JsonType.class)
    @Column(name = "required_skill_ids", columnDefinition = "jsonb")
    private List<UUID> requiredSkillIds;

    @Type(JsonType.class)
    @Column(name = "required_certification_ids", columnDefinition = "jsonb")
    private List<UUID> requiredCertificationIds;

    @Column(name = "arrival_window_start")
    private LocalDateTime arrivalWindowStart;

    @Column(name = "arrival_window_end")
    private LocalDateTime arrivalWindowEnd;

    @Column(name = "visit_duration_minutes")
    private Integer visitDurationMinutes;

    @Column(name = "preferred_start_time")
    private LocalDateTime preferredStartTime;

    @Column(name = "latest_end_time")
    private LocalDateTime latestEndTime;

    @Column(name = "visit_type")
    private String visitType;

    @Column(name = "priority")
    @Builder.Default
    private Integer priority = 3;

    @Column(name = "workload_points")
    @Builder.Default
    private Integer workloadPoints = 1;

    @Column(name = "special_instructions", columnDefinition = "TEXT")
    private String specialInstructions;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        if (requestId == null) {
            requestId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
