package com.caxl.carestaff.domain.services;

import com.caxl.carestaff.application.port.out.persistence.PatientPort;
import com.caxl.carestaff.domain.entities.ServiceRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Domain service for extracting required skills from patient conditions and visit types.
 * Implements sophisticated business rules for skill derivation.
 */
@Service
@Slf4j
public class SkillExtractionService {

    // Medical condition to skill mappings
    private static final Map<String, List<UUID>> MEDICAL_CONDITION_SKILLS = Map.of(
            "diabetes", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Blood glucose monitoring
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Insulin administration
            ),
            "wound_care", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Sterile technique
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Wound assessment
            ),
            "cardiac", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Cardiac monitoring
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Medication management
            ),
            "respiratory", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Oxygen therapy
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Respiratory assessment
            ),
            "mobility_assistance", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Physical therapy
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Transfer techniques
            )
    );

    // Visit type to skill mappings
    private static final Map<String, List<UUID>> VISIT_TYPE_SKILLS = Map.of(
            "routine_checkup", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Vital signs
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Basic assessment
            ),
            "wound_care", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Sterile technique
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Wound assessment
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Dressing changes
            ),
            "medication_management", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Medication management
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Medication reconciliation
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Patient education
            ),
            "post_surgical", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Sterile technique
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Wound assessment
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Post-op monitoring
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Pain management
            ),
            "physical_therapy", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Physical therapy
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Transfer techniques
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Exercise therapy
            ),
            "mental_health", List.of(
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Mental health assessment
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Crisis intervention
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Therapeutic communication
            )
    );

    // Priority-based skill requirements
    private static final Map<Integer, List<UUID>> PRIORITY_SKILLS = Map.of(
            1, List.of( // Critical priority - requires advanced skills
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Advanced life support
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Emergency response
            ),
            2, List.of( // High priority - requires specialized skills
                    UUID.fromString("550e8400-e29b-41d4-a716-************"), // Specialized care
                    UUID.fromString("550e8400-e29b-41d4-a716-************")  // Complex procedures
            )
    );

    /**
     * Extract comprehensive skill requirements from service request and patient profile.
     */
    public List<UUID> extractRequiredSkills(ServiceRequest serviceRequest, PatientPort.PatientDomain patient) {
        Set<UUID> allRequiredSkills = new HashSet<>();
        
        log.debug("Extracting skills for request {} with visit type: {}", 
                 serviceRequest.getRequestId(), serviceRequest.getVisitType());

        // 1. Add explicitly specified skills from service request
        if (serviceRequest.getRequiredSkillIds() != null) {
            allRequiredSkills.addAll(serviceRequest.getRequiredSkillIds());
            log.debug("Added {} explicit skills from service request", serviceRequest.getRequiredSkillIds().size());
        }

        // 2. Derive skills from visit type
        if (serviceRequest.getVisitType() != null) {
            List<UUID> visitTypeSkills = deriveSkillsFromVisitType(serviceRequest.getVisitType());
            allRequiredSkills.addAll(visitTypeSkills);
            log.debug("Added {} skills from visit type: {}", visitTypeSkills.size(), serviceRequest.getVisitType());
        }

        // 3. Derive skills from patient medical conditions
        if (patient.medicalConditions() != null) {
            List<UUID> conditionSkills = deriveSkillsFromMedicalConditions(patient.medicalConditions());
            allRequiredSkills.addAll(conditionSkills);
            log.debug("Added {} skills from medical conditions", conditionSkills.size());
        }

        // 4. Add priority-based skills for high-priority requests
        if (serviceRequest.getPriority() <= 2) {
            List<UUID> prioritySkills = derivePrioritySkills(serviceRequest.getPriority());
            allRequiredSkills.addAll(prioritySkills);
            log.debug("Added {} priority-based skills for priority {}", prioritySkills.size(), serviceRequest.getPriority());
        }

        // 5. Add workload-based skills for complex cases
        if (serviceRequest.getWorkloadPoints() >= 4) {
            allRequiredSkills.add(UUID.fromString("550e8400-e29b-41d4-a716-************")); // Complex care management
            log.debug("Added complex care management skill for high workload");
        }

        List<UUID> result = new ArrayList<>(allRequiredSkills);
        log.info("Extracted {} total required skills for request {}", result.size(), serviceRequest.getRequestId());
        
        return result;
    }

    /**
     * Derive skills from visit type using business rules.
     */
    private List<UUID> deriveSkillsFromVisitType(String visitType) {
        if (visitType == null) return List.of();
        
        String normalizedVisitType = visitType.toLowerCase().trim();
        
        // Direct mapping
        List<UUID> directSkills = VISIT_TYPE_SKILLS.get(normalizedVisitType);
        if (directSkills != null) {
            return new ArrayList<>(directSkills);
        }
        
        // Pattern matching for complex visit types
        List<UUID> derivedSkills = new ArrayList<>();
        
        if (normalizedVisitType.contains("wound") || normalizedVisitType.contains("dressing")) {
            derivedSkills.addAll(VISIT_TYPE_SKILLS.get("wound_care"));
        }
        
        if (normalizedVisitType.contains("medication") || normalizedVisitType.contains("med")) {
            derivedSkills.addAll(VISIT_TYPE_SKILLS.get("medication_management"));
        }
        
        if (normalizedVisitType.contains("therapy") || normalizedVisitType.contains("rehab")) {
            derivedSkills.addAll(VISIT_TYPE_SKILLS.get("physical_therapy"));
        }
        
        if (normalizedVisitType.contains("mental") || normalizedVisitType.contains("psych")) {
            derivedSkills.addAll(VISIT_TYPE_SKILLS.get("mental_health"));
        }
        
        if (normalizedVisitType.contains("surgical") || normalizedVisitType.contains("post-op")) {
            derivedSkills.addAll(VISIT_TYPE_SKILLS.get("post_surgical"));
        }
        
        return derivedSkills;
    }

    /**
     * Derive skills from patient medical conditions.
     */
    private List<UUID> deriveSkillsFromMedicalConditions(String medicalConditions) {
        if (medicalConditions == null || medicalConditions.trim().isEmpty()) {
            return List.of();
        }
        
        Set<UUID> derivedSkills = new HashSet<>();
        String normalizedConditions = medicalConditions.toLowerCase();
        
        // Check for each known condition
        for (Map.Entry<String, List<UUID>> entry : MEDICAL_CONDITION_SKILLS.entrySet()) {
            if (normalizedConditions.contains(entry.getKey())) {
                derivedSkills.addAll(entry.getValue());
                log.debug("Found condition '{}' requiring skills: {}", entry.getKey(), entry.getValue().size());
            }
        }
        
        // Additional pattern matching
        if (normalizedConditions.contains("insulin") || normalizedConditions.contains("diabetic")) {
            derivedSkills.addAll(MEDICAL_CONDITION_SKILLS.get("diabetes"));
        }
        
        if (normalizedConditions.contains("heart") || normalizedConditions.contains("cardiac") || 
            normalizedConditions.contains("hypertension")) {
            derivedSkills.addAll(MEDICAL_CONDITION_SKILLS.get("cardiac"));
        }
        
        if (normalizedConditions.contains("copd") || normalizedConditions.contains("asthma") || 
            normalizedConditions.contains("breathing")) {
            derivedSkills.addAll(MEDICAL_CONDITION_SKILLS.get("respiratory"));
        }
        
        if (normalizedConditions.contains("mobility") || normalizedConditions.contains("walker") || 
            normalizedConditions.contains("wheelchair")) {
            derivedSkills.addAll(MEDICAL_CONDITION_SKILLS.get("mobility_assistance"));
        }
        
        return new ArrayList<>(derivedSkills);
    }

    /**
     * Derive priority-based skills for high-priority cases.
     */
    private List<UUID> derivePrioritySkills(int priority) {
        return PRIORITY_SKILLS.getOrDefault(priority, List.of());
    }

    /**
     * Get skill description for logging and debugging.
     */
    public String getSkillDescription(UUID skillId) {
        // This would typically come from a skill repository
        // For now, return a placeholder
        return "Skill-" + skillId.toString().substring(0, 8);
    }
}
