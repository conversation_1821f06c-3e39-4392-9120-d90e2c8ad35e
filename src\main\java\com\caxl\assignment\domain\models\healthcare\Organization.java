package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Organization domain model for hierarchical homecare management.
 * Represents the top-level organization that manages multiple teams.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Organization {

    @NotBlank(message = "Organization ID is required")
    @JsonProperty("organization_id")
    private String organizationId;

    @NotBlank(message = "Organization name is required")
    @JsonProperty("organization_name")
    private String organizationName;

    /**
     * Primary operating state for compliance.
     */
    @NotBlank(message = "Primary state is required")
    @JsonProperty("primary_state")
    private String primaryState;

    /**
     * Additional states where organization operates.
     */
    @JsonProperty("operating_states")
    private List<String> operatingStates;

    /**
     * Teams managed by this organization.
     */
    @Valid
    @JsonProperty("teams")
    private List<CareTeam> teams;

    /**
     * Organization-level licenses and certifications.
     */
    @Valid
    @JsonProperty("organization_licenses")
    private OrganizationLicenses organizationLicenses;

    /**
     * Contact information for the organization.
     */
    @Valid
    @NotNull(message = "Contact information is required")
    @JsonProperty("contact_info")
    private ContactInfo contactInfo;

    /**
     * Service areas covered by the organization.
     */
    @Valid
    @JsonProperty("service_areas")
    private List<ServiceArea> serviceAreas;

    // === NESTED CLASSES ===

    /**
     * Organization-level licensing information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OrganizationLicenses {
        
        @JsonProperty("medicare_provider_number")
        private String medicareProviderNumber;

        @JsonProperty("medicaid_provider_number")
        private String medicaidProviderNumber;

        @JsonProperty("state_licenses")
        private Map<String, String> stateLicenses; // state -> license number

        @JsonProperty("accreditation")
        private List<String> accreditation; // Joint Commission, CHAP, etc.
    }

    /**
     * Contact information for the organization.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ContactInfo {
        
        @NotBlank(message = "Main phone is required")
        @JsonProperty("main_phone")
        private String mainPhone;

        @JsonProperty("emergency_phone")
        private String emergencyPhone;

        @NotBlank(message = "Email is required")
        @JsonProperty("email")
        private String email;

        @Valid
        @NotNull(message = "Address is required")
        @JsonProperty("address")
        private Address address;
    }

    /**
     * Address information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Address {
        
        @NotBlank(message = "Street address is required")
        @JsonProperty("street")
        private String street;

        @NotBlank(message = "City is required")
        @JsonProperty("city")
        private String city;

        @NotBlank(message = "State is required")
        @JsonProperty("state")
        private String state;

        @NotBlank(message = "ZIP code is required")
        @JsonProperty("zip_code")
        private String zipCode;

        @JsonProperty("country")
        @Builder.Default
        private String country = "US";
    }

    /**
     * Service area definition for geographic coverage.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceArea {
        
        @NotBlank(message = "Area ID is required")
        @JsonProperty("area_id")
        private String areaId;

        @NotBlank(message = "Area name is required")
        @JsonProperty("area_name")
        private String areaName;

        @JsonProperty("zip_codes")
        private List<String> zipCodes;

        @JsonProperty("cities")
        private List<String> cities;

        @JsonProperty("counties")
        private List<String> counties;

        @JsonProperty("travel_radius_miles")
        private Double travelRadiusMiles;
    }
}
