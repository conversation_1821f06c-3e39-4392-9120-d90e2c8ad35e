package com.caxl.assignment.application.services;

import com.caxl.assignment.application.ports.RuleRepository;
import com.caxl.assignment.domain.exceptions.RuleEvaluationException;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.Rule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Rule evaluator service for evaluating business rules.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RuleEvaluatorService {

    private final RuleRepository ruleRepository;

    /**
     * Evaluate hard rules for a patient-clinician pair.
     *
     * @param patient   Patient object
     * @param clinician Clinician object
     * @return Evaluation result containing allowed status and satisfied rule IDs
     */
    public HardRuleEvaluationResult evaluateHardRules(Patient patient, Clinician clinician) {
        List<Rule> hardRules = ruleRepository.getHardRules();
        List<String> satisfiedRuleIds = new ArrayList<>();

        for (Rule rule : hardRules) {
            try {
                Object result = rule.evaluate(patient, clinician);
                boolean satisfied = result instanceof Boolean ? (Boolean) result : Boolean.parseBoolean(result.toString());

                if (satisfied) {
                    satisfiedRuleIds.add(rule.getRuleId());
                    log.debug("Hard rule {} satisfied for patient {} and clinician {}",
                             rule.getRuleId(), patient.getId(), clinician.getId());
                } else {
                    log.debug("Hard rule {} NOT satisfied for patient {} and clinician {}",
                             rule.getRuleId(), patient.getId(), clinician.getId());
                    // If any hard rule fails, the assignment is not allowed
                    return new HardRuleEvaluationResult(false, satisfiedRuleIds);
                }
            } catch (Exception e) {
                log.error("Error evaluating hard rule {} for patient {} and clinician {}: {}",
                         rule.getRuleId(), patient.getId(), clinician.getId(), e.getMessage());
                throw new RuleEvaluationException("Error evaluating hard rule: " + rule.getRuleId(), e);
            }
        }

        return new HardRuleEvaluationResult(true, satisfiedRuleIds);
    }

    /**
     * Evaluate soft rules for a patient-clinician pair.
     *
     * @param patient   Patient object
     * @param clinician Clinician object
     * @return Evaluation result containing score and satisfied/not satisfied rule IDs
     */
    public SoftRuleEvaluationResult evaluateSoftRules(Patient patient, Clinician clinician) {
        List<Rule> softRules = ruleRepository.getSoftRules();
        List<String> satisfiedRuleIds = new ArrayList<>();
        List<String> notSatisfiedRuleIds = new ArrayList<>();
        double totalScore = 0.0;

        for (Rule rule : softRules) {
            try {
                Object result = rule.evaluate(patient, clinician);
                double score = result instanceof Number ? ((Number) result).doubleValue() : 0.0;

                if (score > 0) {
                    satisfiedRuleIds.add(rule.getRuleId());
                    totalScore += score;
                    log.debug("Soft rule {} satisfied with score {} for patient {} and clinician {}",
                             rule.getRuleId(), score, patient.getId(), clinician.getId());
                } else {
                    notSatisfiedRuleIds.add(rule.getRuleId());
                    log.debug("Soft rule {} NOT satisfied for patient {} and clinician {}",
                             rule.getRuleId(), patient.getId(), clinician.getId());
                }
            } catch (Exception e) {
                log.error("Error evaluating soft rule {} for patient {} and clinician {}: {}",
                         rule.getRuleId(), patient.getId(), clinician.getId(), e.getMessage());
                // For soft rules, we continue even if one fails
                notSatisfiedRuleIds.add(rule.getRuleId());
            }
        }

        return new SoftRuleEvaluationResult(totalScore, satisfiedRuleIds, notSatisfiedRuleIds);
    }

    /**
     * Evaluate all rules for a patient-clinician pair.
     *
     * @param patient   Patient object
     * @param clinician Clinician object
     * @return Complete evaluation result
     */
    public CompleteRuleEvaluationResult evaluateAllRules(Patient patient, Clinician clinician) {
        HardRuleEvaluationResult hardResult = evaluateHardRules(patient, clinician);
        SoftRuleEvaluationResult softResult = evaluateSoftRules(patient, clinician);

        return new CompleteRuleEvaluationResult(
                hardResult.isAllowed(),
                hardResult.satisfiedRuleIds(),
                softResult.satisfiedRuleIds(),
                softResult.notSatisfiedRuleIds(),
                softResult.totalScore()
        );
    }

    /**
     * Evaluate rules for multiple patient-clinician pairs and create matrices.
     *
     * @param patients   List of patients
     * @param clinicians List of clinicians
     * @return Evaluation matrices
     */
    public EvaluationMatrices evaluateAllPairs(List<Patient> patients, List<Clinician> clinicians) {
        Map<String, Boolean> allowedMatrix = new HashMap<>();
        Map<String, Double> softScoreMatrix = new HashMap<>();

        for (Patient patient : patients) {
            for (Clinician clinician : clinicians) {
                String key = createMatrixKey(patient.getId(), clinician.getId());

                // Evaluate hard rules
                HardRuleEvaluationResult hardResult = evaluateHardRules(patient, clinician);
                allowedMatrix.put(key, hardResult.isAllowed());

                // Evaluate soft rules if hard rules are satisfied
                if (hardResult.isAllowed()) {
                    SoftRuleEvaluationResult softResult = evaluateSoftRules(patient, clinician);
                    softScoreMatrix.put(key, softResult.totalScore());
                } else {
                    softScoreMatrix.put(key, 0.0);
                }
            }
        }

        return new EvaluationMatrices(allowedMatrix, softScoreMatrix);
    }

    /**
     * Create a matrix key for patient-clinician pair.
     *
     * @param patientId   Patient ID
     * @param clinicianId Clinician ID
     * @return Matrix key
     */
    public static String createMatrixKey(String patientId, String clinicianId) {
        return patientId + "_" + clinicianId;
    }

    // Result classes
    public record HardRuleEvaluationResult(boolean isAllowed, List<String> satisfiedRuleIds) {}

    public record SoftRuleEvaluationResult(double totalScore, List<String> satisfiedRuleIds, List<String> notSatisfiedRuleIds) {}

    public record CompleteRuleEvaluationResult(
            boolean isAllowed,
            List<String> hardRulesSatisfied,
            List<String> softRulesSatisfied,
            List<String> softRulesNotSatisfied,
            double totalScore
    ) {}

    public record EvaluationMatrices(Map<String, Boolean> allowedMatrix, Map<String, Double> softScoreMatrix) {}
}
