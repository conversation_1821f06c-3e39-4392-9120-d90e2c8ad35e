package com.caxl.assignment.domain.carestaff.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain entity representing a scheduled appointment.
 * Framework-agnostic domain model for appointment management.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Appointment {

    private UUID appointmentId;
    private UUID serviceRequestId;
    private UUID careStaffId;
    private UUID patientId;
    private LocalDateTime scheduledStartTime;
    private LocalDateTime scheduledEndTime;
    private LocalDateTime actualStartTime;
    private LocalDateTime actualEndTime;
    private String status; // scheduled, in_progress, completed, cancelled, no_show
    private String notes;
    private UUID schedulerId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Create a new appointment from a service request.
     */
    public static Appointment create(UUID serviceRequestId, UUID careStaffId, UUID patientId,
                                   LocalDateTime startTime, LocalDateTime endTime, UUID schedulerId) {
        return Appointment.builder()
                .appointmentId(UUID.randomUUID())
                .serviceRequestId(serviceRequestId)
                .careStaffId(careStaffId)
                .patientId(patientId)
                .scheduledStartTime(startTime)
                .scheduledEndTime(endTime)
                .status("scheduled")
                .schedulerId(schedulerId)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * Check if this appointment overlaps with a given time window.
     */
    public boolean overlapsWith(LocalDateTime startTime, LocalDateTime endTime, int thresholdMinutes) {
        if (scheduledStartTime == null || scheduledEndTime == null) {
            return false;
        }
        
        // Calculate overlap
        LocalDateTime overlapStart = scheduledStartTime.isAfter(startTime) ? scheduledStartTime : startTime;
        LocalDateTime overlapEnd = scheduledEndTime.isBefore(endTime) ? scheduledEndTime : endTime;
        
        if (overlapStart.isBefore(overlapEnd)) {
            long overlapMinutes = java.time.Duration.between(overlapStart, overlapEnd).toMinutes();
            return overlapMinutes >= thresholdMinutes;
        }
        
        return false;
    }

    /**
     * Update appointment status.
     */
    public void updateStatus(String newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Check if appointment is active (scheduled or in progress).
     */
    public boolean isActive() {
        return "scheduled".equalsIgnoreCase(status) || "in_progress".equalsIgnoreCase(status);
    }
}
