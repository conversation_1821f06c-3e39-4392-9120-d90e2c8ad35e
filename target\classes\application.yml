server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true

spring:
  application:
    name: caxl-assignment-service
  datasource:
    url: ****************************************************
    username: caxl_user
    password: caxl_password
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.spatial.dialect.postgis.PostgisPG10Dialect
        format_sql: true
        jdbc:
          lob:
            non_contextual_creation: true
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  mvc:
    throw-exception-if-no-handler-found: true

logging:
  level:
    root: INFO
    com.caxl.assignment: DEBUG
    ai.timefold.solver: INFO

timefold:
  solver:
    termination:
      spent-limit: 30s
      unimproved-spent-limit: 10s
      best-score-limit: 0hard/*soft
    move-thread-count: AUTO
    environment-mode: PRODUCTION

# Application specific configuration
assignment:
  config:
    rules-file: rules.json
    skill-hierarchies-file: skill_hierarchies.json
    base-score: 100.0
    max-assignments-per-batch: 100
    relaxation:
      strategies:
        - SKILL_SUBSTITUTION
        - VISIT_RESCHEDULING
        - EXTENDED_SERVICE_RADIUS
        - OVERTIME_CAPACITY
      max-date-shift-days: 3
      extended-radius-percent: 20
      overtime-capacity-percent: 20
      max-relaxations-per-patient: 3

# CareStaff Matching Service Configuration
carestaff:
  matching:
    default-search-radius-km: 50.0
    min-score-threshold: 0.0
    overlap-threshold-minutes: 1
    min-time-before-visit-minutes: 30
    min-time-after-visit-minutes: 30
    geofence-strict-containment: true
    staff-service-geofence-types:
      - service_area
      - county_area

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
