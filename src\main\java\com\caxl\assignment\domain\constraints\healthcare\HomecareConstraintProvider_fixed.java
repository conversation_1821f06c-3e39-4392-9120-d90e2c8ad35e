package com.caxl.assignment.domain.constraints.healthcare;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import ai.timefold.solver.core.api.score.stream.Joiners;

import com.caxl.assignment.domain.models.healthcare.*;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Enhanced HomecareConstraintProvider with US state-specific compliance
 * and geofencing capabilities for homecare scheduling using Timefold.
 */
public class HomecareConstraintProvider implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
        return new Constraint[] {
                // === HARD CONSTRAINTS (Must be satisfied) ===
                careStaffSkillMatch(constraintFactory),
                careStaffLicenseCompliance(constraintFactory),
                careStaffAvailability(constraintFactory),
                geographicZoneCompliance(constraintFactory),
                workingHoursCompliance(constraintFactory),
                overtimeRestrictionsCompliance(constraintFactory),
                clinicalSupervisionRequirements(constraintFactory),
                stateSpecificCompliance(constraintFactory),
                
                // === NEW ENHANCED HARD CONSTRAINTS ===
                stateLicenseValidation(constraintFactory),
                serviceTypeQualificationMatch(constraintFactory),
                geoZoneBoundaryCompliance(constraintFactory),
                maximumTravelDistanceLimit(constraintFactory),
                noConflictingVisitsForStaff(constraintFactory),
                stateWorkingHourLimits(constraintFactory),
                mandatoryBreakPeriods(constraintFactory),
                backgroundCheckCompliance(constraintFactory),
                
                // === SOFT CONSTRAINTS (Optimization objectives) ===
                minimizeTravelTime(constraintFactory),
                maximizeContinuityOfCare(constraintFactory),
                balanceWorkloadAmongStaff(constraintFactory),
                respectPatientPreferences(constraintFactory),
                respectStaffPreferences(constraintFactory),
                minimizeFragmentation(constraintFactory),
                prioritizeUrgentVisits(constraintFactory),
                optimizeTeamUtilization(constraintFactory),
                
                // === NEW ENHANCED SOFT CONSTRAINTS ===
                minimizeInterzoneTravel(constraintFactory),
                balanceWorkloadByGeoZone(constraintFactory),
                maximizeStaffUtilization(constraintFactory),
                minimizeOvertimeUsage(constraintFactory),
                preferExperiencedStaffForComplexCases(constraintFactory)
        };
    }

    // === HARD CONSTRAINTS ===


    /**
     * Care staff must have required skills for the visit.
     */
    protected Constraint careStaffSkillMatch(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> !hasRequiredSkills(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff skill match required");
    }

    /**
     * Care staff must have valid license for the state and service type.
     */
    protected Constraint careStaffLicenseCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> !hasValidLicense(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff license compliance required");
    }

    /**
     * Care staff must be available during visit time.
     */
    protected Constraint careStaffAvailability(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> !isStaffAvailable(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff availability required");
    }

    /**
     * Care staff must operate within their designated geographic zones.
     */
    protected Constraint geographicZoneCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> !isWithinOperatingZone(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Geographic zone compliance required");
    }

    /**
     * Care staff working hours must comply with labor regulations.
     */
    protected Constraint workingHoursCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .groupBy(HomecareVisit::getAssignedCareStaffId,
                        (visitGroup) -> visitGroup.sum(visit -> 
                                visit.getVisitSchedule().getEstimatedDuration().toMinutes()))
                .filter((staffId, totalMinutes) -> totalMinutes > getMaxDailyMinutes(staffId))
                .penalize(HardSoftScore.ONE_HARD,
                        (staffId, totalMinutes) -> (int)(totalMinutes - getMaxDailyMinutes(staffId)))
                .asConstraint("Working hours compliance required");
    }

    /**
     * Overtime and rest period regulations must be followed.
     */
    protected Constraint overtimeRestrictionsCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(HomecareVisit.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, HomecareVisit::getAssignedCareStaffId),
                        Joiners.lessThan(HomecareVisit::getVisitId, HomecareVisit::getVisitId))
                .filter((visit1, visit2) -> violatesRestPeriod(visit1, visit2))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Rest period compliance required");
    }

    /**
     * Clinical supervision requirements must be met.
     */
    protected Constraint clinicalSupervisionRequirements(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> requiresSupervisionButNotAvailable(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Clinical supervision requirements");
    }

    /**
     * State-specific compliance rules must be followed.
     */
    protected Constraint stateSpecificCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> violatesStateRegulations(visit, staff))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("State-specific compliance required");
    }

    // === NEW ENHANCED HARD CONSTRAINTS ===


    /**
     * Enhanced state license validation for multi-state operations.
     */
    protected Constraint stateLicenseValidation(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .join(HomePatient.class,
                        Joiners.equal(HomecareVisit::getPatientId, HomePatient::getId))
                .filter((visit, staff, patient) -> !hasValidStateLicense(staff, patient.getState()))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff must have valid license for patient's state");
    }

    /**
     * Service type qualification matching with enhanced validation.
     */
    protected Constraint serviceTypeQualificationMatch(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .filter((visit, staff) -> !isQualifiedForServiceType(staff, visit.getServiceType()))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff must be qualified for the service type");
    }

    /**
     * Geographic zone boundary compliance with precise geofencing.
     */
    protected Constraint geoZoneBoundaryCompliance(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .join(HomePatient.class,
                        Joiners.equal(HomecareVisit::getPatientId, HomePatient::getId))
                .filter((visit, staff, patient) -> !isWithinAssignedGeoZone(staff, patient))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Care staff must operate within assigned geographic zones");
    }

    /**
     * Maximum travel distance limit per visit.
     */
    protected Constraint maximumTravelDistanceLimit(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(HomecareVisit.class)
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .join(CareStaff.class,
                        Joiners.equal(HomecareVisit::getAssignedCareStaffId, CareStaff::getId))
                .join(HomePatient.class,
                        Joiners.equal(HomecareVisit::getPatientId, HomePatient::getId))
                .filter((visit, staff, patient) -> exceedsMaxTravelDistance(staff, patient))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Travel distance must not exceed maximum limit");
    }

    // ... rest of the file remains the same ...

    // Helper methods remain unchanged
    private boolean hasRequiredSkills(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return true;
    }

    private boolean hasValidLicense(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return true;
    }

    private boolean isStaffAvailable(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return true;
    }

    private boolean isWithinOperatingZone(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return true;
    }

    private long getMaxDailyMinutes(String staffId) {
        // Implementation
        return 8 * 60; // 8 hours
    }

    private boolean violatesRestPeriod(HomecareVisit visit1, HomecareVisit visit2) {
        // Implementation
        return false;
    }

    private boolean requiresSupervisionButNotAvailable(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return false;
    }

    private boolean violatesStateRegulations(HomecareVisit visit, CareStaff staff) {
        // Implementation
        return false;
    }

    private boolean hasValidStateLicense(CareStaff staff, String state) {
        // Implementation
        return true;
    }

    private boolean isQualifiedForServiceType(CareStaff staff, ServiceType serviceType) {
        // Implementation
        return true;
    }

    private boolean isWithinAssignedGeoZone(CareStaff staff, HomePatient patient) {
        // Implementation
        return true;
    }

    private boolean exceedsMaxTravelDistance(CareStaff staff, HomePatient patient) {
        // Implementation
        return false;
    }
}
