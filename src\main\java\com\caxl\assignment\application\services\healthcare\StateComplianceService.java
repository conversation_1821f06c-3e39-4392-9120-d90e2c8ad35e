package com.caxl.assignment.application.services.healthcare;

import com.caxl.assignment.domain.models.healthcare.*;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * StateComplianceService manages state-specific compliance rules and validation
 * for US homecare scheduling across multiple states.
 */
@Service
public class StateComplianceService {

    private final Map<String, USState> stateRules;

    public StateComplianceService() {
        // Initialize with sample state rules - in production, load from database
        this.stateRules = initializeStateRules();
    }

    /**
     * Validate care staff compliance for a specific state.
     */
    public ComplianceValidationResult validateStaffCompliance(CareStaff careStaff, String stateCode) {
        USState state = stateRules.get(stateCode);
        if (state == null) {
            return ComplianceValidationResult.builder()
                    .isCompliant(false)
                    .violations(List.of("Unknown state: " + stateCode))
                    .build();
        }

        List<String> violations = validateAgainstStateRules(careStaff, state);
        
        return ComplianceValidationResult.builder()
                .isCompliant(violations.isEmpty())
                .violations(violations)
                .stateCode(stateCode)
                .validationDate(LocalDateTime.now())
                .build();
    }

    /**
     * Check working hour compliance for a specific state.
     */
    public boolean isWorkingHoursCompliant(CareStaff careStaff, String stateCode, int proposedHours) {
        USState state = stateRules.get(stateCode);
        if (state == null || state.getWorkingHourRegulations() == null) {
            return true; // Default to allowing if no rules
        }

        USState.WorkingHourRegulations regulations = state.getWorkingHourRegulations();
        
        if (regulations.getMaxHoursPerWeek() != null && 
            proposedHours > regulations.getMaxHoursPerWeek()) {
            return false;
        }

        return true;
    }

    /**
     * Get overtime regulations for a specific state.
     */
    public Optional<USState.OvertimeRegulations> getOvertimeRegulations(String stateCode) {
        USState state = stateRules.get(stateCode);
        return state != null ? Optional.ofNullable(state.getOvertimeRegulations()) : Optional.empty();
    }

    /**
     * Check if overtime is allowed for the given hours.
     */
    public boolean isOvertimeAllowed(String stateCode, int currentHours, int additionalHours) {
        return getOvertimeRegulations(stateCode)
                .map(regs -> {
                    if (regs.isOvertimeApprovalRequired()) {
                        return false; // Require manual approval
                    }
                    
                    int totalOvertimeHours = Math.max(0, (currentHours + additionalHours) - 40);
                    return regs.getMaxOvertimeHoursPerWeek() == null || 
                           totalOvertimeHours <= regs.getMaxOvertimeHoursPerWeek();
                })
                .orElse(true); // Default to allow if no regulations
    }

    /**
     * Get all active compliance rules for a state at a specific date.
     */
    public List<USState.ComplianceRule> getActiveComplianceRules(String stateCode, LocalDateTime date) {
        USState state = stateRules.get(stateCode);
        if (state == null) {
            return List.of();
        }
        
        return state.getActiveRules(date);
    }

    /**
     * Check license reciprocity between states.
     */
    public boolean hasLicenseReciprocity(String fromState, String toState) {
        USState state = stateRules.get(toState);
        if (state == null || state.getLicensingRequirements() == null) {
            return false;
        }
        
        return state.getLicensingRequirements().getReciprocityStates().contains(fromState);
    }

    /**
     * Validate care staff against all applicable state rules.
     */
    private List<String> validateAgainstStateRules(CareStaff careStaff, USState state) {
        List<String> violations = new java.util.ArrayList<>();

        // Check licensing requirements
        if (state.getLicensingRequirements() != null) {
            violations.addAll(validateLicensingRequirements(careStaff, state.getLicensingRequirements()));
        }

        // Check background check requirements
        if (state.getBackgroundCheckRequirements() != null) {
            violations.addAll(validateBackgroundCheckRequirements(careStaff, state.getBackgroundCheckRequirements()));
        }

        // Check working hour regulations
        if (state.getWorkingHourRegulations() != null) {
            violations.addAll(validateWorkingHourRegulations(careStaff, state.getWorkingHourRegulations()));
        }

        return violations;
    }

    private List<String> validateLicensingRequirements(CareStaff careStaff, USState.LicensingRequirements requirements) {
        List<String> violations = new java.util.ArrayList<>();

        if (requirements.isLicenseVerificationRequired()) {
            boolean hasValidLicense = careStaff.getStateLicensing().getLicenses().stream()
                    .anyMatch(license -> !license.isExpired() && 
                             requirements.getRequiredLicenses().contains(license.getLicenseName()));
            
            if (!hasValidLicense) {
                violations.add("Missing required valid license");
            }
        }

        return violations;
    }

    private List<String> validateBackgroundCheckRequirements(CareStaff careStaff, USState.BackgroundCheckRequirements requirements) {
        List<String> violations = new java.util.ArrayList<>();

        if (requirements.isCriminalBackgroundCheck() && 
            !careStaff.getComplianceStatus().isBackgroundCheckValid()) {
            violations.add("Criminal background check required");
        }

        if (requirements.isDrugScreening() && 
            !careStaff.getComplianceStatus().isDrugScreeningValid()) {
            violations.add("Drug screening required");
        }

        return violations;
    }

    private List<String> validateWorkingHourRegulations(CareStaff careStaff, USState.WorkingHourRegulations regulations) {
        List<String> violations = new java.util.ArrayList<>();

        if (regulations.getMaxHoursPerWeek() != null && 
            careStaff.getWorkingHours().getMaxHoursPerWeek() > regulations.getMaxHoursPerWeek()) {
            violations.add("Exceeds maximum hours per week limit");
        }

        return violations;
    }

    /**
     * Initialize state rules - in production, this would load from database.
     */
    private Map<String, USState> initializeStateRules() {
        // Sample state rules for California and Texas
        USState california = createCaliforniaRules();
        USState texas = createTexasRules();
        
        return Map.of(
                "CA", california,
                "TX", texas
        );
    }

    private USState createCaliforniaRules() {
        return USState.builder()
                .stateCode("CA")
                .stateName("California")
                .workingHourRegulations(USState.WorkingHourRegulations.builder()
                        .maxHoursPerWeek(40)
                        .maxHoursPerDay(8)
                        .overtimeThresholdHours(8)
                        .mandatoryRestPeriod(true)
                        .minHoursBetweenShifts(8)
                        .build())
                .overtimeRegulations(USState.OvertimeRegulations.builder()
                        .overtimeRateMultiplier(1.5)
                        .doubleTimeThreshold(12)
                        .overtimeApprovalRequired(false)
                        .maxOvertimeHoursPerWeek(20)
                        .build())
                .backgroundCheckRequirements(USState.BackgroundCheckRequirements.builder()
                        .criminalBackgroundCheck(true)
                        .fingerprintCheck(true)
                        .drugScreening(true)
                        .validityPeriodMonths(24)
                        .build())
                .build();
    }

    private USState createTexasRules() {
        return USState.builder()
                .stateCode("TX")
                .stateName("Texas")
                .workingHourRegulations(USState.WorkingHourRegulations.builder()
                        .maxHoursPerWeek(50)
                        .maxHoursPerDay(10)
                        .overtimeThresholdHours(40)
                        .mandatoryRestPeriod(false)
                        .minHoursBetweenShifts(6)
                        .build())
                .overtimeRegulations(USState.OvertimeRegulations.builder()
                        .overtimeRateMultiplier(1.5)
                        .overtimeApprovalRequired(true)
                        .maxOvertimeHoursPerWeek(15)
                        .build())
                .backgroundCheckRequirements(USState.BackgroundCheckRequirements.builder()
                        .criminalBackgroundCheck(true)
                        .fingerprintCheck(false)
                        .drugScreening(true)
                        .validityPeriodMonths(36)
                        .build())
                .build();
    }

    /**
     * Compliance validation result.
     */
    public static class ComplianceValidationResult {
        private final boolean isCompliant;
        private final List<String> violations;
        private final String stateCode;
        private final LocalDateTime validationDate;

        public ComplianceValidationResult(boolean isCompliant, List<String> violations, 
                                        String stateCode, LocalDateTime validationDate) {
            this.isCompliant = isCompliant;
            this.violations = violations;
            this.stateCode = stateCode;
            this.validationDate = validationDate;
        }

        public static ComplianceValidationResultBuilder builder() {
            return new ComplianceValidationResultBuilder();
        }

        // Getters
        public boolean isCompliant() { return isCompliant; }
        public List<String> getViolations() { return violations; }
        public String getStateCode() { return stateCode; }
        public LocalDateTime getValidationDate() { return validationDate; }

        public static class ComplianceValidationResultBuilder {
            private boolean isCompliant;
            private List<String> violations;
            private String stateCode;
            private LocalDateTime validationDate;

            public ComplianceValidationResultBuilder isCompliant(boolean isCompliant) {
                this.isCompliant = isCompliant;
                return this;
            }

            public ComplianceValidationResultBuilder violations(List<String> violations) {
                this.violations = violations;
                return this;
            }

            public ComplianceValidationResultBuilder stateCode(String stateCode) {
                this.stateCode = stateCode;
                return this;
            }

            public ComplianceValidationResultBuilder validationDate(LocalDateTime validationDate) {
                this.validationDate = validationDate;
                return this;
            }

            public ComplianceValidationResult build() {
                return new ComplianceValidationResult(isCompliant, violations, stateCode, validationDate);
            }
        }
    }
}
