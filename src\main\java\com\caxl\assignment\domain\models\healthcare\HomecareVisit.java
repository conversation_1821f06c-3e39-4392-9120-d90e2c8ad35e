package com.caxl.assignment.domain.models.healthcare;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.variable.PlanningVariable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * HomecareVisit domain model representing a scheduled visit to a patient's home.
 * This is the core planning entity that will be optimized by Timefold.
 */
@PlanningEntity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomecareVisit {

    @NotBlank(message = "Visit ID is required")
    @JsonProperty("visit_id")
    private String visitId;    /**
     * Patient information for this visit.
     */
    @NotBlank(message = "Patient ID is required")
    @JsonProperty("patient_id")
    private String patientId; // Reference to HomePatient

    /**
     * Patient object for this visit (used during scheduling).
     */
    @JsonProperty("patient")
    private HomePatient patient;

    /**
     * Assigned care staff for this visit.
     * This is the planning variable that Timefold will optimize.
     */
    @PlanningVariable(valueRangeProviderRefs = "careStaffRange")
    @JsonProperty("assigned_care_staff_id")
    private String assignedCareStaffId; // Reference to CareStaff - this will be optimized

    /**
     * Scheduled start time for this visit.
     * This is a planning variable that Timefold will optimize.
     */
    @PlanningVariable(valueRangeProviderRefs = "timeSlotRange")
    @JsonProperty("scheduled_start_time")
    private LocalDateTime scheduledStartTime;

    /**
     * Visit scheduling information.
     */
    @Valid
    @NotNull(message = "Visit schedule is required")
    @JsonProperty("visit_schedule")
    private VisitSchedule visitSchedule;

    /**
     * Service details for this visit.
     */
    @Valid
    @NotNull(message = "Service details are required")
    @JsonProperty("service_details")
    private ServiceDetails serviceDetails;

    /**
     * Clinical requirements and constraints.
     */
    @Valid
    @JsonProperty("clinical_requirements")
    private ClinicalRequirements clinicalRequirements;

    /**
     * Geographic and logistics information.
     */
    @Valid
    @JsonProperty("logistics_info")
    private LogisticsInfo logisticsInfo;

    /**
     * State-specific compliance requirements.
     */
    @Valid
    @JsonProperty("compliance_requirements")
    private ComplianceRequirements complianceRequirements;

    /**
     * Visit priority and urgency.
     */
    @Valid
    @JsonProperty("priority_info")
    private PriorityInfo priorityInfo;

    // === NESTED CLASSES ===

    /**
     * Visit scheduling information including time windows and constraints.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VisitSchedule {
        
        @JsonProperty("preferred_start_time")
        private LocalDateTime preferredStartTime;

        @JsonProperty("earliest_start_time")
        private LocalDateTime earliestStartTime;

        @JsonProperty("latest_start_time")
        private LocalDateTime latestStartTime;

        @NotNull(message = "Estimated duration is required")
        @JsonProperty("estimated_duration")
        private Duration estimatedDuration;

        @JsonProperty("frequency")
        private VisitFrequency frequency;

        @JsonProperty("recurring_pattern")
        private RecurringPattern recurringPattern;

        @JsonProperty("is_urgent")
        @Builder.Default
        private boolean isUrgent = false;

        @JsonProperty("can_be_rescheduled")
        @Builder.Default
        private boolean canBeRescheduled = true;
    }

    /**
     * Visit frequency enumeration.
     */
    public enum VisitFrequency {
        ONE_TIME,
        DAILY,
        TWICE_DAILY,
        THREE_TIMES_DAILY,
        WEEKLY,
        TWICE_WEEKLY,
        THREE_TIMES_WEEKLY,
        BIWEEKLY,
        MONTHLY,
        AS_NEEDED
    }

    /**
     * Recurring visit pattern for regular patients.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RecurringPattern {
        
        @JsonProperty("days_of_week")
        private List<Integer> daysOfWeek; // 1=Monday, 7=Sunday

        @JsonProperty("preferred_times")
        private List<String> preferredTimes; // "morning", "afternoon", "evening"

        @JsonProperty("same_staff_preference")
        @Builder.Default
        private boolean sameStaffPreference = true;

        @JsonProperty("pattern_start_date")
        private LocalDateTime patternStartDate;

        @JsonProperty("pattern_end_date")
        private LocalDateTime patternEndDate;
    }

    /**
     * Service details and clinical tasks for the visit.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ServiceDetails {
        
        @NotNull(message = "Primary service type is required")
        @JsonProperty("primary_service_type")
        private String primaryServiceType; // Skilled Nursing, PT, OT, etc.

        @JsonProperty("secondary_services")
        private List<String> secondaryServices;

        @JsonProperty("clinical_tasks")
        private List<String> clinicalTasks;

        @JsonProperty("required_equipment")
        private List<String> requiredEquipment;

        @JsonProperty("medication_administration")
        @Builder.Default
        private boolean medicationAdministration = false;

        @JsonProperty("wound_care_required")
        @Builder.Default
        private boolean woundCareRequired = false;

        @JsonProperty("assessment_required")
        @Builder.Default
        private boolean assessmentRequired = false;

        @JsonProperty("documentation_requirements")
        private List<String> documentationRequirements;
    }

    /**
     * Clinical requirements and staff qualifications needed.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClinicalRequirements {
        
        @JsonProperty("required_certifications")
        private List<String> requiredCertifications;

        @JsonProperty("required_skills")
        private List<String> requiredSkills;

        @JsonProperty("minimum_experience_years")
        private Integer minimumExperienceYears;

        @JsonProperty("language_requirement")
        private String languageRequirement;

        @JsonProperty("gender_preference")
        private String genderPreference;

        @JsonProperty("specialty_required")
        private String specialtyRequired;

        @JsonProperty("supervision_level")
        private String supervisionLevel; // Independent, Supervised, etc.
    }

    /**
     * Geographic and logistics information for visit planning.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LogisticsInfo {
        
        @JsonProperty("patient_address")
        private String patientAddress;

        @JsonProperty("geocode_lat")
        private Double geocodeLat;

        @JsonProperty("geocode_lng")
        private Double geocodeLng;

        @JsonProperty("operating_zone_id")
        private String operatingZoneId;

        @JsonProperty("parking_availability")
        @Builder.Default
        private boolean parkingAvailability = true;

        @JsonProperty("building_access_notes")
        private String buildingAccessNotes;

        @JsonProperty("travel_considerations")
        private List<String> travelConsiderations;

        @JsonProperty("estimated_travel_time_prev")
        private Duration estimatedTravelTimePrev;

        @JsonProperty("estimated_travel_time_next")
        private Duration estimatedTravelTimeNext;
    }

    /**
     * State-specific compliance requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComplianceRequirements {
        
        @JsonProperty("state_regulations")
        private Map<String, Object> stateRegulations;

        @JsonProperty("medicare_compliance")
        @Builder.Default
        private boolean medicareCompliance = false;

        @JsonProperty("medicaid_compliance")
        @Builder.Default
        private boolean medicaidCompliance = false;

        @JsonProperty("insurance_authorizations")
        private List<String> insuranceAuthorizations;

        @JsonProperty("clinical_supervision_required")
        @Builder.Default
        private boolean clinicalSupervisionRequired = false;

        @JsonProperty("documentation_deadline_hours")
        private Integer documentationDeadlineHours;
    }

    /**
     * Visit priority and urgency information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PriorityInfo {
        
        @JsonProperty("priority_level")
        @Builder.Default
        private PriorityLevel priorityLevel = PriorityLevel.ROUTINE;

        @JsonProperty("urgency_score")
        private Integer urgencyScore; // 1-10 scale

        @JsonProperty("clinical_acuity")
        private String clinicalAcuity;

        @JsonProperty("patient_stability")
        private String patientStability;

        @JsonProperty("scheduling_flexibility")
        @Builder.Default
        private SchedulingFlexibility schedulingFlexibility = SchedulingFlexibility.FLEXIBLE;
    }

    /**
     * Priority level enumeration.
     */
    public enum PriorityLevel {
        EMERGENCY,    // Must be seen within hours
        URGENT,       // Must be seen within 24 hours
        HIGH,         // Must be seen within 2-3 days
        ROUTINE,      // Normal scheduling
        LOW           // Can be delayed if needed
    }

    /**
     * Scheduling flexibility enumeration.
     */
    public enum SchedulingFlexibility {
        FIXED,        // Cannot be moved
        LIMITED,      // Small time window adjustments
        FLEXIBLE,     // Can be rescheduled within constraints
        VERY_FLEXIBLE // High scheduling flexibility
    }

    // === CONVENIENCE METHODS FOR CONSTRAINTS ===

    /**
     * Get the duration of this visit from the visit schedule.
     */
    public Duration getDuration() {
        return visitSchedule != null ? visitSchedule.getEstimatedDuration() : Duration.ofHours(1);
    }

    /**
     * Get the scheduled end time calculated from start time and duration.
     */
    public LocalDateTime getScheduledEndTime() {
        if (scheduledStartTime == null || getDuration() == null) {
            return null;
        }
        return scheduledStartTime.plus(getDuration());
    }

    /**
     * Check if this visit has a valid scheduled time window.
     */
    public boolean hasValidTimeWindow() {
        return scheduledStartTime != null && 
               visitSchedule != null && 
               visitSchedule.getEstimatedDuration() != null;
    }

    /**
     * Check if this visit overlaps with another visit.
     */
    public boolean overlapsWith(HomecareVisit other) {
        if (!this.hasValidTimeWindow() || !other.hasValidTimeWindow()) {
            return false;
        }
        
        LocalDateTime thisStart = this.scheduledStartTime;
        LocalDateTime thisEnd = this.getScheduledEndTime();
        LocalDateTime otherStart = other.scheduledStartTime;
        LocalDateTime otherEnd = other.getScheduledEndTime();
        
        return thisStart.isBefore(otherEnd) && otherStart.isBefore(thisEnd);
    }

    /**
     * Get the required skills for this visit.
     */
    public List<String> getRequiredSkills() {
        if (clinicalRequirements != null && clinicalRequirements.getRequiredSkills() != null) {
            return clinicalRequirements.getRequiredSkills();
        }
        return List.of();
    }

    /**
     * Check if visit is within time window constraints.
     */
    public boolean isWithinTimeWindow() {
        if (scheduledStartTime == null || visitSchedule == null) {
            return false;
        }
        
        LocalDateTime earliest = visitSchedule.getEarliestStartTime();
        LocalDateTime latest = visitSchedule.getLatestStartTime();
        
        if (earliest != null && scheduledStartTime.isBefore(earliest)) {
            return false;
        }
        
        if (latest != null && scheduledStartTime.isAfter(latest)) {
            return false;
        }
        
        return true;
    }
}
