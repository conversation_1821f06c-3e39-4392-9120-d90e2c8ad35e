package com.caxl.assignment.infrastructure.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for bulk assignment results.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkAssignmentResultDto {

    @JsonProperty("total_requested")
    private Integer totalRequested;

    @JsonProperty("successful_assignments")
    private Integer successfulAssignments;

    @JsonProperty("failed_assignments")
    private Integer failedAssignments;

    @JsonProperty("errors")
    private List<String> errors;

    @JsonProperty("success")
    private Boolean success;
}
