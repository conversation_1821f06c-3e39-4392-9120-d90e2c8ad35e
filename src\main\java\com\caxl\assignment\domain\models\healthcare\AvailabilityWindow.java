package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.DayOfWeek;
import java.time.LocalDateTime;

/**
 * Represents an availability window for care staff scheduling.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AvailabilityWindow {

    @NotNull(message = "Start time is required")
    @JsonProperty("start_time")
    private LocalDateTime startTime;

    @NotNull(message = "End time is required")
    @JsonProperty("end_time")
    private LocalDateTime endTime;

    @NotNull(message = "Day of week is required")
    @JsonProperty("day_of_week")
    private DayOfWeek dayOfWeek;

    @JsonProperty("is_recurring")
    @Builder.Default
    private boolean isRecurring = true;

    @JsonProperty("notes")
    private String notes;

    /**
     * Check if the given time falls within this availability window.
     */
    public boolean containsTime(LocalDateTime time) {
        return !time.isBefore(startTime) && !time.isAfter(endTime);
    }

    /**
     * Check if this availability window overlaps with another.
     */
    public boolean overlapsWith(AvailabilityWindow other) {
        return startTime.isBefore(other.endTime) && endTime.isAfter(other.startTime);
    }
}
