package com.caxl.assignment.application.services.traffic;

import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * Traffic-aware service for real-time travel time optimization.
 * Integrates with external traffic APIs and maintains dynamic travel time matrix.
 */
@Service
@Slf4j
public class TrafficAwareService {

    @Value("${assignment.traffic.google-maps-api-key:}")
    private String googleMapsApiKey;

    @Value("${assignment.traffic.update-interval-minutes:5}")
    private int updateIntervalMinutes;

    @Value("${assignment.traffic.max-delay-threshold-minutes:15}")
    private int maxDelayThresholdMinutes;

    @Value("${assignment.traffic.enabled:true}")
    private boolean trafficAwarenessEnabled;

    private final RestTemplate restTemplate;
    private final Map<String, TravelTimeInfo> travelTimeCache = new ConcurrentHashMap<>();
    private final Map<String, TrafficCondition> regionTrafficConditions = new ConcurrentHashMap<>();

    public TrafficAwareService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    public enum TrafficCondition {
        LIGHT, NORMAL, HEAVY, SEVERE
    }

    /**
     * Get real-time travel time between clinician and patient locations.
     */
    public Duration getRealTimeTravelTime(Clinician clinician, Patient patient) {
        if (!trafficAwarenessEnabled) {
            return getBaselineTravelTime(clinician, patient);
        }

        String cacheKey = generateCacheKey(clinician.getId(), patient.getId());

        TravelTimeInfo cached = travelTimeCache.get(cacheKey);
        if (cached != null && isRecentlyUpdated(cached.getLastUpdated())) {
            return cached.getCurrentTravelTime();
        }

        return fetchRealTimeTravelTime(clinician, patient);
    }    /**
     * Fetch real-time travel time from external traffic API.
     */
    private Duration fetchRealTimeTravelTime(Clinician clinician, Patient patient) {
        try {
            // Get coordinates from clinician and patient
            Double clinicianLat = getLatitude(clinician);
            Double clinicianLon = getLongitude(clinician);
            Double patientLat = getLatitude(patient);
            Double patientLon = getLongitude(patient);
            
            // Check if coordinates are available
            if (clinicianLat == null || clinicianLon == null || patientLat == null || patientLon == null) {
                log.warn("Missing coordinates for clinician {} or patient {}, using baseline", 
                        clinician.getId(), patient.getId());
                return getBaselineTravelTime(clinician, patient);
            }
            
            // Google Maps Distance Matrix API integration
            String url = buildDistanceMatrixUrl(clinicianLat, clinicianLon, patientLat, patientLon);

            GoogleMapsResponse response = restTemplate.getForObject(url, GoogleMapsResponse.class);
            
            if (response != null && response.isValid()) {
                Duration travelTime = Duration.ofSeconds(response.getDurationInSeconds());
                
                // Cache the result
                String cacheKey = generateCacheKey(clinician.getId(), patient.getId());
                Duration baselineTime = getBaselineTravelTime(clinician, patient);
                
                travelTimeCache.put(cacheKey, TravelTimeInfo.builder()
                        .currentTravelTime(travelTime)
                        .baselineTravelTime(baselineTime)
                        .trafficFactor(calculateTrafficFactor(travelTime, baselineTime))
                        .lastUpdated(LocalDateTime.now())
                        .build());

                // Check for significant delays and trigger events
                checkForTrafficDelays(clinician, patient, travelTime);

                return travelTime;
            }
        } catch (Exception e) {
            log.warn("Failed to fetch real-time travel time: {}", e.getMessage());
        }

        return getBaselineTravelTime(clinician, patient);
    }

    /**
     * Update travel time matrix for all active clinicians and patients.
     */
    public CompletableFuture<Void> updateTravelTimeMatrix(List<Clinician> clinicians, List<Patient> patients) {
        return CompletableFuture.runAsync(() -> {
            log.info("Updating travel time matrix for {} clinicians and {} patients", 
                    clinicians.size(), patients.size());

            List<CompletableFuture<Void>> updateTasks = new ArrayList<>();

            for (Clinician clinician : clinicians) {
                for (Patient patient : patients) {
                    updateTasks.add(CompletableFuture.runAsync(() -> 
                            getRealTimeTravelTime(clinician, patient)));
                }
            }

            CompletableFuture.allOf(updateTasks.toArray(new CompletableFuture[0])).join();
            log.info("Travel time matrix update completed");
        });
    }

    /**
     * Check for significant traffic delays and create scheduling events.
     */
    private void checkForTrafficDelays(Clinician clinician, Patient patient, Duration currentTravelTime) {
        Duration baselineTime = getBaselineTravelTime(clinician, patient);
        long delayMinutes = currentTravelTime.minus(baselineTime).toMinutes();

        if (delayMinutes > maxDelayThresholdMinutes) {
            log.warn("Significant traffic delay detected: {} minutes for clinician {} to patient {}", 
                    delayMinutes, clinician.getId(), patient.getId());

            // This would trigger a scheduling event in a real implementation
            // createTrafficDelayEvent(clinician, patient, delayMinutes);
        }
    }    /**
     * Get baseline travel time (without traffic consideration).
     */
    private Duration getBaselineTravelTime(Clinician clinician, Patient patient) {
        // Get coordinates
        Double clinicianLat = getLatitude(clinician);
        Double clinicianLon = getLongitude(clinician);
        Double patientLat = getLatitude(patient);
        Double patientLon = getLongitude(patient);
        
        // If coordinates are missing, use a default time
        if (clinicianLat == null || clinicianLon == null || patientLat == null || patientLon == null) {
            log.debug("Missing coordinates, using default travel time");
            return Duration.ofMinutes(30); // Default 30 minutes
        }
        
        // Calculate straight-line distance and apply average speed
        double distance = calculateHaversineDistance(clinicianLat, clinicianLon, patientLat, patientLon);

        // Assume average urban speed of 30 km/h
        double timeHours = distance / 30.0;
        return Duration.ofMinutes((long) (timeHours * 60));
    }

    /**
     * Calculate Haversine distance between two geographic points.
     */
    private double calculateHaversineDistance(double lat1, double lon1, double lat2, double lon2) {
        final int R = 6371; // Radius of the Earth in kilometers

        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * Build Google Maps Distance Matrix API URL.
     */
    private String buildDistanceMatrixUrl(double originLat, double originLon, 
                                        double destLat, double destLon) {
        return String.format(
                "https://maps.googleapis.com/maps/api/distancematrix/json?" +
                "origins=%f,%f&destinations=%f,%f&departure_time=now&traffic_model=best_guess&key=%s",
                originLat, originLon, destLat, destLon, googleMapsApiKey
        );
    }

    /**
     * Generate cache key for travel time lookup.
     */
    private String generateCacheKey(String clinicianId, String patientId) {
        return String.format("%s_%s", clinicianId, patientId);
    }

    /**
     * Check if travel time data is recently updated.
     */
    private boolean isRecentlyUpdated(LocalDateTime lastUpdated) {
        return lastUpdated.isAfter(LocalDateTime.now().minusMinutes(updateIntervalMinutes));
    }

    /**
     * Calculate traffic factor (current time / baseline time).
     */
    private double calculateTrafficFactor(Duration currentTime, Duration baselineTime) {
        if (baselineTime.isZero()) return 1.0;
        return (double) currentTime.toMinutes() / baselineTime.toMinutes();
    }

    /**
     * Get current traffic conditions for a region.
     */
    public TrafficCondition getRegionTrafficCondition(String region) {
        return regionTrafficConditions.getOrDefault(region, TrafficCondition.NORMAL);
    }

    /**
     * Update traffic conditions for a region.
     */
    public void updateRegionTrafficCondition(String region, TrafficCondition condition) {
        regionTrafficConditions.put(region, condition);
        log.info("Updated traffic condition for region {}: {}", region, condition);
        
        // Invalidate cache for this region
        travelTimeCache.entrySet().removeIf(entry -> entry.getKey().contains(region));
    }

    /**
     * Get travel time matrix for optimization.
     */
    public Map<String, Duration> getTravelTimeMatrix() {
        Map<String, Duration> matrix = new HashMap<>();
        travelTimeCache.forEach((key, info) -> matrix.put(key, info.getCurrentTravelTime()));
        return matrix;
    }

    /**
     * Clear travel time cache (for testing or reset).
     */
    public void clearCache() {
        travelTimeCache.clear();
        log.info("Travel time cache cleared");
    }

    // =========================================================================
    // INNER CLASSES
    // =========================================================================

    /**
     * Travel time information with caching metadata.
     */
    public static class TravelTimeInfo {
        private Duration currentTravelTime;
        private Duration baselineTravelTime;
        private double trafficFactor;
        private LocalDateTime lastUpdated;

        public static TravelTimeInfoBuilder builder() {
            return new TravelTimeInfoBuilder();
        }

        // Getters
        public Duration getCurrentTravelTime() { return currentTravelTime; }
        public Duration getBaselineTravelTime() { return baselineTravelTime; }
        public double getTrafficFactor() { return trafficFactor; }
        public LocalDateTime getLastUpdated() { return lastUpdated; }

        public static class TravelTimeInfoBuilder {
            private Duration currentTravelTime;
            private Duration baselineTravelTime;
            private double trafficFactor;
            private LocalDateTime lastUpdated;

            public TravelTimeInfoBuilder currentTravelTime(Duration currentTravelTime) {
                this.currentTravelTime = currentTravelTime;
                return this;
            }

            public TravelTimeInfoBuilder baselineTravelTime(Duration baselineTravelTime) {
                this.baselineTravelTime = baselineTravelTime;
                return this;
            }

            public TravelTimeInfoBuilder trafficFactor(double trafficFactor) {
                this.trafficFactor = trafficFactor;
                return this;
            }

            public TravelTimeInfoBuilder lastUpdated(LocalDateTime lastUpdated) {
                this.lastUpdated = lastUpdated;
                return this;
            }

            public TravelTimeInfo build() {
                TravelTimeInfo info = new TravelTimeInfo();
                info.currentTravelTime = this.currentTravelTime;
                info.baselineTravelTime = this.baselineTravelTime;
                info.trafficFactor = this.trafficFactor;
                info.lastUpdated = this.lastUpdated;
                return info;
            }
        }
    }

    /**
     * Google Maps API response structure
     */
    private static class GoogleMapsResponse {
        private Row[] rows;

        public boolean isValid() {
            return rows != null && rows.length > 0 && 
                   rows[0].elements != null && rows[0].elements.length > 0 &&
                   "OK".equals(rows[0].elements[0].status);
        }

        public long getDurationInSeconds() {
            if (isValid()) {
                return rows[0].elements[0].duration_in_traffic != null ? 
                       rows[0].elements[0].duration_in_traffic.value :
                       rows[0].elements[0].duration.value;
            }
            return 0;
        }

        private static class Row {
            private Element[] elements;
        }

        private static class Element {
            private String status;
            private DurationInfo duration;
            private DurationInfo duration_in_traffic;
        }

        private static class DurationInfo {
            private long value; // in seconds
        }
    }

    /**
     * Extract latitude from clinician location.
     */
    private Double getLatitude(Clinician clinician) {
        if (clinician.getLocation() != null && clinician.getLocation().getCoordinates() != null) {
            return clinician.getLocation().getCoordinates().get("latitude");
        }
        return null;
    }
    
    /**
     * Extract longitude from clinician location.
     */
    private Double getLongitude(Clinician clinician) {
        if (clinician.getLocation() != null && clinician.getLocation().getCoordinates() != null) {
            return clinician.getLocation().getCoordinates().get("longitude");
        }
        return null;
    }
    
    /**
     * Extract latitude from patient location.
     */
    private Double getLatitude(Patient patient) {
        if (patient.getLocation() != null && patient.getLocation().getCoordinates() != null) {
            return patient.getLocation().getCoordinates().get("latitude");
        }
        return null;
    }
    
    /**
     * Extract longitude from patient location.
     */
    private Double getLongitude(Patient patient) {
        if (patient.getLocation() != null && patient.getLocation().getCoordinates() != null) {
            return patient.getLocation().getCoordinates().get("longitude");
        }
        return null;
    }
}
