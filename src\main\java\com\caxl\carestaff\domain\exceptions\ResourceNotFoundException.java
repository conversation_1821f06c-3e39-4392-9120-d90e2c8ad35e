package com.caxl.carestaff.domain.exceptions;

/**
 * Exception thrown when a requested resource is not found.
 */
public class ResourceNotFoundException extends RuntimeException {

    public ResourceNotFoundException(String message) {
        super(message);
    }

    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public static ResourceNotFoundException serviceRequest(String requestId) {
        return new ResourceNotFoundException("Service request not found: " + requestId);
    }

    public static ResourceNotFoundException patient(String patientId) {
        return new ResourceNotFoundException("Patient not found: " + patientId);
    }

    public static ResourceNotFoundException careStaff(String careStaffId) {
        return new ResourceNotFoundException("Care staff not found: " + careStaffId);
    }

    public static ResourceNotFoundException matchSuggestion(String suggestionId) {
        return new ResourceNotFoundException("Match suggestion not found: " + suggestionId);
    }
}
