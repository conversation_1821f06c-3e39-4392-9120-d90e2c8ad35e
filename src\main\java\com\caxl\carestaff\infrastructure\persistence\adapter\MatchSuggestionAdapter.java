package com.caxl.carestaff.infrastructure.persistence.adapter;

import com.caxl.carestaff.application.port.out.persistence.MatchSuggestionPort;
import com.caxl.carestaff.domain.entities.MatchSuggestion;
import com.caxl.carestaff.infrastructure.persistence.entity.MatchSuggestionEntity;
import com.caxl.carestaff.infrastructure.persistence.repository.MatchSuggestionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Persistence adapter for match suggestion operations.
 * Implements the MatchSuggestionPort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class MatchSuggestionAdapter implements MatchSuggestionPort {

    private final MatchSuggestionRepository repository;

    @Override
    public List<MatchSuggestion> saveAll(List<MatchSuggestion> suggestions) {
        log.debug("Saving {} match suggestions", suggestions.size());
        
        List<MatchSuggestionEntity> entities = suggestions.stream()
                .map(this::mapToEntity)
                .toList();
        
        List<MatchSuggestionEntity> saved = repository.saveAll(entities);
        
        return saved.stream()
                .map(this::mapToDomain)
                .toList();
    }

    @Override
    public List<MatchSuggestion> findByServiceRequestId(UUID serviceRequestId) {
        log.debug("Finding match suggestions for service request: {}", serviceRequestId);
        
        return repository.findByServiceRequestIdAndIsActiveTrue(serviceRequestId)
                .stream()
                .map(this::mapToDomain)
                .toList();
    }

    @Override
    public List<MatchSuggestion> findAllById(List<UUID> suggestionIds) {
        log.debug("Finding match suggestions by IDs: {}", suggestionIds.size());
        
        return repository.findAllById(suggestionIds)
                .stream()
                .map(this::mapToDomain)
                .toList();
    }

    @Override
    public Optional<MatchSuggestion> findById(UUID suggestionId) {
        log.debug("Finding match suggestion by ID: {}", suggestionId);
        
        return repository.findById(suggestionId)
                .map(this::mapToDomain);
    }

    @Override
    public MatchSuggestion save(MatchSuggestion suggestion) {
        log.debug("Saving match suggestion: {}", suggestion.getSuggestionId());
        
        MatchSuggestionEntity entity = mapToEntity(suggestion);
        MatchSuggestionEntity saved = repository.save(entity);
        return mapToDomain(saved);
    }

    /**
     * Map domain entity to JPA entity.
     */
    private MatchSuggestionEntity mapToEntity(MatchSuggestion domain) {
        return MatchSuggestionEntity.builder()
                .suggestionId(domain.getSuggestionId())
                .serviceRequestId(domain.getServiceRequestId())
                .suggestedCareStaffId(domain.getSuggestedCareStaffId())
                .score(domain.getScore())
                .rationale(domain.getRationale())
                .suggestedDateTime(domain.getSuggestedDateTime())
                .createdAt(domain.getCreatedAt())
                .isActive(domain.isActive())
                .build();
    }

    /**
     * Map JPA entity to domain entity.
     */
    private MatchSuggestion mapToDomain(MatchSuggestionEntity entity) {
        return MatchSuggestion.builder()
                .suggestionId(entity.getSuggestionId())
                .serviceRequestId(entity.getServiceRequestId())
                .suggestedCareStaffId(entity.getSuggestedCareStaffId())
                .score(entity.getScore())
                .rationale(entity.getRationale())
                .suggestedDateTime(entity.getSuggestedDateTime())
                .createdAt(entity.getCreatedAt())
                .isActive(entity.getIsActive())
                .build();
    }
}
