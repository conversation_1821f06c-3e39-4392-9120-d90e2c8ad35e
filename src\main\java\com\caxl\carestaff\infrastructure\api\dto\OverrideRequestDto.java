package com.caxl.carestaff.infrastructure.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * DTO for match override requests.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OverrideRequestDto {

    @NotNull(message = "Selected care staff ID is required")
    @JsonProperty("selected_carestaff_id")
    private UUID selectedCareStaffId;

    @JsonProperty("reason")
    private String reason;

    @JsonProperty("explanation")
    private String explanation;
}
