package com.caxl.assignment.domain.services;

import com.caxl.assignment.domain.entities.MatchSuggestion;
import com.caxl.assignment.domain.entities.ServiceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Domain service for detecting conflicts in bulk assignment operations.
 * Implements comprehensive conflict detection logic for scheduling conflicts.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConflictDetectionService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Detect conflicts in a list of match suggestions for bulk assignment.
     */
    public ConflictDetectionResult detectConflicts(List<MatchSuggestion> suggestions,
                                                  Map<UUID, ServiceRequest> serviceRequests) {
        
        log.info("Detecting conflicts for {} suggestions", suggestions.size());

        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> validSuggestionIds = new HashSet<>();
        Set<UUID> conflictedSuggestionIds = new HashSet<>();

        // 1. Check for staff double-booking conflicts
        Map<UUID, List<MatchSuggestion>> staffSuggestions = groupSuggestionsByStaff(suggestions);
        
        for (Map.Entry<UUID, List<MatchSuggestion>> entry : staffSuggestions.entrySet()) {
            UUID staffId = entry.getKey();
            List<MatchSuggestion> staffSuggestionList = entry.getValue();
            
            StaffConflictResult staffConflicts = detectStaffConflicts(staffId, staffSuggestionList, serviceRequests);
            conflicts.addAll(staffConflicts.conflicts());
            conflictedSuggestionIds.addAll(staffConflicts.conflictedSuggestionIds());
        }

        // 2. Check for patient double-booking conflicts
        Map<UUID, List<MatchSuggestion>> patientSuggestions = groupSuggestionsByPatient(suggestions, serviceRequests);
        
        for (Map.Entry<UUID, List<MatchSuggestion>> entry : patientSuggestions.entrySet()) {
            UUID patientId = entry.getKey();
            List<MatchSuggestion> patientSuggestionList = entry.getValue();
            
            PatientConflictResult patientConflicts = detectPatientConflicts(patientId, patientSuggestionList, serviceRequests);
            conflicts.addAll(patientConflicts.conflicts());
            conflictedSuggestionIds.addAll(patientConflicts.conflictedSuggestionIds());
        }

        // 3. Check for service request status conflicts
        ServiceRequestConflictResult statusConflicts = detectServiceRequestStatusConflicts(suggestions, serviceRequests);
        conflicts.addAll(statusConflicts.conflicts());
        conflictedSuggestionIds.addAll(statusConflicts.conflictedSuggestionIds());

        // 4. Check for resource capacity conflicts
        ResourceConflictResult resourceConflicts = detectResourceConflicts(suggestions, serviceRequests);
        conflicts.addAll(resourceConflicts.conflicts());
        conflictedSuggestionIds.addAll(resourceConflicts.conflictedSuggestionIds());

        // 5. Check for business rule conflicts
        BusinessRuleConflictResult businessConflicts = detectBusinessRuleConflicts(suggestions, serviceRequests);
        conflicts.addAll(businessConflicts.conflicts());
        conflictedSuggestionIds.addAll(businessConflicts.conflictedSuggestionIds());

        // Determine valid suggestions (those without conflicts)
        validSuggestionIds = suggestions.stream()
                .map(MatchSuggestion::getSuggestionId)
                .filter(id -> !conflictedSuggestionIds.contains(id))
                .collect(Collectors.toSet());

        log.info("Conflict detection completed: {} valid, {} conflicted, {} total conflicts", 
                validSuggestionIds.size(), conflictedSuggestionIds.size(), conflicts.size());

        return new ConflictDetectionResult(
                conflicts,
                validSuggestionIds,
                conflictedSuggestionIds,
                suggestions.size(),
                validSuggestionIds.size(),
                conflictedSuggestionIds.size()
        );
    }

    /**
     * Detect conflicts for a specific staff member.
     */
    private StaffConflictResult detectStaffConflicts(UUID staffId, 
                                                   List<MatchSuggestion> staffSuggestions,
                                                   Map<UUID, ServiceRequest> serviceRequests) {
        
        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> conflictedIds = new HashSet<>();

        // Sort suggestions by suggested time
        List<MatchSuggestion> sortedSuggestions = staffSuggestions.stream()
                .sorted(Comparator.comparing(MatchSuggestion::getSuggestedDateTime))
                .toList();

        // Check for overlapping suggestions
        for (int i = 0; i < sortedSuggestions.size() - 1; i++) {
            MatchSuggestion current = sortedSuggestions.get(i);
            MatchSuggestion next = sortedSuggestions.get(i + 1);
            
            ServiceRequest currentRequest = serviceRequests.get(current.getServiceRequestId());
            ServiceRequest nextRequest = serviceRequests.get(next.getServiceRequestId());
            
            if (currentRequest != null && nextRequest != null) {
                LocalDateTime currentEnd = current.getSuggestedDateTime()
                        .plusMinutes(currentRequest.getTimeWindow().getVisitDurationMinutes());
                
                if (currentEnd.isAfter(next.getSuggestedDateTime())) {
                    ConflictInfo conflict = new ConflictInfo(
                            ConflictType.STAFF_DOUBLE_BOOKING,
                            String.format("Staff %s has overlapping appointments", staffId),
                            List.of(current.getSuggestionId(), next.getSuggestionId()),
                            ConflictSeverity.HIGH
                    );
                    conflicts.add(conflict);
                    conflictedIds.add(current.getSuggestionId());
                    conflictedIds.add(next.getSuggestionId());
                }
            }
        }

        // Check for conflicts with existing appointments
        for (MatchSuggestion suggestion : staffSuggestions) {
            ServiceRequest request = serviceRequests.get(suggestion.getServiceRequestId());
            if (request != null) {
                List<ExistingAppointment> existingConflicts = findExistingAppointmentConflicts(
                        staffId, suggestion.getSuggestedDateTime(), request.getTimeWindow().getVisitDurationMinutes());
                
                if (!existingConflicts.isEmpty()) {
                    ConflictInfo conflict = new ConflictInfo(
                            ConflictType.EXISTING_APPOINTMENT_CONFLICT,
                            String.format("Staff %s has existing appointment conflicts", staffId),
                            List.of(suggestion.getSuggestionId()),
                            ConflictSeverity.HIGH
                    );
                    conflicts.add(conflict);
                    conflictedIds.add(suggestion.getSuggestionId());
                }
            }
        }

        return new StaffConflictResult(conflicts, conflictedIds);
    }

    /**
     * Detect conflicts for a specific patient.
     */
    private PatientConflictResult detectPatientConflicts(UUID patientId,
                                                        List<MatchSuggestion> patientSuggestions,
                                                        Map<UUID, ServiceRequest> serviceRequests) {
        
        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> conflictedIds = new HashSet<>();

        // Check for multiple appointments for same patient at overlapping times
        if (patientSuggestions.size() > 1) {
            List<MatchSuggestion> sortedSuggestions = patientSuggestions.stream()
                    .sorted(Comparator.comparing(MatchSuggestion::getSuggestedDateTime))
                    .toList();

            for (int i = 0; i < sortedSuggestions.size() - 1; i++) {
                MatchSuggestion current = sortedSuggestions.get(i);
                MatchSuggestion next = sortedSuggestions.get(i + 1);
                
                ServiceRequest currentRequest = serviceRequests.get(current.getServiceRequestId());
                if (currentRequest != null) {
                    LocalDateTime currentEnd = current.getSuggestedDateTime()
                            .plusMinutes(currentRequest.getTimeWindow().getVisitDurationMinutes());
                    
                    // Patients should have at least 2 hours between visits
                    if (currentEnd.plusHours(2).isAfter(next.getSuggestedDateTime())) {
                        ConflictInfo conflict = new ConflictInfo(
                                ConflictType.PATIENT_DOUBLE_BOOKING,
                                String.format("Patient %s has appointments too close together", patientId),
                                List.of(current.getSuggestionId(), next.getSuggestionId()),
                                ConflictSeverity.MEDIUM
                        );
                        conflicts.add(conflict);
                        conflictedIds.add(next.getSuggestionId()); // Keep the earlier one
                    }
                }
            }
        }

        return new PatientConflictResult(conflicts, conflictedIds);
    }

    /**
     * Detect service request status conflicts.
     */
    private ServiceRequestConflictResult detectServiceRequestStatusConflicts(
            List<MatchSuggestion> suggestions,
            Map<UUID, ServiceRequest> serviceRequests) {
        
        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> conflictedIds = new HashSet<>();

        for (MatchSuggestion suggestion : suggestions) {
            ServiceRequest request = serviceRequests.get(suggestion.getServiceRequestId());
            
            if (request == null) {
                ConflictInfo conflict = new ConflictInfo(
                        ConflictType.SERVICE_REQUEST_NOT_FOUND,
                        String.format("Service request %s not found", suggestion.getServiceRequestId()),
                        List.of(suggestion.getSuggestionId()),
                        ConflictSeverity.HIGH
                );
                conflicts.add(conflict);
                conflictedIds.add(suggestion.getSuggestionId());
            } else if (!"pending".equalsIgnoreCase(request.getStatus())) {
                ConflictInfo conflict = new ConflictInfo(
                        ConflictType.INVALID_SERVICE_REQUEST_STATUS,
                        String.format("Service request %s has status '%s', expected 'pending'", 
                                     request.getRequestId(), request.getStatus()),
                        List.of(suggestion.getSuggestionId()),
                        ConflictSeverity.HIGH
                );
                conflicts.add(conflict);
                conflictedIds.add(suggestion.getSuggestionId());
            }
        }

        return new ServiceRequestConflictResult(conflicts, conflictedIds);
    }

    /**
     * Detect resource capacity conflicts.
     */
    private ResourceConflictResult detectResourceConflicts(
            List<MatchSuggestion> suggestions,
            Map<UUID, ServiceRequest> serviceRequests) {
        
        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> conflictedIds = new HashSet<>();

        // Check for daily capacity limits per staff
        Map<UUID, Map<String, Integer>> staffDailyLoad = new HashMap<>();
        
        for (MatchSuggestion suggestion : suggestions) {
            UUID staffId = suggestion.getSuggestedCareStaffId();
            String date = suggestion.getSuggestedDateTime().toLocalDate().toString();
            
            staffDailyLoad.computeIfAbsent(staffId, k -> new HashMap<>())
                         .merge(date, 1, Integer::sum);
        }

        // Check against daily limits (assume max 8 appointments per day)
        for (Map.Entry<UUID, Map<String, Integer>> staffEntry : staffDailyLoad.entrySet()) {
            UUID staffId = staffEntry.getKey();
            
            for (Map.Entry<String, Integer> dayEntry : staffEntry.getValue().entrySet()) {
                String date = dayEntry.getKey();
                int appointmentCount = dayEntry.getValue();
                
                if (appointmentCount > 8) {
                    // Find suggestions for this staff on this day
                    List<UUID> affectedSuggestions = suggestions.stream()
                            .filter(s -> s.getSuggestedCareStaffId().equals(staffId) &&
                                        s.getSuggestedDateTime().toLocalDate().toString().equals(date))
                            .map(MatchSuggestion::getSuggestionId)
                            .toList();
                    
                    ConflictInfo conflict = new ConflictInfo(
                            ConflictType.DAILY_CAPACITY_EXCEEDED,
                            String.format("Staff %s exceeds daily capacity on %s (%d appointments)", 
                                         staffId, date, appointmentCount),
                            affectedSuggestions,
                            ConflictSeverity.MEDIUM
                    );
                    conflicts.add(conflict);
                    
                    // Mark excess appointments as conflicted (keep first 8)
                    affectedSuggestions.stream().skip(8).forEach(conflictedIds::add);
                }
            }
        }

        return new ResourceConflictResult(conflicts, conflictedIds);
    }

    /**
     * Detect business rule conflicts.
     */
    private BusinessRuleConflictResult detectBusinessRuleConflicts(
            List<MatchSuggestion> suggestions,
            Map<UUID, ServiceRequest> serviceRequests) {
        
        List<ConflictInfo> conflicts = new ArrayList<>();
        Set<UUID> conflictedIds = new HashSet<>();

        for (MatchSuggestion suggestion : suggestions) {
            ServiceRequest request = serviceRequests.get(suggestion.getServiceRequestId());
            
            if (request != null) {
                // Check if suggested time is within service request time window
                if (suggestion.getSuggestedDateTime().isBefore(request.getTimeWindow().getArrivalWindowStart()) ||
                    suggestion.getSuggestedDateTime().isAfter(request.getTimeWindow().getArrivalWindowEnd())) {
                    
                    ConflictInfo conflict = new ConflictInfo(
                            ConflictType.TIME_WINDOW_VIOLATION,
                            String.format("Suggested time %s is outside request time window", 
                                         suggestion.getSuggestedDateTime()),
                            List.of(suggestion.getSuggestionId()),
                            ConflictSeverity.HIGH
                    );
                    conflicts.add(conflict);
                    conflictedIds.add(suggestion.getSuggestionId());
                }

                // Check for weekend restrictions (example business rule)
                if (suggestion.getSuggestedDateTime().getDayOfWeek().getValue() >= 6) { // Saturday or Sunday
                    if (request.getPriority() > 2) { // Only high priority on weekends
                        ConflictInfo conflict = new ConflictInfo(
                                ConflictType.WEEKEND_RESTRICTION,
                                "Non-urgent appointments not allowed on weekends",
                                List.of(suggestion.getSuggestionId()),
                                ConflictSeverity.LOW
                        );
                        conflicts.add(conflict);
                        conflictedIds.add(suggestion.getSuggestionId());
                    }
                }
            }
        }

        return new BusinessRuleConflictResult(conflicts, conflictedIds);
    }

    // Helper methods
    private Map<UUID, List<MatchSuggestion>> groupSuggestionsByStaff(List<MatchSuggestion> suggestions) {
        return suggestions.stream()
                .collect(Collectors.groupingBy(MatchSuggestion::getSuggestedCareStaffId));
    }

    private Map<UUID, List<MatchSuggestion>> groupSuggestionsByPatient(
            List<MatchSuggestion> suggestions, 
            Map<UUID, ServiceRequest> serviceRequests) {
        
        return suggestions.stream()
                .filter(s -> serviceRequests.containsKey(s.getServiceRequestId()))
                .collect(Collectors.groupingBy(s -> serviceRequests.get(s.getServiceRequestId()).getPatientId()));
    }

    private List<ExistingAppointment> findExistingAppointmentConflicts(
            UUID staffId, LocalDateTime suggestedTime, int durationMinutes) {
        
        String sql = """
            SELECT appointment_id, scheduled_start_time, scheduled_end_time
            FROM appointment
            WHERE carestaff_id = ?
            AND status IN ('scheduled', 'in_progress')
            AND (
                (scheduled_start_time <= ? AND scheduled_end_time > ?) OR
                (scheduled_start_time < ? AND scheduled_end_time >= ?) OR
                (scheduled_start_time >= ? AND scheduled_start_time < ?)
            )
            """;
        
        LocalDateTime suggestedEnd = suggestedTime.plusMinutes(durationMinutes);
        
        return jdbcTemplate.query(sql, 
                (rs, rowNum) -> new ExistingAppointment(
                        UUID.fromString(rs.getString("appointment_id")),
                        rs.getTimestamp("scheduled_start_time").toLocalDateTime(),
                        rs.getTimestamp("scheduled_end_time").toLocalDateTime()
                ),
                staffId, suggestedTime, suggestedTime, suggestedEnd, suggestedEnd, suggestedTime, suggestedEnd);
    }

    // Record classes for results
    public record ConflictDetectionResult(
            List<ConflictInfo> conflicts,
            Set<UUID> validSuggestionIds,
            Set<UUID> conflictedSuggestionIds,
            int totalSuggestions,
            int validCount,
            int conflictedCount
    ) {}

    public record ConflictInfo(
            ConflictType type,
            String description,
            List<UUID> affectedSuggestionIds,
            ConflictSeverity severity
    ) {}

    private record StaffConflictResult(List<ConflictInfo> conflicts, Set<UUID> conflictedSuggestionIds) {}
    private record PatientConflictResult(List<ConflictInfo> conflicts, Set<UUID> conflictedSuggestionIds) {}
    private record ServiceRequestConflictResult(List<ConflictInfo> conflicts, Set<UUID> conflictedSuggestionIds) {}
    private record ResourceConflictResult(List<ConflictInfo> conflicts, Set<UUID> conflictedSuggestionIds) {}
    private record BusinessRuleConflictResult(List<ConflictInfo> conflicts, Set<UUID> conflictedSuggestionIds) {}
    private record ExistingAppointment(UUID appointmentId, LocalDateTime startTime, LocalDateTime endTime) {}

    public enum ConflictType {
        STAFF_DOUBLE_BOOKING,
        PATIENT_DOUBLE_BOOKING,
        EXISTING_APPOINTMENT_CONFLICT,
        SERVICE_REQUEST_NOT_FOUND,
        INVALID_SERVICE_REQUEST_STATUS,
        DAILY_CAPACITY_EXCEEDED,
        TIME_WINDOW_VIOLATION,
        WEEKEND_RESTRICTION
    }

    public enum ConflictSeverity {
        LOW, MEDIUM, HIGH
    }
}
