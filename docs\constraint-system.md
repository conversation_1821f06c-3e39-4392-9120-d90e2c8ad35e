# Constraint System - CareStaff Matching Service

## Overview

The CareStaff Matching Service uses a sophisticated two-tier constraint system that separates mandatory requirements (hard constraints) from optimization preferences (soft constraints). This system is fully configurable via JSON stored in the database.

## Constraint Architecture

### Two-Tier System

```
Constraint System
├── Hard Constraints (Filters)
│   ├── Must be satisfied for any match
│   ├── Boolean pass/fail evaluation
│   └── Eliminates incompatible candidates
└── Soft Constraints (Scoring Weights)
    ├── Used for optimization and ranking
    ├── Numeric scoring with configurable weights
    └── Determines match quality and order
```

### Configuration Storage

```sql
-- Configuration table structure
CREATE TABLE matching_configuration (
    config_id UUID PRIMARY KEY,
    config_name VARCHAR(255) NOT NULL,
    criteria_json JSONB NOT NULL,
    is_active BOOLEAN DEFAULT false,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Hard Constraints (Filters)

Hard constraints are mandatory requirements that must be satisfied for a carestaff to be considered for a match.

### 1. Skill Matching Requirements

#### `requiredSkillsMustMatchAll`
- **Type**: Boolean
- **Default**: `false`
- **Purpose**: Determines skill matching strictness

**When `true` (Strict Matching):**
```java
// Staff must have ALL required skills
boolean hasAllSkills = serviceRequest.getRequiredSkillIds().stream()
        .allMatch(skillId -> staff.skillIds().contains(skillId));
if (!hasAllSkills) {
    return false; // Reject match
}
```

**When `false` (Flexible Matching):**
```java
// Staff must have at least ONE required skill
boolean hasAnySkill = serviceRequest.getRequiredSkillIds().stream()
        .anyMatch(skillId -> staff.skillIds().contains(skillId));
if (!hasAnySkill) {
    return false; // Reject match
}
```

**Use Cases:**
- **Strict mode**: Critical care, specialized procedures
- **Flexible mode**: General care, skill shortage areas

### 2. Geographic Constraints

#### `proximitySearchRadiusKm`
- **Type**: Double
- **Default**: `50.0`
- **Range**: 1.0 - 500.0
- **Purpose**: Maximum search distance for candidates

```java
// PostGIS spatial query with radius constraint
SELECT carestaff_id FROM caresstaff_pe cs
JOIN location l ON cs.location_id = l.location_id
WHERE ST_DWithin(l.coordinates, 
                 ST_SetSRID(ST_Point(:patientLon, :patientLat), 4326), 
                 :radiusMeters)
```

#### `mustRespectGeoServiceArea`
- **Type**: Boolean
- **Default**: `true`
- **Purpose**: Enforce geofence service area boundaries

```java
if (criteria.getFilters().isMustRespectGeoServiceArea()) {
    List<Polygon> serviceGeofences = careStaffPort.findServiceGeofencesForStaff(
            staff.careStaffId(), 
            criteria.getGeography().getStaffServiceGeofenceTypes()
    );
    
    boolean withinServiceArea = serviceGeofences.stream()
            .anyMatch(geofence -> 
                criteria.getGeography().isGeofenceStrictContainmentOnly() 
                    ? geofence.contains(patient.locationCoordinates())
                    : geofence.intersects(patient.locationCoordinates())
            );
    
    if (!withinServiceArea) {
        return false; // Outside service area
    }
}
```

### 3. Availability Constraints

#### `mustRespectAvailability`
- **Type**: Boolean
- **Default**: `true`
- **Purpose**: Prevent scheduling conflicts

```java
if (criteria.getFilters().isMustRespectAvailability()) {
    List<Appointment> overlappingAppointments = careStaffPort.findOverlappingAppointments(
            staff.careStaffId(), 
            serviceRequest.getTimeWindow(), 
            criteria.getAvailability().getOverlapThresholdMinutes()
    );
    
    if (!overlappingAppointments.isEmpty()) {
        return false; // Staff unavailable
    }
}
```

### 4. Patient Preference Constraints

#### `mustNotBeBarred`
- **Type**: Boolean
- **Default**: `true`
- **Purpose**: Respect patient's barred staff list

```java
if (criteria.getFilters().isMustNotBeBarred()) {
    if (patient.barredCareStaffIds() != null && 
        patient.barredCareStaffIds().contains(staff.careStaffId())) {
        return false; // Staff is barred by patient
    }
}
```

### 5. Quality Thresholds

#### `minScoreThreshold`
- **Type**: Double
- **Default**: `0.0`
- **Range**: -100.0 - 100.0
- **Purpose**: Minimum acceptable match quality

```java
if (scoreResult.score() < criteria.getFilters().getMinScoreThreshold()) {
    continue; // Skip low-quality matches
}
```

### 6. Experience Requirements

#### Implicit Experience Constraints
```java
// Minimum experience for high-priority cases
if (serviceRequest.getPriority() <= 2 && staff.experienceYears() < 2) {
    return false; // Insufficient experience for critical cases
}
```

### 7. Certification Requirements

#### Required Certifications Validation
```java
if (serviceRequest.getRequiredCertificationIds() != null && 
    !serviceRequest.getRequiredCertificationIds().isEmpty()) {
    
    boolean hasCertifications = serviceRequest.getRequiredCertificationIds().stream()
            .allMatch(certId -> staff.certificationIds().contains(certId));
    
    if (!hasCertifications) {
        return false; // Missing required certifications
    }
}
```

## Soft Constraints (Scoring Weights)

Soft constraints are used for optimization and ranking. They don't eliminate candidates but influence match quality scores.

### 1. Skill-Related Scoring

#### `skillMatchBonusPerRequiredSkill`
- **Default**: `10.0`
- **Purpose**: Points per matched required skill

```java
long matchedSkills = serviceRequest.getRequiredSkillIds().stream()
        .mapToLong(skillId -> staff.skillIds().contains(skillId) ? 1 : 0)
        .sum();

double skillBonus = matchedSkills * weights.getSkillMatchBonusPerRequiredSkill();
score += skillBonus;
```

#### `skillMatchAllBonus`
- **Default**: `50.0`
- **Purpose**: Bonus when staff has ALL required skills

```java
if (matchedSkills == serviceRequest.getRequiredSkillIds().size()) {
    score += weights.getSkillMatchAllBonus();
}
```

### 2. Geographic Scoring

#### `proximityKmPenalty`
- **Default**: `-1.0`
- **Purpose**: Penalty per kilometer distance

```java
double distance = staff.baseLocation().distance(patientLocationCoords) * 111.0; // Convert to km
double proximityPenalty = distance * weights.getProximityKmPenalty();
score += proximityPenalty;
```

#### `geoServiceAreaBonus`
- **Default**: `20.0`
- **Purpose**: Bonus for working within designated service area

```java
if (withinServiceArea) {
    score += weights.getGeoServiceAreaBonus();
}
```

### 3. Relationship Scoring

#### `preferredCareStaffBonus`
- **Default**: `15.0`
- **Purpose**: Bonus for patient's preferred staff

```java
if (patient.preferredCareStaffIds() != null && 
    patient.preferredCareStaffIds().contains(staff.careStaffId())) {
    score += weights.getPreferredCareStaffBonus();
}
```

#### `continuityBonusPerRecentVisit`
- **Default**: `5.0`
- **Purpose**: Multiplier for continuity of care score

```java
ContinuityOfCareService.ContinuityScore continuityScore = 
        continuityOfCareService.calculateContinuityScore(
                staff.careStaffId(), patient.patientId(), patient, 30);

double continuityBonus = continuityScore.score() * weights.getContinuityBonusPerRecentVisit();
score += continuityBonus;
```

### 4. Communication Scoring

#### `languageMatchBonus`
- **Default**: `8.0`
- **Purpose**: Bonus for matching patient's preferred language

```java
if (patient.preferredLanguage() != null && 
    staff.languages().contains(patient.preferredLanguage())) {
    score += weights.getLanguageMatchBonus();
}
```

### 5. Experience Scoring

#### `experienceLevelBonusPerYear`
- **Default**: `2.0`
- **Purpose**: Bonus per year of staff experience

```java
double experienceBonus = staff.experienceYears() * weights.getExperienceLevelBonusPerYear();
score += experienceBonus;
```

### 6. Availability Optimization

#### `availabilityWindowFitPenaltyPerMinuteDeviation`
- **Default**: `-0.5`
- **Purpose**: Penalty for suboptimal scheduling

```java
AvailabilityWindowScoringService.AvailabilityWindowScore availabilityScore = 
        availabilityWindowScoringService.calculateAvailabilityScore(
                staff.careStaffId(), serviceRequest, criteria.getAvailability(), weights);
score += availabilityScore.score();
```

## Configuration Examples

### Example 1: Rural Area Configuration

For areas with limited staff availability:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 100.0,
    "mustRespectGeoServiceArea": false,
    "mustRespectAvailability": true,
    "mustNotBeBarred": true,
    "minScoreThreshold": -10.0
  },
  "scoring_weights": {
    "skillMatchBonusPerRequiredSkill": 15.0,
    "proximityKmPenalty": -0.5,
    "preferredCareStaffBonus": 20.0,
    "continuityBonusPerRecentVisit": 10.0,
    "experienceLevelBonusPerYear": 3.0
  }
}
```

**Rationale:**
- Flexible skill matching due to limited staff
- Larger search radius for rural coverage
- Relaxed geographic constraints
- Lower score threshold to ensure matches
- Reduced distance penalty
- Higher continuity bonus to maintain relationships

### Example 2: Urban High-Density Configuration

For urban areas with abundant staff:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": true,
    "proximitySearchRadiusKm": 15.0,
    "mustRespectGeoServiceArea": true,
    "mustRespectAvailability": true,
    "mustNotBeBarred": true,
    "minScoreThreshold": 25.0
  },
  "scoring_weights": {
    "skillMatchAllBonus": 75.0,
    "proximityKmPenalty": -2.0,
    "geoServiceAreaBonus": 30.0,
    "languageMatchBonus": 15.0,
    "experienceLevelBonusPerYear": 4.0
  }
}
```

**Rationale:**
- Strict skill matching with abundant options
- Smaller search radius for efficiency
- Strict geographic boundaries
- Higher quality threshold
- Strong distance penalty for urban efficiency
- Emphasis on language matching in diverse areas

### Example 3: Emergency/Critical Care Configuration

For urgent, high-priority cases:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 75.0,
    "mustRespectGeoServiceArea": false,
    "mustRespectAvailability": false,
    "mustNotBeBarred": false,
    "minScoreThreshold": 0.0
  },
  "scoring_weights": {
    "skillMatchBonusPerRequiredSkill": 20.0,
    "proximityKmPenalty": -3.0,
    "experienceLevelBonusPerYear": 10.0,
    "skillMatchAllBonus": 100.0
  }
}
```

**Rationale:**
- Relaxed constraints for emergency situations
- Allows availability conflicts (can reschedule other appointments)
- Ignores patient preferences in emergencies
- Strong emphasis on skills and experience
- Heavy distance penalty for quick response

## Constraint Validation

### JSON Schema Validation

The system validates configuration JSON against a predefined schema:

```json
{
  "type": "object",
  "properties": {
    "filters": {
      "type": "object",
      "properties": {
        "requiredSkillsMustMatchAll": {"type": "boolean"},
        "proximitySearchRadiusKm": {"type": "number", "minimum": 1.0, "maximum": 500.0},
        "mustRespectGeoServiceArea": {"type": "boolean"},
        "mustRespectAvailability": {"type": "boolean"},
        "mustNotBeBarred": {"type": "boolean"},
        "minScoreThreshold": {"type": "number", "minimum": -100.0, "maximum": 100.0}
      },
      "required": ["proximitySearchRadiusKm"]
    },
    "scoring_weights": {
      "type": "object",
      "properties": {
        "skillMatchBonusPerRequiredSkill": {"type": "number"},
        "proximityKmPenalty": {"type": "number"},
        "preferredCareStaffBonus": {"type": "number"}
      }
    }
  },
  "required": ["filters", "scoring_weights"]
}
```

### Runtime Validation

```java
// Validate configuration on load
public void validateConfiguration(MatchingCriteria criteria) {
    if (criteria.getFilters().getProximitySearchRadiusKm() <= 0) {
        throw new ConfigurationException("Search radius must be positive");
    }
    
    if (criteria.getFilters().getMinScoreThreshold() < -100 || 
        criteria.getFilters().getMinScoreThreshold() > 100) {
        throw new ConfigurationException("Score threshold must be between -100 and 100");
    }
}
```

## Performance Impact

### Hard Constraint Performance

Hard constraints are applied early in the pipeline to eliminate candidates quickly:

1. **Spatial filtering**: Most expensive, uses spatial indexes
2. **Skill filtering**: Medium cost, uses regular indexes
3. **Availability checking**: Variable cost, depends on appointment volume
4. **Simple boolean checks**: Minimal cost

### Soft Constraint Performance

Soft constraints are calculated only for candidates that pass hard constraints:

1. **Database lookups**: Continuity scoring, availability analysis
2. **Calculations**: Distance, experience, simple bonuses
3. **Aggregation**: Final score calculation and ranking

### Optimization Strategies

```java
// Early termination for hard constraints
if (!passesSkillFilter(staff, serviceRequest, criteria)) {
    continue; // Skip expensive checks
}

if (!passesGeographicFilter(staff, patient, criteria)) {
    continue; // Skip availability check
}

// Only calculate soft constraints for viable candidates
ScoreResult score = calculateMatchScore(staff, serviceRequest, patient, criteria);
```

## Configuration Management Best Practices

### 1. Version Control
- Keep configuration changes in version control
- Document the business rationale for each change
- Test configurations in development before production

### 2. Gradual Rollout
- Test new configurations with small user groups
- Monitor match quality metrics after changes
- Have rollback procedures ready

### 3. Performance Monitoring
- Track average response times after configuration changes
- Monitor database query performance
- Set up alerts for performance degradation

### 4. Business Alignment
- Involve stakeholders in weight decisions
- Regular review meetings for configuration tuning
- Document business rules and their implementation

This constraint system provides the flexibility to adapt the matching algorithm to different business requirements, geographic constraints, and operational needs while maintaining high performance and match quality.
