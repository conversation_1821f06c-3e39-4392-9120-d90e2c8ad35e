{"name": "Reinforcement Learning Optimization", "description": "Tests the reinforcement learning component's ability to optimize rule weights based on match quality metrics.", "patients": [{"id": "patient-001", "name": "<PERSON>", "mrn": "MRN12345", "location": {"address": "123 Main St, Anytown, CA 90210", "postal_code": "90210", "coordinates": {"latitude": 34.0522, "longitude": -118.2437}}, "required_skills": ["wound_care", "medication_management"], "required_certifications": ["RN"], "required_slots": [{"date": "2025-06-01", "start_time": "09:00", "end_time": "11:00"}], "preferences": {"preferred_carestaff_ids": ["carestaff-002"], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 2}}, {"id": "patient-002", "name": "<PERSON>", "mrn": "MRN67890", "location": {"address": "456 Oak St, Anytown, CA 90211", "postal_code": "90211", "coordinates": {"latitude": 34.0548, "longitude": -118.25}}, "required_skills": ["physical_therapy", "medication_management"], "required_certifications": ["PT"], "required_slots": [{"date": "2025-06-01", "start_time": "13:00", "end_time": "15:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": ["carestaff-001"], "preferred_carestaff_gender": "female", "preferred_languages": ["english", "spanish"], "previous_carestaff_ids": ["carestaff-003"]}, "profile": {"min_carestaff_experience": 3}}, {"id": "patient-003", "name": "<PERSON>", "mrn": "MRN54321", "location": {"address": "789 Pine St, Anytown, CA 90212", "postal_code": "90212", "coordinates": {"latitude": 34.053, "longitude": -118.248}}, "required_skills": ["respiratory_therapy", "medication_management"], "required_certifications": ["RN"], "required_slots": [{"date": "2025-06-01", "start_time": "16:00", "end_time": "18:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": ["carestaff-004"]}, "profile": {"min_carestaff_experience": 5}}], "carestaff": [{"id": "carestaff-001", "name": "<PERSON>", "service_type": "home_care", "skills": ["wound_care", "medication_management", "physical_therapy"], "certifications": ["RN", "CPR"], "languages": ["english"], "location": {"address": "101 Elm St, Anytown, CA 90210", "service_areas": ["90210", "90211", "90212"], "coordinates": {"latitude": 34.0535, "longitude": -118.249}}, "available_slots": [{"date": "2025-06-01", "start_time": "08:00", "end_time": "12:00"}, {"date": "2025-06-01", "start_time": "15:00", "end_time": "19:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 5}, "daily_appointments_count": 2, "max_daily_appointments": 5}, {"id": "carestaff-002", "name": "<PERSON>", "service_type": "home_care", "skills": ["wound_care", "medication_management", "physical_therapy"], "certifications": ["RN", "PT", "CPR"], "languages": ["english", "spanish"], "location": {"address": "202 Maple St, Anytown, CA 90211", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.054, "longitude": -118.2495}}, "available_slots": [{"date": "2025-06-01", "start_time": "09:00", "end_time": "17:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 8}, "daily_appointments_count": 1, "max_daily_appointments": 4}, {"id": "carestaff-003", "name": "<PERSON>", "service_type": "home_care", "skills": ["physical_therapy", "medication_management"], "certifications": ["PT", "CPR"], "languages": ["english", "spanish"], "location": {"address": "303 Cedar St, Anytown, CA 90211", "service_areas": ["90211", "90212"], "coordinates": {"latitude": 34.0545, "longitude": -118.2505}}, "available_slots": [{"date": "2025-06-01", "start_time": "12:00", "end_time": "20:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 6}, "daily_appointments_count": 2, "max_daily_appointments": 5}, {"id": "carestaff-004", "name": "<PERSON>", "service_type": "home_care", "skills": ["respiratory_therapy", "medication_management", "wound_care"], "certifications": ["RN", "CPR"], "languages": ["english", "mandarin"], "location": {"address": "404 Birch St, Anytown, CA 90212", "service_areas": ["90212"], "coordinates": {"latitude": 34.055, "longitude": -118.251}}, "available_slots": [{"date": "2025-06-01", "start_time": "14:00", "end_time": "22:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 10}, "daily_appointments_count": 1, "max_daily_appointments": 3}, {"id": "carestaff-005", "name": "<PERSON>", "service_type": "home_care", "skills": ["wound_care", "medication_management", "physical_therapy"], "certifications": ["RN", "CPR"], "languages": ["english", "french"], "location": {"address": "505 Walnut St, Anytown, CA 90210", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.0555, "longitude": -118.2515}}, "available_slots": [{"date": "2025-06-01", "start_time": "08:00", "end_time": "16:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 7}, "daily_appointments_count": 2, "max_daily_appointments": 5}], "rl_config": {"num_episodes": 10, "learning_rate": 0.001, "gamma": 0.99, "initial_weights": {"preferred_carestaff": 1.0, "gender_preference": 1.0, "language_preference": 1.0, "continuity_of_care": 1.0, "workload_balance": 1.0}, "expected_optimized_weights": {"preferred_carestaff": [1.5, 2.5], "gender_preference": [0.8, 1.5], "language_preference": [1.0, 2.0], "continuity_of_care": [1.2, 2.2], "workload_balance": [0.5, 1.5]}}, "expected_matches": {"patient-001": ["carestaff-002", "carestaff-001", "carestaff-005"], "patient-002": ["carestaff-003", "carestaff-002"], "patient-003": ["carestaff-004"]}, "expected_metrics": {"initial": {"avg_score": [0.7, 0.9], "diversity": [0.6, 0.9], "preference_satisfaction": [0.5, 0.8], "workload_balance": [0.6, 0.9]}, "optimized": {"avg_score": [0.8, 1.0], "diversity": [0.7, 1.0], "preference_satisfaction": [0.7, 1.0], "workload_balance": [0.7, 1.0]}}}