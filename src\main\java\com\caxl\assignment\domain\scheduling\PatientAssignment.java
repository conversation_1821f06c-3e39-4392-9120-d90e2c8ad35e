package com.caxl.assignment.domain.scheduling;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.lookup.PlanningId;
import ai.timefold.solver.core.api.domain.variable.PlanningVariable;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * Timefold planning entity for patient-clinician assignments.
 * Uses dual planning variables for optimal assignment and scheduling.
 */
@PlanningEntity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PatientAssignment {

    @PlanningId
    private String id;

    /**
     * Planning variable: Assigned clinician (optimized by Timefold).
     */
    @PlanningVariable(valueRangeProviderRefs = "clinicianRange")
    private Clinician assignedClinician;

    /**
     * Planning variable: Assigned time slot (optimized by Timefold).
     */
    @PlanningVariable(valueRangeProviderRefs = "timeSlotRange")
    private TimeSlot assignedTimeSlot;

    /**
     * Problem fact: The patient requiring assignment.
     */
    private Patient patient;

    /**
     * Problem fact: Assignment date.
     */
    private LocalDate assignmentDate;

    /**
     * Problem fact: Priority level (1=highest, 5=lowest).
     */
    @Builder.Default
    private int priority = 3;

    /**
     * Problem fact: Geographic zone for travel optimization.
     */
    private String geographicZone;

    // === ADVANCED BUSINESS LOGIC ===

    /**
     * Check if assignment is complete and feasible.
     */
    public boolean isFeasible() {
        return assignedClinician != null &&
               assignedTimeSlot != null &&
               hasRequiredSkills() &&
               isClinicianAvailable();
    }

    /**
     * Check if clinician has required skills.
     */
    public boolean hasRequiredSkills() {
        if (assignedClinician == null || patient == null) return false;
        return assignedClinician.getSkills().containsAll(patient.getRequiredSkills());
    }

    /**
     * Check if clinician is available at assigned time.
     */
    public boolean isClinicianAvailable() {
        if (assignedClinician == null || assignedTimeSlot == null) return false;

        return assignedClinician.getAvailableSlots().stream()
                .anyMatch(slot -> slot.getDate().equals(assignmentDate) &&
                                !slot.getStartTime().isAfter(assignedTimeSlot.getStartTime()) &&
                                !slot.getEndTime().isBefore(assignedTimeSlot.getEndTime()));
    }

    /**
     * Get workload points for this assignment.
     */
    public int getWorkloadPoints() {
        return patient != null ? patient.getWorkloadPoints() : 0;
    }

    /**
     * Calculate advanced preference matching score.
     */
    public int getPreferenceScore() {
        if (!isComplete()) return 0;

        int score = 0;
        if (patient.getPreferences() != null) {
            // Preferred clinician (highest weight)
            if (patient.getPreferences().getPreferredCarestaffIds()
                    .contains(assignedClinician.getId())) {
                score += 25;
            }

            // Previous clinician (continuity of care)
            if (patient.getPreferences().getPreviousCarestaffIds()
                    .contains(assignedClinician.getId())) {
                score += 15;
            }

            // Gender preference
            if (patient.getPreferences().getPreferredCarestaffGender() != null &&
                patient.getPreferences().getPreferredCarestaffGender()
                       .equals(assignedClinician.getPii().getGender())) {
                score += 10;
            }

            // Language preference
            if (patient.getPreferences().getPreferredLanguages().stream()
                    .anyMatch(lang -> assignedClinician.getLanguages().contains(lang))) {
                score += 8;
            }
        }

        return score;
    }

    /**
     * Calculate skill match quality (0-100).
     */
    public int getSkillMatchQuality() {
        if (!isComplete()) return 0;

        var requiredSkills = patient.getRequiredSkills();
        var clinicianSkills = assignedClinician.getSkills();

        if (requiredSkills.isEmpty()) return 100;

        long matchingSkills = requiredSkills.stream()
                .filter(clinicianSkills::contains)
                .count();

        return (int) ((matchingSkills * 100) / requiredSkills.size());
    }

    /**
     * Calculate priority weight for optimization.
     */
    public int getPriorityWeight() {
        return switch (priority) {
            case 1 -> 100; // Critical
            case 2 -> 75;  // High
            case 3 -> 50;  // Normal
            case 4 -> 25;  // Low
            case 5 -> 10;  // Deferred
            default -> 50;
        };
    }

    /**
     * Calculate travel efficiency score based on geographic proximity.
     */
    public int getTravelEfficiencyScore() {
        if (!isComplete()) return 0;

        String patientZone = patient.getLocation().getPostalCode().substring(0, 3);
        String clinicianZone = assignedClinician.getLocation().getServiceAreas().isEmpty()
                ? "000" : assignedClinician.getLocation().getServiceAreas().get(0);

        if (patientZone.equals(clinicianZone)) {
            return 20; // Same zone - excellent
        } else if (Math.abs(Integer.parseInt(patientZone) - Integer.parseInt(clinicianZone)) <= 10) {
            return 10; // Adjacent zones - good
        } else {
            return 0; // Distant zones - poor
        }
    }

    /**
     * Check if assignment is complete.
     */
    public boolean isComplete() {
        return assignedClinician != null && assignedTimeSlot != null && patient != null;
    }

    /**
     * Get patient ID for convenience.
     */
    public String getPatientId() {
        return patient != null ? patient.getId() : null;
    }

    /**
     * Get clinician ID for convenience.
     */
    public String getClinicianId() {
        return assignedClinician != null ? assignedClinician.getId() : null;
    }

    /**
     * Get the assigned care staff (alias for assignedClinician as CareStaff).
     * This method is used by enhanced constraints.
     */
    public CareStaff getCareStaff() {
        if (assignedClinician instanceof CareStaff) {
            return (CareStaff) assignedClinician;
        }
        return null; // Or throw exception if not CareStaff
    }

    /**
     * Get start time as LocalDateTime for constraint evaluation.
     * Combines assignment date with time slot start time.
     */
    public LocalDateTime getStartTime() {
        if (assignmentDate != null && assignedTimeSlot != null && assignedTimeSlot.getStartTime() != null) {
            return assignmentDate.atTime(assignedTimeSlot.getStartTime());
        }
        return null;
    }

    /**
     * Get end time as LocalDateTime for constraint evaluation.
     * Combines assignment date with time slot end time.
     */
    public LocalDateTime getEndTime() {
        if (assignmentDate != null && assignedTimeSlot != null && assignedTimeSlot.getEndTime() != null) {
            return assignmentDate.atTime(assignedTimeSlot.getEndTime());
        }
        return null;
    }

    /**
     * Advanced time slot for sophisticated scheduling.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeSlot {
        private String id;
        private LocalTime startTime;
        private LocalTime endTime;
        private String shiftType; // MORNING, AFTERNOON, EVENING
        private int capacity; // Max assignments in this slot
        private String serviceArea;

        /**
         * Check if this slot overlaps with another.
         */
        public boolean overlapsWith(TimeSlot other) {
            if (other == null) return false;
            return startTime.isBefore(other.endTime) && endTime.isAfter(other.startTime);
        }

        /**
         * Get duration in minutes.
         */
        public long getDurationMinutes() {
            return java.time.Duration.between(startTime, endTime).toMinutes();
        }

        /**
         * Check if slot can accommodate assignment.
         */
        public boolean canAccommodate(PatientAssignment assignment) {
            return assignment.getWorkloadPoints() <= getRemainingCapacity();
        }

        /**
         * Get remaining capacity (simplified).
         */
        public int getRemainingCapacity() {
            return capacity; // Would be calculated based on current assignments
        }
    }
}
