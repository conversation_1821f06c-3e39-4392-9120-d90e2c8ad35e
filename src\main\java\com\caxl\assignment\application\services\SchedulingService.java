package com.caxl.assignment.application.services;

import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.bi.BiJoiner;
import ai.timefold.solver.core.api.score.stream.uni.UniConstraintStream;
import static ai.timefold.solver.core.api.score.stream.Joiners.*;
import ai.timefold.solver.core.api.solver.Solver;
import ai.timefold.solver.core.api.solver.SolverFactory;
import com.caxl.assignment.domain.exceptions.NoFeasibleAssignmentException;
import com.caxl.assignment.domain.models.Assignment;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.scheduling.ClinicianShift;
import com.caxl.assignment.domain.scheduling.PatientAssignment;
import com.caxl.assignment.domain.scheduling.SchedulingSolution;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Advanced Timefold scheduling service using dual planning variables.
 * Provides enterprise-grade clinician scheduling with sophisticated optimization.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SchedulingService {

    private final SolverFactory<SchedulingSolution> solverFactory;

    /**
     * Create optimal assignments using advanced dual-variable optimization.
     * This is the main entry point for the optimization service.
     */
    public List<Assignment> createAssignments(
            List<Patient> patients,
            List<Clinician> clinicians,
            List<LocalDate> schedulingPeriod) {

        log.info("Starting advanced scheduling for {} patients and {} clinicians over {} days",
                patients.size(), clinicians.size(), schedulingPeriod.size());

        // Create advanced scheduling problem
        SchedulingSolution problem = createAdvancedSchedulingProblem(patients, clinicians, schedulingPeriod);

        // Solve using Timefold
        Solver<SchedulingSolution> solver = solverFactory.buildSolver();
        SchedulingSolution solution = solver.solve(problem);

        // Validate solution
        validateAdvancedSolution(solution, patients.size());

        // Convert to assignments
        List<Assignment> assignments = convertAdvancedToAssignments(solution);

        // Log results
        logAdvancedOptimizationResults(solution, assignments.size());

        return assignments;
    }

    /**
     * Create assignments for a single day (convenience method).
     */
    public List<Assignment> createAssignments(
            List<Patient> patients,
            List<Clinician> clinicians,
            Object properties) {

        // Default to today for single-day scheduling
        List<LocalDate> singleDay = List.of(LocalDate.now());
        return createAssignments(patients, clinicians, singleDay);
    }

    /**
     * Create an advanced scheduling problem with shifts and patient assignments.
     */
    private SchedulingSolution createAdvancedSchedulingProblem(
            List<Patient> patients,
            List<Clinician> clinicians,
            List<LocalDate> schedulingPeriod) {

        // Generate shifts for the scheduling period
        List<ClinicianShift> shifts = generateShifts(schedulingPeriod, clinicians.size());

        // Create patient assignments
        List<PatientAssignment> patientAssignments = new ArrayList<>();
        for (Patient patient : patients) {
            for (LocalDate date : schedulingPeriod) {
                patientAssignments.add(PatientAssignment.builder()
                        .id(UUID.randomUUID().toString())
                        .patient(patient)
                        .assignmentDate(date)
                        .build());
            }
        }

        return SchedulingSolution.builder()
                .assignments(patientAssignments)
                .clinicians(clinicians)
                .patients(patients)
                .schedulingPeriod(schedulingPeriod)
                .build();
    }

    /**
     * Generate shifts based on scheduling period and available clinicians.
     */
    private List<ClinicianShift> generateShifts(List<LocalDate> dates, int clinicianCount) {
        List<ClinicianShift> shifts = new ArrayList<>();

        for (LocalDate date : dates) {
            // Create morning shifts
            int morningShifts = Math.min(clinicianCount / 2, 4);
            for (int i = 0; i < morningShifts; i++) {
                shifts.add(createShift(date, ClinicianShift.ShiftType.MORNING, i + 1));
            }

            // Create afternoon shifts
            int afternoonShifts = Math.min(clinicianCount / 2, 4);
            for (int i = 0; i < afternoonShifts; i++) {
                shifts.add(createShift(date, ClinicianShift.ShiftType.AFTERNOON, i + 1));
            }
        }

        log.debug("Generated {} shifts for {} days", shifts.size(), dates.size());
        return shifts;
    }

    /**
     * Create a single shift with optimal parameters.
     */
    private ClinicianShift createShift(LocalDate date, ClinicianShift.ShiftType shiftType, int shiftNumber) {
        LocalTime startTime;
        LocalTime endTime;

        switch (shiftType) {
            case MORNING:
                startTime = LocalTime.of(8, 0);
                endTime = LocalTime.of(16, 0);
                break;
            case AFTERNOON:
                startTime = LocalTime.of(14, 0);
                endTime = LocalTime.of(22, 0);
                break;
            case EVENING:
                startTime = LocalTime.of(18, 0);
                endTime = LocalTime.of(2, 0); // Next day
                break;
            default:
                startTime = LocalTime.of(8, 0);
                endTime = LocalTime.of(16, 0);
        }

        return ClinicianShift.builder()
                .id(String.format("shift_%s_%s_%d", date, shiftType.name().toLowerCase(), shiftNumber))
                .date(date)
                .shiftType(shiftType)
                .startTime(startTime)
                .endTime(endTime)
                .maxPatients(8) // Optimal capacity
                .maxWorkloadPoints(100) // Optimal workload limit
                .serviceArea("general")
                .patientVisits(new ArrayList<>())
                .build();
    }

    /**
     * Validate the advanced scheduling solution.
     */
    private void validateAdvancedSolution(SchedulingSolution solution, int totalPatients) {
        if (!solution.isFeasible()) {
            String message = String.format(
                "No feasible schedule found. Assigned %d of %d patients. Score: %s",
                solution.getAssignedPatients().size(),
                totalPatients,
                solution.getScore()
            );
            throw new NoFeasibleAssignmentException(message);
        }
    }

    /**
     * Convert advanced scheduling solution to assignment list.
     */
    private List<Assignment> convertAdvancedToAssignments(SchedulingSolution solution) {
        List<Assignment> assignments = new ArrayList<>();

        for (PatientAssignment patientAssignment : solution.getAssignments()) {
            if (patientAssignment.getAssignedClinician() != null && patientAssignment.getAssignedTimeSlot() != null) {
                Assignment assignment = Assignment.builder()
                        .id(UUID.randomUUID().toString())
                        .patientId(patientAssignment.getPatient().getId())
                        .clinicianId(patientAssignment.getAssignedClinician().getId())
                        .visitDate(patientAssignment.getAssignmentDate().toString())
                        .startTime(patientAssignment.getAssignedTimeSlot().getStartTime().toString())
                        .endTime(patientAssignment.getAssignedTimeSlot().getEndTime().toString())
                        .status(Assignment.AssignmentStatus.PENDING)
                        .workloadPoints(patientAssignment.getWorkloadPoints())
                        .build();
                assignments.add(assignment);
            }
        }

        return assignments;
    }

    /**
     * Create an assignment from a shift and patient.
     */
    private Assignment createAssignment(ClinicianShift shift, Patient patient, int visitOrder) {
        // Calculate visit time based on order in shift
        LocalTime visitStartTime = shift.getStartTime().plusMinutes(visitOrder * 60L);
        LocalTime visitEndTime = visitStartTime.plusMinutes(60); // 1-hour visits

        return Assignment.builder()
                .id(UUID.randomUUID().toString())
                .patientId(patient.getId())
                .clinicianId(shift.getAssignedClinician().getId())
                .visitDate(shift.getShiftDate().toString())
                .startTime(visitStartTime.toString())
                .endTime(visitEndTime.toString())
                .status(Assignment.AssignmentStatus.PENDING)
                .workloadPoints(patient.getWorkloadPoints())
                .build();
    }

    /**
     * Log advanced optimization results.
     */
    private void logAdvancedOptimizationResults(SchedulingSolution solution, int assignmentCount) {
        SchedulingSolution.SolutionSummary summary = solution.getSummary();

        log.info("=== Advanced Scheduling Results ===");
        log.info("Total patients: {}", summary.getTotalPatients());
        log.info("Assigned patients: {}", summary.getAssignedPatients());
        log.info("Assignment rate: {:.1f}%", summary.getAssignmentRate() * 100);
        log.info("Total shifts: {}", summary.getTotalShifts());
        log.info("Active shifts: {}", summary.getActiveShifts());
        log.info("Average utilization: {:.1f}%", summary.getAverageUtilization() * 100);
        log.info("Workload fairness: {:.2f}", summary.getWorkloadFairness());
        log.info("Final score: {}", summary.getScore());
        log.info("Assignments created: {}", assignmentCount);
        log.info("=== Optimization Complete ===");
    }
}
