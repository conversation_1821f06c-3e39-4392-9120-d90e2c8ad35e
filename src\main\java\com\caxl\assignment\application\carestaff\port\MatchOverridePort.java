package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.entities.MatchOverride;

import java.util.UUID;

/**
 * Port interface for match override persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface MatchOverridePort {

    /**
     * Save match override.
     * 
     * @param matchOverride Match override to save
     * @return Saved match override
     */
    MatchOverride save(MatchOverride matchOverride);
}

