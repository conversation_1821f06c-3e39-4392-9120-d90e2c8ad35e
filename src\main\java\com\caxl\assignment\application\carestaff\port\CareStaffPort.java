package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.entities.Appointment;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;

import java.util.List;
import java.util.UUID;

/**
 * Port interface for care staff persistence operations with spatial queries.
 * Driving port in hexagonal architecture.
 */
public interface CareStaffPort {

    /**
     * Find potential care staff by skills and proximity using spatial queries.
     * 
     * @param requiredSkillIds Required skill IDs
     * @param patientLocationCoords Patient location coordinates
     * @param searchRadiusKm Search radius in kilometers
     * @param requireAllSkills Whether all skills must be matched
     * @return List of potential care staff domain entities
     */
    List<CareStaffDomain> findPotentialStaffBySkillsAndProximity(
        List<UUID> requiredSkillIds, 
        Point patientLocationCoords, 
        double searchRadiusKm, 
        boolean requireAllSkills
    );

    /**
     * Find service geofences for a care staff member.
     * 
     * @param staffId Care staff ID
     * @param geofenceTypes List of geofence types to search
     * @return List of geofence polygons
     */
    List<Polygon> findServiceGeofencesForStaff(UUID staffId, List<String> geofenceTypes);

    /**
     * Find overlapping appointments for a care staff member.
     * 
     * @param staffId Care staff ID
     * @param requestTimeWindow Time window to check
     * @param overlapThresholdMinutes Minimum overlap threshold
     * @return List of overlapping appointments
     */
    List<Appointment> findOverlappingAppointments(
        UUID staffId, 
        ServiceRequestTimeWindow requestTimeWindow, 
        int overlapThresholdMinutes
    );

    /**
     * Domain representation of care staff for matching operations.
     */
    record CareStaffDomain(
        UUID careStaffId,
        String name,
        Point baseLocation,
        List<UUID> skillIds,
        List<UUID> certificationIds,
        List<String> languages,
        int experienceYears,
        boolean isActive,
        List<String> operatingZones
    ) {}

    /**
     * Time window representation for queries.
     */
    record ServiceRequestTimeWindow(
        java.time.LocalDateTime startTime,
        java.time.LocalDateTime endTime
    ) {}
}
