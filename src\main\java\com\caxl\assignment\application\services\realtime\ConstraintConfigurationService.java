package com.caxl.assignment.application.services.realtime;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Dynamic constraint configuration service for real-time constraint tuning.
 * Allows administrators to adjust constraint weights and parameters without system restart.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ConstraintConfigurationService {

    private final ObjectMapper objectMapper;
    
    // Current constraint configuration
    private final Map<String, ConstraintConfig> currentConfig = new ConcurrentHashMap<>();
    
    // Predefined constraint profiles for different scenarios
    private final Map<String, Map<String, ConstraintConfig>> constraintProfiles = new ConcurrentHashMap<>();

    /**
     * Initialize constraint configurations and profiles.
     */
    public void initializeConstraintProfiles() {
        // Normal operation profile
        Map<String, ConstraintConfig> normalProfile = createNormalProfile();
        constraintProfiles.put("NORMAL", normalProfile);
        
        // Urgent/Emergency profile with relaxed constraints
        Map<String, ConstraintConfig> urgentProfile = createUrgentProfile();
        constraintProfiles.put("URGENT", urgentProfile);
        
        // High-demand profile (flu season, holidays)
        Map<String, ConstraintConfig> highDemandProfile = createHighDemandProfile();
        constraintProfiles.put("HIGH_DEMAND", highDemandProfile);
        
        // Rural profile with extended travel times
        Map<String, ConstraintConfig> ruralProfile = createRuralProfile();
        constraintProfiles.put("RURAL", ruralProfile);
        
        // Urban profile optimized for city constraints
        Map<String, ConstraintConfig> urbanProfile = createUrbanProfile();
        constraintProfiles.put("URBAN", urbanProfile);
        
        // Set normal as default
        applyConstraintProfile("NORMAL");
    }

    /**
     * Apply a predefined constraint profile.
     */
    public void applyConstraintProfile(String profileName) {
        Map<String, ConstraintConfig> profile = constraintProfiles.get(profileName);
        if (profile != null) {
            currentConfig.clear();
            currentConfig.putAll(profile);
            log.info("Applied constraint profile: {}", profileName);
        } else {
            log.warn("Unknown constraint profile: {}", profileName);
        }
    }

    /**
     * Apply urgent constraint profile for emergency scenarios.
     */
    public void applyUrgentConstraintProfile() {
        applyConstraintProfile("URGENT");
    }

    /**
     * Restore normal constraint profile.
     */
    public void restoreNormalConstraintProfile() {
        applyConstraintProfile("NORMAL");
    }

    /**
     * Update individual constraint configuration.
     */
    public void updateConstraintConfig(String constraintName, ConstraintConfig config) {
        currentConfig.put(constraintName, config);
        log.info("Updated constraint configuration for: {} with weight: {} and enabled: {}", 
                constraintName, config.getWeight(), config.isEnabled());
    }

    /**
     * Get current configuration for a constraint.
     */
    public ConstraintConfig getConstraintConfig(String constraintName) {
        return currentConfig.getOrDefault(constraintName, getDefaultConfig());
    }

    /**
     * Get all current constraint configurations.
     */
    public Map<String, ConstraintConfig> getAllConfigurations() {
        return Map.copyOf(currentConfig);
    }

    /**
     * Reload configuration from external source.
     */
    public void reloadConfiguration() {
        log.info("Reloading constraint configuration");
        // TODO: Reload from database or configuration service
        initializeConstraintProfiles();
    }

    /**
     * Create normal operation constraint profile.
     */
    private Map<String, ConstraintConfig> createNormalProfile() {
        Map<String, ConstraintConfig> profile = new ConcurrentHashMap<>();
        
        // Hard constraints
        profile.put("caregiver_availability", ConstraintConfig.hardConstraint(1.0));
        profile.put("skill_requirements", ConstraintConfig.hardConstraint(1.0));
        profile.put("workload_limits", ConstraintConfig.hardConstraint(1.0));
        profile.put("patient_time_windows", ConstraintConfig.hardConstraint(1.0));
        profile.put("travel_feasibility", ConstraintConfig.hardConstraint(1.0));
        profile.put("licensing_certification", ConstraintConfig.hardConstraint(1.0));
        profile.put("prohibited_pairings", ConstraintConfig.hardConstraint(1.0));
        
        // Soft constraints
        profile.put("minimize_travel_time", ConstraintConfig.softConstraint(100.0, true));
        profile.put("continuity_of_care", ConstraintConfig.softConstraint(80.0, true));
        profile.put("workload_balance", ConstraintConfig.softConstraint(60.0, true));
        profile.put("patient_preferences", ConstraintConfig.softConstraint(40.0, true));
        profile.put("caregiver_preferences", ConstraintConfig.softConstraint(30.0, true));
        profile.put("minimize_overtime", ConstraintConfig.softConstraint(50.0, true));
        profile.put("visit_urgency", ConstraintConfig.softConstraint(90.0, true));
        profile.put("shift_cohesion", ConstraintConfig.softConstraint(35.0, true));
        
        return profile;
    }

    /**
     * Create urgent/emergency constraint profile with relaxed constraints.
     */
    private Map<String, ConstraintConfig> createUrgentProfile() {
        Map<String, ConstraintConfig> profile = new ConcurrentHashMap<>();
        
        // Hard constraints - some relaxed for emergency scenarios
        profile.put("caregiver_availability", ConstraintConfig.hardConstraint(1.0));
        profile.put("skill_requirements", ConstraintConfig.softConstraint(200.0, true)); // Relaxed to soft
        profile.put("workload_limits", ConstraintConfig.softConstraint(150.0, true)); // Allow overtime
        profile.put("patient_time_windows", ConstraintConfig.softConstraint(100.0, true)); // More flexible
        profile.put("travel_feasibility", ConstraintConfig.hardConstraint(0.8)); // Slightly relaxed
        profile.put("licensing_certification", ConstraintConfig.hardConstraint(1.0));
        profile.put("prohibited_pairings", ConstraintConfig.hardConstraint(1.0));
        
        // Soft constraints - prioritize urgent placement
        profile.put("minimize_travel_time", ConstraintConfig.softConstraint(50.0, true)); // Lower priority
        profile.put("continuity_of_care", ConstraintConfig.softConstraint(20.0, true)); // Much lower
        profile.put("workload_balance", ConstraintConfig.softConstraint(20.0, true)); // Lower
        profile.put("patient_preferences", ConstraintConfig.softConstraint(10.0, true)); // Lower
        profile.put("caregiver_preferences", ConstraintConfig.softConstraint(5.0, true)); // Much lower
        profile.put("minimize_overtime", ConstraintConfig.softConstraint(10.0, true)); // Accept overtime
        profile.put("visit_urgency", ConstraintConfig.softConstraint(300.0, true)); // Very high priority
        profile.put("shift_cohesion", ConstraintConfig.softConstraint(10.0, true)); // Lower
        
        return profile;
    }

    /**
     * Create high-demand constraint profile for peak periods.
     */
    private Map<String, ConstraintConfig> createHighDemandProfile() {
        Map<String, ConstraintConfig> profile = createNormalProfile();
        
        // Adjust for high demand scenarios
        profile.put("workload_limits", ConstraintConfig.softConstraint(100.0, true)); // Allow some flexibility
        profile.put("minimize_overtime", ConstraintConfig.softConstraint(30.0, true)); // Accept more overtime
        profile.put("workload_balance", ConstraintConfig.softConstraint(40.0, true)); // Less strict balancing
        profile.put("shift_cohesion", ConstraintConfig.softConstraint(50.0, true)); // Higher importance
        
        return profile;
    }

    /**
     * Create rural constraint profile with extended travel considerations.
     */
    private Map<String, ConstraintConfig> createRuralProfile() {
        Map<String, ConstraintConfig> profile = createNormalProfile();
        
        // Adjust for rural scenarios
        profile.put("minimize_travel_time", ConstraintConfig.softConstraint(150.0, true)); // Higher importance
        profile.put("shift_cohesion", ConstraintConfig.softConstraint(100.0, true)); // Much higher importance
        profile.put("continuity_of_care", ConstraintConfig.softConstraint(120.0, true)); // Higher due to limited staff
        profile.put("workload_balance", ConstraintConfig.softConstraint(40.0, true)); // Less strict due to constraints
        
        return profile;
    }

    /**
     * Create urban constraint profile optimized for city scenarios.
     */
    private Map<String, ConstraintConfig> createUrbanProfile() {
        Map<String, ConstraintConfig> profile = createNormalProfile();
        
        // Adjust for urban scenarios
        profile.put("minimize_travel_time", ConstraintConfig.softConstraint(120.0, true)); // Higher due to traffic
        profile.put("patient_preferences", ConstraintConfig.softConstraint(60.0, true)); // Higher due to options
        profile.put("caregiver_preferences", ConstraintConfig.softConstraint(45.0, true)); // Higher due to options
        profile.put("workload_balance", ConstraintConfig.softConstraint(80.0, true)); // Higher importance
        
        return profile;
    }

    /**
     * Get default constraint configuration.
     */
    private ConstraintConfig getDefaultConfig() {
        return ConstraintConfig.softConstraint(50.0, true);
    }

    /**
     * Constraint configuration class.
     */
    public static class ConstraintConfig {
        private boolean isHardConstraint;
        private double weight;
        private boolean enabled;
        private Map<String, Object> parameters;

        public ConstraintConfig() {}

        public ConstraintConfig(boolean isHardConstraint, double weight, boolean enabled, Map<String, Object> parameters) {
            this.isHardConstraint = isHardConstraint;
            this.weight = weight;
            this.enabled = enabled;
            this.parameters = parameters != null ? parameters : Map.of();
        }

        public static ConstraintConfig hardConstraint(double weight) {
            return new ConstraintConfig(true, weight, true, Map.of());
        }

        public static ConstraintConfig softConstraint(double weight, boolean enabled) {
            return new ConstraintConfig(false, weight, enabled, Map.of());
        }

        // Getters and setters
        public boolean isHardConstraint() { return isHardConstraint; }
        public void setHardConstraint(boolean hardConstraint) { isHardConstraint = hardConstraint; }
        
        public double getWeight() { return weight; }
        public void setWeight(double weight) { this.weight = weight; }
        
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }
    }
}
