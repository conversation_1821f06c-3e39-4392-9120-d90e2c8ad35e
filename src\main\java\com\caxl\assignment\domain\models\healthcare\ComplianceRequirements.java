package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.ArrayList;
import java.util.List;

/**
 * Compliance requirements for homecare visits.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComplianceRequirements {

    @NotBlank(message = "State is required")
    @JsonProperty("state")
    private String state;

    @NotNull(message = "Required break duration is required")
    @JsonProperty("required_break_duration")
    private Duration requiredBreakDuration;

    @NotNull(message = "Max consecutive hours is required")
    @JsonProperty("max_consecutive_hours")
    private Duration maxConsecutiveHours;    @JsonProperty("overtime_allowed")
    @Builder.Default
    private boolean overtimeAllowed = false;

    @JsonProperty("background_check_required")
    @Builder.Default
    private boolean backgroundCheckRequired = true;

    @JsonProperty("drug_test_required")
    @Builder.Default
    private boolean drugTestRequired = true;

    @JsonProperty("liability_insurance_required")
    @Builder.Default
    private boolean liabilityInsuranceRequired = true;

    @JsonProperty("required_licenses")
    @Builder.Default
    private List<String> requiredLicenses = new ArrayList<>();
}
