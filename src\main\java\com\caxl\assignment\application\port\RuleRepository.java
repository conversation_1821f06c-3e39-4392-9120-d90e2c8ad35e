package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Rule;

import java.util.List;

/**
 * Repository interface for rule management.
 */
public interface RuleRepository {
    
    /**
     * Get all hard constraint rules.
     *
     * @return List of hard rules
     */
    List<Rule> getHardRules();
    
    /**
     * Get all soft constraint rules.
     *
     * @return List of soft rules
     */
    List<Rule> getSoftRules();
    
    /**
     * Get all prioritization rules.
     *
     * @return List of prioritization rules
     */
    List<Rule> getPrioritizationRules();
    
    /**
     * Get all rescheduling rules.
     *
     * @return List of rescheduling rules
     */
    List<Rule> getReschedulingRules();
    
    /**
     * Get all rules.
     *
     * @return List of all rules
     */
    List<Rule> getAllRules();
    
    /**
     * Get rules by domain.
     *
     * @param domain Domain name
     * @return List of rules for the domain
     */
    List<Rule> getRulesByDomain(String domain);
    
    /**
     * Get rule by ID.
     *
     * @param ruleId Rule ID
     * @return Rule object or null if not found
     */
    Rule getRuleById(String ruleId);
    
    /**
     * Check if rules are loaded.
     *
     * @return true if rules are loaded
     */
    boolean areRulesLoaded();
    
    /**
     * Reload rules from configuration.
     */
    void reloadRules();
}
