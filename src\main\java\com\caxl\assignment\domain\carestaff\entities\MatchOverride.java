package com.caxl.assignment.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain entity representing a manual override of match suggestions.
 * Framework-agnostic domain model for override tracking.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchOverride {

    private UUID overrideId;
    private UUID serviceRequestId;
    private UUID selectedCareStaffId;
    private UUID schedulerId;
    private String reason;
    private String explanation;
    private LocalDateTime overrideDateTime;
    private LocalDateTime createdAt;

    /**
     * Create a new match override.
     */
    public static MatchOverride create(UUID serviceRequestId, UUID careStaffId, 
                                     UUID schedulerId, String reason, String explanation) {
        return MatchOverride.builder()
                .overrideId(UUID.randomUUID())
                .serviceRequestId(serviceRequestId)
                .selectedCareStaffId(careStaffId)
                .schedulerId(schedulerId)
                .reason(reason)
                .explanation(explanation)
                .overrideDateTime(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();
    }
}
