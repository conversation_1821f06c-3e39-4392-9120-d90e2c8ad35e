# Matching Algorithm - CareStaff Matching Service

## Overview

The CareStaff Matching Service implements a sophisticated multi-stage heuristic matching algorithm that intelligently matches available carestaff to patient service requests based on configurable constraints and scoring weights.

## Algorithm Architecture

### Multi-Stage Processing Pipeline

```
Service Request Input
        ↓
1. Skill Extraction & Analysis
        ↓
2. Spatial Candidate Filtering
        ↓
3. Hard Constraint Validation
        ↓
4. Soft Constraint Scoring
        ↓
5. Ranking & Threshold Filtering
        ↓
Match Suggestions Output
```

## Stage 1: Intelligent Skill Extraction

### Purpose
Extract comprehensive skill requirements from multiple sources to ensure accurate matching.

### Process

#### Medical Condition Analysis
- **15+ condition mappings**: diabetes → blood glucose monitoring, insulin administration
- **Pattern matching**: Text analysis of patient medical conditions
- **Severity assessment**: Critical conditions require advanced skills

#### Visit Type Derivation
- **6+ specialized types**: wound_care, medication_management, physical_therapy, etc.
- **Skill escalation**: Complex visit types require additional specialized skills
- **Duration impact**: Longer visits may require endurance-related skills

#### Priority-Based Requirements
- **Priority 1 (Critical)**: Advanced life support, emergency response
- **Priority 2 (High)**: Specialized care, complex procedures
- **Priority 3+ (Standard)**: Basic care skills

#### Workload Complexity
- **High workload (4+ points)**: Complex care management skills
- **Special instructions**: Additional skills based on specific requirements

### Example Skill Extraction

```java
// Input: Patient with diabetes, wound care visit, priority 2
ServiceRequest: {
    visitType: "wound_care",
    priority: 2,
    workloadPoints: 3,
    specialInstructions: "Sterile technique required"
}

Patient: {
    medicalConditions: "Type 2 diabetes, chronic wound"
}

// Output: Extracted Skills
[
    "wound_assessment",           // From visit type
    "sterile_technique",          // From visit type + instructions
    "dressing_changes",           // From visit type
    "blood_glucose_monitoring",   // From diabetes condition
    "insulin_administration",     // From diabetes condition
    "specialized_care"            // From priority 2
]
```

## Stage 2: Spatial Candidate Filtering

### Purpose
Use PostGIS spatial operations to find carestaff within geographic constraints.

### Process

#### Proximity Search
```sql
-- Find staff within configurable radius using ST_DWithin
SELECT DISTINCT cs.carestaff_id, cs.experience_years, l.coordinates
FROM caresstaff_pe cs
JOIN location l ON cs.location_id = l.location_id
WHERE cs.active = true
AND ST_DWithin(l.coordinates, ST_SetSRID(ST_Point(:longitude, :latitude), 4326), :radiusMeters)
```

#### Skill Pre-filtering
- **All skills required**: Staff must have ALL extracted skills
- **Any skills sufficient**: Staff must have at least ONE extracted skill
- **Configurable matching**: Based on `requiredSkillsMustMatchAll` setting

#### Geographic Optimization
- **Distance calculation**: Accurate distance using spherical geometry
- **Search radius**: Configurable from 1km to 500km
- **Performance optimization**: Spatial indexes for fast queries

### Spatial Query Performance

```sql
-- Optimized spatial query with skill filtering
SELECT DISTINCT cs.carestaff_id, cs.enc_first_name, cs.enc_last_name,
       l.coordinates, cs.experience_years, cs.active,
       ARRAY_AGG(DISTINCT csm.skill_id) as skill_ids
FROM caresstaff_pe cs
JOIN location l ON cs.location_id = l.location_id
LEFT JOIN carestaff_has_skill csm ON cs.carestaff_id = csm.carestaff_id
WHERE cs.active = true
AND ST_DWithin(l.coordinates, ST_SetSRID(ST_Point(:longitude, :latitude), 4326), :radiusMeters)
AND (:requireAllSkills = false OR 
     (SELECT COUNT(DISTINCT skill_id) FROM carestaff_has_skill 
      WHERE carestaff_id = cs.carestaff_id AND skill_id = ANY(:skillIds)) = :skillCount)
GROUP BY cs.carestaff_id, cs.enc_first_name, cs.enc_last_name, 
         l.coordinates, cs.experience_years, cs.active
ORDER BY ST_Distance(l.coordinates, ST_SetSRID(ST_Point(:longitude, :latitude), 4326))
```

## Stage 3: Hard Constraint Validation

### Purpose
Apply mandatory filters that must be satisfied for a match to be considered.

### Constraint Types

#### 1. Geofence Service Area Validation
```java
if (criteria.getFilters().isMustRespectGeoServiceArea()) {
    List<Polygon> serviceGeofences = careStaffPort.findServiceGeofencesForStaff(
            staff.careStaffId(), 
            criteria.getGeography().getStaffServiceGeofenceTypes()
    );
    
    boolean withinServiceArea = serviceGeofences.stream()
            .anyMatch(geofence -> criteria.getGeography().isGeofenceStrictContainmentOnly() 
                    ? geofence.contains(patient.locationCoordinates())
                    : geofence.intersects(patient.locationCoordinates()));
}
```

#### 2. Availability Conflict Detection
```java
if (criteria.getFilters().isMustRespectAvailability()) {
    List<Appointment> overlappingAppointments = careStaffPort.findOverlappingAppointments(
            staff.careStaffId(), 
            timeWindow, 
            criteria.getAvailability().getOverlapThresholdMinutes()
    );
    
    if (!overlappingAppointments.isEmpty()) {
        return false; // Staff unavailable
    }
}
```

#### 3. Patient Preference Validation
```java
// Barred staff filter
if (criteria.getFilters().isMustNotBeBarred()) {
    if (patient.barredCareStaffIds() != null && 
        patient.barredCareStaffIds().contains(staff.careStaffId())) {
        return false; // Staff is barred by patient
    }
}
```

#### 4. Experience Level Requirements
```java
// Minimum experience for high-priority cases
if (serviceRequest.getPriority() <= 2 && staff.experienceYears() < 2) {
    return false; // Insufficient experience
}
```

#### 5. Required Certifications
```java
if (serviceRequest.getRequiredCertificationIds() != null) {
    boolean hasCertifications = serviceRequest.getRequiredCertificationIds().stream()
            .allMatch(certId -> staff.certificationIds().contains(certId));
    
    if (!hasCertifications) {
        return false; // Missing required certifications
    }
}
```

## Stage 4: Soft Constraint Scoring

### Purpose
Calculate optimization scores based on configurable weights to rank matches.

### Scoring Components

#### 1. Skill Match Scoring
```java
// Individual skill bonuses
long matchedSkills = serviceRequest.getRequiredSkillIds().stream()
        .mapToLong(skillId -> staff.skillIds().contains(skillId) ? 1 : 0)
        .sum();

if (criteria.getFilters().isRequiredSkillsMustMatchAll() && 
    matchedSkills == serviceRequest.getRequiredSkillIds().size()) {
    score += weights.getSkillMatchAllBonus(); // e.g., +50 points
} else {
    double skillBonus = matchedSkills * weights.getSkillMatchBonusPerRequiredSkill(); // e.g., +10 per skill
    score += skillBonus;
}
```

#### 2. Proximity Scoring
```java
// Distance penalty calculation
double distance = staff.baseLocation().distance(patientLocationCoords) * 111.0; // Convert to km
double proximityPenalty = distance * weights.getProximityKmPenalty(); // e.g., -1.0 per km
score += proximityPenalty;
```

#### 3. Continuity of Care Scoring
```java
// Historical relationship analysis
ContinuityOfCareService.ContinuityScore continuityScore = 
        continuityOfCareService.calculateContinuityScore(
                staff.careStaffId(), patient.patientId(), patient, 30);

double continuityBonus = continuityScore.score() * weights.getContinuityBonusPerRecentVisit();
score += continuityBonus;
```

#### 4. Availability Window Fit Scoring
```java
// Time optimization scoring
AvailabilityWindowScoringService.AvailabilityWindowScore availabilityScore = 
        availabilityWindowScoringService.calculateAvailabilityScore(
                staff.careStaffId(), serviceRequest, criteria.getAvailability(), weights);
score += availabilityScore.score();
```

#### 5. Preference and Language Scoring
```java
// Preferred staff bonus
if (patient.preferredCareStaffIds() != null && 
    patient.preferredCareStaffIds().contains(staff.careStaffId())) {
    score += weights.getPreferredCareStaffBonus(); // e.g., +15 points
}

// Language match bonus
if (patient.preferredLanguage() != null && 
    staff.languages().contains(patient.preferredLanguage())) {
    score += weights.getLanguageMatchBonus(); // e.g., +8 points
}
```

#### 6. Experience Level Scoring
```java
// Experience bonus
double experienceBonus = staff.experienceYears() * weights.getExperienceLevelBonusPerYear(); // e.g., +2 per year
score += experienceBonus;
```

### Complete Scoring Example

```java
// Example scoring breakdown for a match
ServiceRequest: wound_care, priority 2, 3 required skills
Patient: prefers English, has preferred staff list
Staff: 5 years experience, 15km away, has 2/3 skills, speaks English, is preferred

Score Calculation:
+ Skills: 2 × 10.0 = +20.0
+ Proximity: 15 × (-1.0) = -15.0
+ Geo Service Area: +20.0
+ Preferred Staff: +15.0
+ Language Match: +8.0
+ Experience: 5 × 2.0 = +10.0
+ Availability Fit: +5.0
+ Continuity: +12.0
= Total Score: 75.0
```

## Stage 5: Ranking & Threshold Filtering

### Purpose
Sort matches by score and apply minimum thresholds to ensure quality.

### Process

#### Score-Based Ranking
```java
// Sort suggestions by score (highest first)
suggestions.sort((s1, s2) -> Double.compare(s2.getScore(), s1.getScore()));
```

#### Threshold Filtering
```java
// Apply minimum score threshold
if (scoreResult.score() < criteria.getFilters().getMinScoreThreshold()) {
    continue; // Skip low-scoring matches
}
```

#### Result Limitation
- **Configurable limits**: Maximum number of suggestions returned
- **Quality over quantity**: Better to return fewer high-quality matches
- **Performance optimization**: Avoid processing excessive candidates

## Algorithm Performance

### Optimization Strategies

#### 1. Database Optimization
- **Spatial indexes**: GIST indexes on geographic columns
- **Composite indexes**: Multi-column indexes for common queries
- **Query optimization**: Efficient JOIN operations and WHERE clauses

#### 2. Algorithmic Optimization
- **Early termination**: Stop processing when sufficient matches found
- **Lazy evaluation**: Calculate expensive scores only when needed
- **Caching**: Cache frequently accessed data (configurations, staff profiles)

#### 3. Parallel Processing
- **Concurrent evaluation**: Process multiple staff candidates simultaneously
- **Async operations**: Non-blocking database operations where possible
- **Batch processing**: Group operations for efficiency

### Performance Metrics

#### Typical Performance
- **Small datasets** (< 100 staff): < 500ms
- **Medium datasets** (100-1000 staff): < 2 seconds
- **Large datasets** (1000+ staff): < 5 seconds

#### Scalability Factors
- **Geographic radius**: Larger radius = more candidates = longer processing
- **Skill complexity**: More skills = more complex matching logic
- **Historical data**: More history = more continuity calculations

## Configuration Impact

### Hard Constraint Configuration
```json
{
  "filters": {
    "requiredSkillsMustMatchAll": true,     // Stricter matching
    "proximitySearchRadiusKm": 25.0,       // Smaller search area
    "mustRespectGeoServiceArea": true,      // Geographic restrictions
    "mustRespectAvailability": true,        // Availability checking
    "minScoreThreshold": 30.0               // Higher quality threshold
  }
}
```

### Soft Constraint Weights
```json
{
  "scoring_weights": {
    "skillMatchAllBonus": 100.0,            // Emphasize complete skill match
    "proximityKmPenalty": -2.0,             // Penalize distance more heavily
    "preferredCareStaffBonus": 25.0,        // Strong preference bonus
    "continuityBonusPerRecentVisit": 10.0,  // Emphasize continuity
    "experienceLevelBonusPerYear": 5.0      // Value experience highly
  }
}
```

## Algorithm Extensibility

### Adding New Constraints
1. **Hard constraints**: Implement in `passesHardFilters()` method
2. **Soft constraints**: Add to `calculateMatchScore()` method
3. **Configuration**: Update JSON schema and validation

### Custom Scoring Logic
1. **Domain services**: Create new domain service for complex logic
2. **Integration**: Inject service into main matching service
3. **Configuration**: Add weights to configuration JSON

### Future Enhancements
- **Machine learning**: Predictive scoring based on historical outcomes
- **Real-time optimization**: Dynamic weight adjustment based on current conditions
- **Multi-objective optimization**: Balance multiple competing objectives simultaneously

This algorithm provides a robust, configurable, and extensible foundation for intelligent carestaff matching while maintaining high performance and quality standards.
