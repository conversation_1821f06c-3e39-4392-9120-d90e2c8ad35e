package com.caxl.assignment.application.services.common;

import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * Common optimization utilities to eliminate duplicate optimization triggering
 * and schedule copying patterns across real-time services.
 */
@Slf4j
public class OptimizationUtils {
    
    private static final Map<String, LocalDateTime> lastOptimizationTime = new ConcurrentHashMap<>();
    private static final Map<String, CompletableFuture<?>> activeOptimizations = new ConcurrentHashMap<>();
    
    /**
     * Trigger optimization with rate limiting and duplicate prevention.
     */
    public static <T> CompletableFuture<T> triggerOptimization(
            String optimizationKey,
            Duration minInterval,
            Duration timeout,
            Supplier<CompletableFuture<T>> optimizationTask) {
        
        log.debug("Triggering optimization for key: {} with min interval: {}", optimizationKey, minInterval);
        
        // Check if optimization is rate limited
        if (isRateLimited(optimizationKey, minInterval)) {
            log.info("Optimization {} is rate limited, skipping", optimizationKey);
            return CompletableFuture.failedFuture(
                new IllegalStateException("Optimization rate limited for key: " + optimizationKey)
            );
        }
        
        // Check if optimization is already running
        CompletableFuture<?> existingOptimization = activeOptimizations.get(optimizationKey);
        if (existingOptimization != null && !existingOptimization.isDone()) {
            log.info("Optimization {} already in progress, returning existing future", optimizationKey);
            @SuppressWarnings("unchecked")
            CompletableFuture<T> typedFuture = (CompletableFuture<T>) existingOptimization;
            return typedFuture;
        }
        
        // Start new optimization
        lastOptimizationTime.put(optimizationKey, LocalDateTime.now());
        
        CompletableFuture<T> optimizationFuture = optimizationTask.get()
                .orTimeout(timeout.toMillis(), java.util.concurrent.TimeUnit.MILLISECONDS)
                .whenComplete((result, throwable) -> {
                    activeOptimizations.remove(optimizationKey);
                    if (throwable != null) {
                        log.error("Optimization {} failed: {}", optimizationKey, throwable.getMessage());
                    } else {
                        log.info("Optimization {} completed successfully", optimizationKey);
                    }
                });
        
        activeOptimizations.put(optimizationKey, optimizationFuture);
        return optimizationFuture;
    }
    
    /**
     * Trigger immediate re-optimization for critical events.
     */
    public static <T> CompletableFuture<T> triggerImmediateReoptimization(
            String scheduleRegion,
            Duration timeout,
            String reason,
            Supplier<CompletableFuture<T>> optimizationTask) {
        
        String optimizationKey = "immediate_" + scheduleRegion + "_" + reason;
        log.info("Triggering immediate re-optimization for region: {} due to: {}", scheduleRegion, reason);
        
        // For immediate optimization, bypass rate limiting
        lastOptimizationTime.put(optimizationKey, LocalDateTime.now());
        
        CompletableFuture<T> optimizationFuture = optimizationTask.get()
                .orTimeout(timeout.toMillis(), java.util.concurrent.TimeUnit.MILLISECONDS)
                .whenComplete((result, throwable) -> {
                    activeOptimizations.remove(optimizationKey);
                    if (throwable != null) {
                        log.error("Immediate re-optimization {} failed: {}", optimizationKey, throwable.getMessage());
                    } else {
                        log.info("Immediate re-optimization {} completed", optimizationKey);
                    }
                });
        
        activeOptimizations.put(optimizationKey, optimizationFuture);
        return optimizationFuture;
    }
    
    /**
     * Safe schedule copying with error handling.
     */
    public static <T> T copyScheduleSafely(T originalSchedule, 
                                          Function<T, T> copyFunction,
                                          String operationContext) {
        if (originalSchedule == null) {
            log.warn("Cannot copy null schedule for operation: {}", operationContext);
            return null;
        }
        
        try {
            T copiedSchedule = copyFunction.apply(originalSchedule);
            log.debug("Successfully copied schedule for operation: {}", operationContext);
            return copiedSchedule;
        } catch (Exception e) {
            log.error("Failed to copy schedule for operation {}: {}", operationContext, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * Check if optimization should be rate limited.
     */
    public static boolean isRateLimited(String optimizationKey, Duration minInterval) {
        LocalDateTime lastTime = lastOptimizationTime.get(optimizationKey);
        if (lastTime == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return now.isBefore(lastTime.plus(minInterval));
    }
    
    /**
     * Check if optimization is currently running.
     */
    public static boolean isOptimizationRunning(String optimizationKey) {
        CompletableFuture<?> optimization = activeOptimizations.get(optimizationKey);
        return optimization != null && !optimization.isDone();
    }
    
    /**
     * Get the status of an optimization.
     */
    public static OptimizationStatus getOptimizationStatus(String optimizationKey) {
        CompletableFuture<?> optimization = activeOptimizations.get(optimizationKey);
        LocalDateTime lastTime = lastOptimizationTime.get(optimizationKey);
        
        if (optimization == null) {
            return new OptimizationStatus(optimizationKey, 
                                        lastTime != null ? StatusType.IDLE : StatusType.NEVER_RUN, 
                                        lastTime, null);
        }
        
        if (optimization.isDone()) {
            if (optimization.isCompletedExceptionally()) {
                return new OptimizationStatus(optimizationKey, StatusType.FAILED, lastTime, 
                                            "Optimization completed with error");
            } else {
                return new OptimizationStatus(optimizationKey, StatusType.COMPLETED, lastTime, null);
            }
        } else {
            return new OptimizationStatus(optimizationKey, StatusType.RUNNING, lastTime, null);
        }
    }
    
    /**
     * Cancel a running optimization.
     */
    public static boolean cancelOptimization(String optimizationKey) {
        CompletableFuture<?> optimization = activeOptimizations.get(optimizationKey);
        if (optimization != null && !optimization.isDone()) {
            boolean cancelled = optimization.cancel(true);
            if (cancelled) {
                activeOptimizations.remove(optimizationKey);
                log.info("Cancelled optimization: {}", optimizationKey);
            }
            return cancelled;
        }
        return false;
    }
    
    /**
     * Clean up completed optimizations from tracking maps.
     */
    public static void cleanupCompletedOptimizations() {
        activeOptimizations.entrySet().removeIf(entry -> entry.getValue().isDone());
        log.debug("Cleaned up completed optimizations, {} active optimizations remaining", 
                 activeOptimizations.size());
    }
    
    /**
     * Get count of active optimizations.
     */
    public static int getActiveOptimizationCount() {
        return (int) activeOptimizations.values().stream().filter(future -> !future.isDone()).count();
    }
    
    /**
     * Optimization status information.
     */
    public static class OptimizationStatus {
        public final String key;
        public final StatusType status;
        public final LocalDateTime lastExecutionTime;
        public final String message;
        
        public OptimizationStatus(String key, StatusType status, LocalDateTime lastExecutionTime, String message) {
            this.key = key;
            this.status = status;
            this.lastExecutionTime = lastExecutionTime;
            this.message = message;
        }
        
        public boolean isActive() {
            return status == StatusType.RUNNING;
        }
        
        public boolean hasRun() {
            return status != StatusType.NEVER_RUN;
        }
    }
    
    /**
     * Optimization status types.
     */
    public enum StatusType {
        NEVER_RUN, IDLE, RUNNING, COMPLETED, FAILED
    }
}
