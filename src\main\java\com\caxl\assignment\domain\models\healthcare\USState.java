package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * USState represents a US state with its specific compliance rules and regulations
 * for homecare scheduling and care staff management.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class USState {

    @NotBlank(message = "State code is required")
    @JsonProperty("state_code")
    private String stateCode; // e.g., "CA", "TX", "NY"

    @NotBlank(message = "State name is required")
    @JsonProperty("state_name")
    private String stateName; // e.g., "California", "Texas", "New York"

    @Valid
    @NotNull(message = "Compliance rules are required")
    @JsonProperty("compliance_rules")
    private List<ComplianceRule> complianceRules;

    @Valid
    @JsonProperty("working_hour_regulations")
    private WorkingHourRegulations workingHourRegulations;

    @Valid
    @JsonProperty("licensing_requirements")
    private LicensingRequirements licensingRequirements;

    @JsonProperty("overtime_regulations")
    private OvertimeRegulations overtimeRegulations;

    @JsonProperty("background_check_requirements")
    private BackgroundCheckRequirements backgroundCheckRequirements;

    /**
     * State-specific compliance rule.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComplianceRule {

        @NotBlank(message = "Rule ID is required")
        @JsonProperty("rule_id")
        private String ruleId;

        @NotBlank(message = "Rule name is required")
        @JsonProperty("rule_name")
        private String ruleName;

        @JsonProperty("rule_description")
        private String ruleDescription;

        @NotNull(message = "Rule type is required")
        @JsonProperty("rule_type")
        private RuleType ruleType;

        @JsonProperty("enforcement_level")
        private EnforcementLevel enforcementLevel;

        @JsonProperty("parameters")
        private Map<String, Object> parameters;

        @JsonProperty("effective_date")
        private LocalDateTime effectiveDate;

        @JsonProperty("expiration_date")
        private LocalDateTime expirationDate;

        @JsonProperty("penalty_description")
        private String penaltyDescription;
    }

    /**
     * Working hour regulations by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WorkingHourRegulations {

        @JsonProperty("max_hours_per_day")
        private Integer maxHoursPerDay;

        @JsonProperty("max_hours_per_week")
        private Integer maxHoursPerWeek;

        @JsonProperty("max_consecutive_days")
        private Integer maxConsecutiveDays;

        @JsonProperty("required_break_hours")
        private Integer requiredBreakHours;

        @JsonProperty("min_hours_between_shifts")
        private Integer minHoursBetweenShifts;

        @JsonProperty("overtime_threshold_hours")
        private Integer overtimeThresholdHours;

        @JsonProperty("mandatory_rest_period")
        private boolean mandatoryRestPeriod;
    }

    /**
     * Licensing requirements by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LicensingRequirements {

        @JsonProperty("required_licenses")
        private List<String> requiredLicenses;

        @JsonProperty("reciprocity_states")
        private List<String> reciprocityStates;

        @JsonProperty("temporary_license_allowed")
        private boolean temporaryLicenseAllowed;

        @JsonProperty("license_verification_required")
        private boolean licenseVerificationRequired;

        @JsonProperty("continuing_education_hours")
        private Integer continuingEducationHours;

        @JsonProperty("renewal_notification_days")
        private Integer renewalNotificationDays;
    }

    /**
     * Overtime regulations by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OvertimeRegulations {

        @JsonProperty("overtime_rate_multiplier")
        private Double overtimeRateMultiplier; // e.g., 1.5 for time-and-a-half

        @JsonProperty("double_time_threshold")
        private Integer doubleTimeThreshold;

        @JsonProperty("overtime_approval_required")
        private boolean overtimeApprovalRequired;

        @JsonProperty("max_overtime_hours_per_week")
        private Integer maxOvertimeHoursPerWeek;

        @JsonProperty("weekend_overtime_rules")
        private boolean weekendOvertimeRules;
    }

    /**
     * Background check requirements by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BackgroundCheckRequirements {

        @JsonProperty("criminal_background_check")
        private boolean criminalBackgroundCheck;

        @JsonProperty("fingerprint_check")
        private boolean fingerprintCheck;

        @JsonProperty("registry_check")
        private boolean registryCheck;

        @JsonProperty("drug_screening")
        private boolean drugScreening;

        @JsonProperty("reference_check_count")
        private Integer referenceCheckCount;

        @JsonProperty("validity_period_months")
        private Integer validityPeriodMonths;

        @JsonProperty("renewal_required")
        private boolean renewalRequired;
    }

    /**
     * Types of compliance rules.
     */
    public enum RuleType {
        WORKING_HOURS,
        LICENSING,
        OVERTIME,
        BREAK_TIME,
        TRAVEL_TIME,
        SUPERVISION,
        DOCUMENTATION,
        PATIENT_SAFETY,
        INFECTION_CONTROL,
        MEDICATION_MANAGEMENT,
        EMERGENCY_PROCEDURES,
        CONTINUING_EDUCATION,
        BACKGROUND_CHECK,
        INSURANCE,
        WORKER_COMPENSATION
    }

    /**
     * Enforcement levels for compliance rules.
     */
    public enum EnforcementLevel {
        MANDATORY,
        RECOMMENDED,
        OPTIONAL,
        CONDITIONAL
    }

    /**
     * Check if a specific rule is active for the given date.
     */
    public boolean isRuleActive(String ruleId, LocalDateTime checkDate) {
        return complianceRules.stream()
                .filter(rule -> rule.getRuleId().equals(ruleId))
                .anyMatch(rule -> isRuleEffective(rule, checkDate));
    }

    private boolean isRuleEffective(ComplianceRule rule, LocalDateTime checkDate) {
        boolean afterEffective = rule.getEffectiveDate() == null || 
                                !checkDate.isBefore(rule.getEffectiveDate());
        boolean beforeExpiration = rule.getExpirationDate() == null || 
                                  !checkDate.isAfter(rule.getExpirationDate());
        return afterEffective && beforeExpiration;
    }

    /**
     * Get all active compliance rules for a given date.
     */
    public List<ComplianceRule> getActiveRules(LocalDateTime checkDate) {
        return complianceRules.stream()
                .filter(rule -> isRuleEffective(rule, checkDate))
                .toList();
    }

    /**
     * Get active rules by type.
     */
    public List<ComplianceRule> getActiveRulesByType(RuleType ruleType, LocalDateTime checkDate) {
        return getActiveRules(checkDate).stream()
                .filter(rule -> rule.getRuleType() == ruleType)
                .toList();
    }
}
