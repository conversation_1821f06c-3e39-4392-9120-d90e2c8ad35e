# Prometheus configuration for homecare scheduling monitoring
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Homecare scheduling service metrics
  - job_name: 'homecare-scheduling'
    static_configs:
      - targets: ['homecare-service:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  # PostgreSQL metrics
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Redis metrics  
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # System metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Example alerting rules for homecare scheduling
rule_files:
  - "/etc/prometheus/rules.yml"
