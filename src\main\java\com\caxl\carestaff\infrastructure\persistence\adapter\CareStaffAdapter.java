package com.caxl.carestaff.infrastructure.persistence.adapter;

import com.caxl.carestaff.application.port.out.persistence.CareStaffPort;
import com.caxl.carestaff.domain.entities.Appointment;
import com.caxl.carestaff.infrastructure.persistence.repository.CareStaffRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.WKBReader;
import org.springframework.stereotype.Repository;

import java.sql.Array;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * Persistence adapter for care staff operations with spatial queries.
 * Implements the CareStaffPort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CareStaffAdapter implements CareStaffPort {

    private final CareStaffRepository repository;
    private final WKBReader wkbReader = new WKBReader();

    @Override
    public List<CareStaffDomain> findPotentialStaffBySkillsAndProximity(
            List<UUID> requiredSkillIds, 
            Point patientLocationCoords, 
            double searchRadiusKm, 
            boolean requireAllSkills) {
        
        log.debug("Finding potential staff by skills and proximity. Skills: {}, Radius: {}km, RequireAll: {}", 
                 requiredSkillIds.size(), searchRadiusKm, requireAllSkills);

        if (patientLocationCoords == null) {
            log.warn("Patient location coordinates are null");
            return List.of();
        }

        UUID[] skillArray = requiredSkillIds.toArray(new UUID[0]);
        double radiusMeters = searchRadiusKm * 1000; // Convert to meters
        
        List<Object[]> results = repository.findPotentialStaffBySkillsAndProximityNative(
                skillArray,
                requiredSkillIds.size(),
                patientLocationCoords.getY(), // latitude
                patientLocationCoords.getX(), // longitude
                radiusMeters,
                requireAllSkills
        );

        return results.stream()
                .map(this::mapToCareStaffDomain)
                .toList();
    }

    @Override
    public List<Polygon> findServiceGeofencesForStaff(UUID staffId, List<String> geofenceTypes) {
        log.debug("Finding service geofences for staff: {} with types: {}", staffId, geofenceTypes);

        String[] typeArray = geofenceTypes.toArray(new String[0]);
        List<Object> results = repository.findServiceGeofencesForStaffNative(staffId, typeArray);

        List<Polygon> polygons = new ArrayList<>();
        for (Object result : results) {
            try {
                if (result instanceof byte[] wkb) {
                    Polygon polygon = (Polygon) wkbReader.read(wkb);
                    polygons.add(polygon);
                }
            } catch (Exception e) {
                log.warn("Error parsing geofence polygon for staff {}: {}", staffId, e.getMessage());
            }
        }

        return polygons;
    }

    @Override
    public List<Appointment> findOverlappingAppointments(
            UUID staffId, 
            ServiceRequestTimeWindow requestTimeWindow, 
            int overlapThresholdMinutes) {
        
        log.debug("Finding overlapping appointments for staff: {} in window: {} - {}", 
                 staffId, requestTimeWindow.startTime(), requestTimeWindow.endTime());

        List<Object[]> results = repository.findOverlappingAppointmentsNative(
                staffId,
                requestTimeWindow.startTime(),
                requestTimeWindow.endTime(),
                overlapThresholdMinutes
        );

        return results.stream()
                .map(this::mapToAppointment)
                .toList();
    }

    /**
     * Map native query result to CareStaffDomain.
     */
    private CareStaffDomain mapToCareStaffDomain(Object[] row) {
        try {
            UUID careStaffId = (UUID) row[0];
            String firstName = (String) row[1];
            String lastName = (String) row[2];
            String name = (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
            
            Point baseLocation = null;
            if (row[3] instanceof byte[] wkb) {
                baseLocation = (Point) wkbReader.read(wkb);
            }
            
            Integer experienceYears = (Integer) row[4];
            Boolean isActive = (Boolean) row[5];
            
            List<UUID> skillIds = new ArrayList<>();
            if (row[6] instanceof Array skillArray) {
                Object[] skills = (Object[]) skillArray.getArray();
                skillIds = Arrays.stream(skills)
                        .filter(UUID.class::isInstance)
                        .map(UUID.class::cast)
                        .toList();
            }
            
            List<String> operatingZones = new ArrayList<>();
            if (row[7] instanceof Array zoneArray) {
                Object[] zones = (Object[]) zoneArray.getArray();
                operatingZones = Arrays.stream(zones)
                        .filter(String.class::isInstance)
                        .map(String.class::cast)
                        .toList();
            }
            
            List<String> languages = new ArrayList<>();
            if (row[8] instanceof Array langArray) {
                Object[] langs = (Object[]) langArray.getArray();
                languages = Arrays.stream(langs)
                        .filter(String.class::isInstance)
                        .map(String.class::cast)
                        .toList();
            }

            return new CareStaffDomain(
                    careStaffId,
                    name.trim(),
                    baseLocation,
                    skillIds,
                    List.of(), // certificationIds - TODO: add to query
                    languages,
                    experienceYears != null ? experienceYears : 0,
                    isActive != null ? isActive : false,
                    operatingZones
            );
            
        } catch (Exception e) {
            log.error("Error mapping care staff domain: {}", e.getMessage());
            throw new RuntimeException("Error mapping care staff data", e);
        }
    }

    /**
     * Map native query result to Appointment.
     */
    private Appointment mapToAppointment(Object[] row) {
        return Appointment.builder()
                .appointmentId((UUID) row[0])
                .scheduledStartTime((LocalDateTime) row[1])
                .scheduledEndTime((LocalDateTime) row[2])
                .serviceRequestId((UUID) row[3])
                .patientId((UUID) row[4])
                .status((String) row[5])
                .build();
    }
}
