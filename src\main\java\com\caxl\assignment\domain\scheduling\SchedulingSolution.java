package com.caxl.assignment.domain.scheduling;

import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty;
import ai.timefold.solver.core.api.domain.solution.PlanningScore;
import ai.timefold.solver.core.api.domain.solution.PlanningSolution;
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Timefold planning solution for clinician scheduling.
 * Uses dual planning variables for optimal assignment and time slot optimization.
 */
@PlanningSolution
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchedulingSolution {

    /**
     * Planning entities: Patient assignments to be optimized by Timefold.
     * Each assignment optimizes both clinician and time slot.
     */
    @PlanningEntityCollectionProperty
    private List<PatientAssignment> assignments;

    /**
     * Available clinicians (value range for assignment optimization).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clinicianRange")
    private List<Clinician> clinicians;

    /**
     * Available time slots (value range for time optimization).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "timeSlotRange")
    private List<PatientAssignment.TimeSlot> timeSlots;

    /**
     * Patients (problem facts for assignment creation).
     */
    @ProblemFactCollectionProperty
    private List<Patient> patients;

    /**
     * Planning score calculated by Timefold constraint provider.
     */
    @PlanningScore
    private HardSoftScore score;

    /**
     * Scheduling period (multiple days supported).
     */
    private List<LocalDate> schedulingPeriod;

    /**
     * Get all patients with complete assignments.
     */
    public List<Patient> getAssignedPatients() {
        return assignments.stream()
                .filter(PatientAssignment::isComplete)
                .map(PatientAssignment::getPatient)
                .collect(Collectors.toList());
    }

    /**
     * Get all unassigned patients.
     */
    public List<Patient> getUnassignedPatients() {
        Set<Patient> assignedPatients = Set.copyOf(getAssignedPatients());
        return patients.stream()
                .filter(patient -> !assignedPatients.contains(patient))
                .collect(Collectors.toList());
    }

    /**
     * Calculate assignment completion rate (0.0 to 1.0).
     */
    public double getAssignmentRate() {
        if (patients.isEmpty()) {
            return 1.0;
        }
        return (double) getAssignedPatients().size() / patients.size();
    }

    /**
     * Get total workload points assigned across all assignments.
     */
    public int getTotalAssignedWorkload() {
        return assignments.stream()
                .filter(PatientAssignment::isComplete)
                .mapToInt(PatientAssignment::getWorkloadPoints)
                .sum();
    }

    /**
     * Calculate workload distribution fairness (lower variance is better).
     */
    public double getWorkloadFairnessScore() {
        var clinicianWorkloads = assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .collect(Collectors.groupingBy(
                    PatientAssignment::getAssignedClinician,
                    Collectors.summingInt(PatientAssignment::getWorkloadPoints)
                ));

        if (clinicianWorkloads.size() < 2) {
            return 0.0; // Perfect fairness with 0 or 1 clinician
        }

        double mean = clinicianWorkloads.values().stream()
                .mapToInt(Integer::intValue)
                .average()
                .orElse(0.0);

        double variance = clinicianWorkloads.values().stream()
                .mapToDouble(workload -> Math.pow(workload - mean, 2))
                .average()
                .orElse(0.0);

        return Math.sqrt(variance); // Standard deviation
    }

    /**
     * Check if the solution is feasible (no hard constraint violations).
     */
    public boolean isFeasible() {
        return score != null && score.hardScore() >= 0;
    }

    /**
     * Get the number of assignments with assigned clinicians.
     */
    public long getAssignedCount() {
        return assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .count();
    }

    /**
     * Get the number of complete assignments (both clinician and time slot).
     */
    public long getCompleteAssignmentCount() {
        return assignments.stream()
                .filter(PatientAssignment::isComplete)
                .count();
    }

    /**
     * Get assignments for a specific date.
     */
    public List<PatientAssignment> getAssignmentsForDate(LocalDate date) {
        return assignments.stream()
                .filter(assignment -> assignment.getAssignmentDate().equals(date))
                .collect(Collectors.toList());
    }

    /**
     * Get assignments for a specific clinician.
     */
    public List<PatientAssignment> getAssignmentsForClinician(Clinician clinician) {
        return assignments.stream()
                .filter(assignment -> clinician.equals(assignment.getAssignedClinician()))
                .collect(Collectors.toList());
    }

    /**
     * Get average preference score across all complete assignments.
     */
    public double getAveragePreferenceScore() {
        return assignments.stream()
                .filter(PatientAssignment::isComplete)
                .mapToDouble(PatientAssignment::getPreferenceScore)
                .average()
                .orElse(0.0);
    }

    /**
     * Get total number of assignments in the solution.
     */
    public int getTotalAssignmentCount() {
        return assignments.size();
    }

    /**
     * Check if all patients are assigned.
     */
    public boolean isCompleteAssignment() {
        return getUnassignedPatients().isEmpty();
    }

    /**
     * Get summary statistics for the solution.
     */
    public SolutionSummary getSummary() {
        return SolutionSummary.builder()
                .totalPatients(patients.size())
                .assignedPatients(getAssignedPatients().size())
                .unassignedPatients(getUnassignedPatients().size())
                .assignmentRate(getAssignmentRate())
                .totalShifts(getTotalShiftCount())
                .assignedShifts((int) getAssignedShiftCount())
                .activeShifts((int) getActiveShiftCount())
                .totalWorkload(getTotalAssignedWorkload())
                .averageUtilization(getAverageShiftUtilization())
                .workloadFairness(getWorkloadFairnessScore())
                .isFeasible(isFeasible())
                .score(score)
                .build();
    }

    /**
     * Summary statistics for the scheduling solution.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SolutionSummary {
        private int totalPatients;
        private int assignedPatients;
        private int unassignedPatients;
        private double assignmentRate;
        private int totalShifts;
        private int assignedShifts;
        private int activeShifts;
        private int totalWorkload;
        private double averageUtilization;
        private double workloadFairness;
        private boolean isFeasible;
        private HardSoftScore score;
    }
}
