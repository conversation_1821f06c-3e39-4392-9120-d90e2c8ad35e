package com.caxl.assignment.domain.constraints.enhanced;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintCollectors;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import com.caxl.assignment.application.services.realtime.ConstraintConfigurationService;
import com.caxl.assignment.application.services.traffic.TrafficAwareService;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.scheduling.PatientAssignment;
import com.caxl.assignment.domain.scheduling.SchedulingSolution;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Set;

/**
 * Enhanced dynamic constraint provider with real-time configuration support.
 * Integrates with traffic awareness and flexible constraint management.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EnhancedSchedulingConstraints implements ConstraintProvider {

    private final ConstraintConfigurationService constraintConfigService;
    private final TrafficAwareService trafficAwareService;

    @Override
    public Constraint[] defineConstraints(ConstraintFactory factory) {
        return new Constraint[]{
            // === HARD CONSTRAINTS (Feasibility) ===
            caregiverAvailabilityConstraint(factory),
            skillRequirementConstraint(factory),
            maximumDailyHoursConstraint(factory),
            patientTimeWindowConstraint(factory),
            oneAssignmentPerPatientConstraint(factory),
            caregiverCapacityConstraint(factory),
            
            // === SOFT CONSTRAINTS (Optimization) ===
            balanceWorkloadConstraint(factory),
            minimizeTravelTimeConstraint(factory),
            continuityOfCareConstraint(factory),
            urgentVisitPriorityConstraint(factory),
            preferredCaregiverConstraint(factory),
            minimizeOvertimeConstraint(factory),
            maximizeCaregiverUtilizationConstraint(factory),
            regionCoverageConstraint(factory),
            teamCohesionConstraint(factory),
            
            // === DYNAMIC CONSTRAINTS (Event-driven) ===
            trafficDelayPenaltyConstraint(factory),
            emergencyResponseConstraint(factory),
            lastMinuteChangesPenaltyConstraint(factory)
        };
    }

    // =========================================================================
    // HARD CONSTRAINTS - Core Feasibility Requirements
    // =========================================================================

    /**
     * Caregiver must be available during assignment time.
     */
    private Constraint caregiverAvailabilityConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> !isCareStaffAvailable(assignment))
                .penalize(HardSoftScore.ONE_HARD,
                        assignment -> getHardConstraintWeight("caregiver_availability"))
                .asConstraint("Caregiver availability");
    }

    /**
     * Caregiver must have required skills for patient care.
     */
    private Constraint skillRequirementConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> !hasRequiredSkills(assignment))
                .penalize(HardSoftScore.ONE_HARD,
                        assignment -> getHardConstraintWeight("skill_requirement"))
                .asConstraint("Skill requirements");
    }

    /**
     * Caregiver cannot exceed maximum daily working hours.
     */
    private Constraint maximumDailyHoursConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getCaregiver,
                        assignment -> assignment.getStartTime().toLocalDate(),
                        ConstraintCollectors.sum(assignment -> 
                            getDurationMinutes(assignment.getStartTime(), assignment.getEndTime())))
                .filter((caregiver, date, totalMinutes) -> 
                    totalMinutes > caregiver.getMaxDailyHours() * 60)
                .penalize(HardSoftScore.ONE_HARD,
                        (caregiver, date, totalMinutes) -> 
                            getHardConstraintWeight("maximum_daily_hours") * 
                            (totalMinutes - caregiver.getMaxDailyHours() * 60))
                .asConstraint("Maximum daily hours");
    }

    /**
     * Patient visits must occur within their time windows.
     */
    private Constraint patientTimeWindowConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> !isWithinTimeWindow(assignment))
                .penalize(HardSoftScore.ONE_HARD,
                        assignment -> getHardConstraintWeight("patient_time_window"))
                .asConstraint("Patient time window");
    }

    /**
     * Each patient can only have one assignment per visit.
     */
    private Constraint oneAssignmentPerPatientConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getPatient, ConstraintCollectors.count())
                .filter((patient, count) -> count > 1)
                .penalize(HardSoftScore.ONE_HARD,
                        (patient, count) -> getHardConstraintWeight("one_assignment_per_patient") * (count - 1))
                .asConstraint("One assignment per patient");
    }

    /**
     * Caregiver cannot exceed maximum number of patients per day.
     */
    private Constraint caregiverCapacityConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getCaregiver,
                        assignment -> assignment.getStartTime().toLocalDate(),
                        ConstraintCollectors.count())
                .filter((caregiver, date, count) -> count > caregiver.getMaxPatientsPerDay())
                .penalize(HardSoftScore.ONE_HARD,
                        (caregiver, date, count) -> 
                            getHardConstraintWeight("caregiver_capacity") * 
                            (count - caregiver.getMaxPatientsPerDay()))
                .asConstraint("Caregiver capacity");
    }

    // =========================================================================
    // SOFT CONSTRAINTS - Optimization Objectives
    // =========================================================================

    /**
     * Balance workload among caregivers.
     */
    private Constraint balanceWorkloadConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getCaregiver, ConstraintCollectors.count())
                .reward(HardSoftScore.ONE_SOFT,
                        (caregiver, count) -> getSoftConstraintWeight("balance_workload") * 
                            calculateWorkloadBalance(count))
                .asConstraint("Balance workload");
    }

    /**
     * Minimize travel time between assignments with real-time traffic consideration.
     */
    private Constraint minimizeTravelTimeConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .join(PatientAssignment.class,
                        assignment1 -> assignment1.getCaregiver(),
                        assignment2 -> assignment2.getCaregiver())
                .filter((assignment1, assignment2) -> 
                    assignment1.getEndTime().isBefore(assignment2.getStartTime()) &&
                    assignment1.getEndTime().toLocalDate().equals(assignment2.getStartTime().toLocalDate()))
                .penalize(HardSoftScore.ONE_SOFT,
                        (assignment1, assignment2) -> 
                            getSoftConstraintWeight("minimize_travel_time") * 
                            getRealTimeTravelTimeMinutes(assignment1, assignment2))
                .asConstraint("Minimize travel time");
    }

    /**
     * Promote continuity of care by assigning same caregiver to regular patients.
     */
    private Constraint continuityOfCareConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> isPreferredCaregiver(assignment))
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("continuity_of_care"))
                .asConstraint("Continuity of care");
    }

    /**
     * Prioritize urgent visits for immediate scheduling.
     */
    private Constraint urgentVisitPriorityConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getPatient().isUrgent())
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("urgent_visit_priority") * 
                            getUrgencyMultiplier(assignment.getPatient()))
                .asConstraint("Urgent visit priority");
    }

    /**
     * Assign patients to their preferred caregivers when possible.
     */
    private Constraint preferredCaregiverConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> isPatientPreferredCaregiver(assignment))
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("preferred_caregiver"))
                .asConstraint("Preferred caregiver");
    }

    /**
     * Minimize overtime hours for cost optimization.
     */
    private Constraint minimizeOvertimeConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getCaregiver,
                        assignment -> assignment.getStartTime().toLocalDate(),
                        ConstraintCollectors.sum(assignment -> 
                            getDurationMinutes(assignment.getStartTime(), assignment.getEndTime())))
                .filter((caregiver, date, totalMinutes) -> 
                    totalMinutes > caregiver.getRegularHours() * 60)
                .penalize(HardSoftScore.ONE_SOFT,
                        (caregiver, date, totalMinutes) -> 
                            getSoftConstraintWeight("minimize_overtime") * 
                            (totalMinutes - caregiver.getRegularHours() * 60))
                .asConstraint("Minimize overtime");
    }

    /**
     * Maximize caregiver utilization efficiency.
     */
    private Constraint maximizeCaregiverUtilizationConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getCaregiver, ConstraintCollectors.count())
                .reward(HardSoftScore.ONE_SOFT,
                        (caregiver, count) -> getSoftConstraintWeight("maximize_utilization") * count)
                .asConstraint("Maximize caregiver utilization");
    }

    /**
     * Ensure adequate coverage across all regions.
     */
    private Constraint regionCoverageConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(assignment -> assignment.getPatient().getRegion(), ConstraintCollectors.count())
                .reward(HardSoftScore.ONE_SOFT,
                        (region, count) -> getSoftConstraintWeight("region_coverage") * count)
                .asConstraint("Region coverage");
    }

    /**
     * Promote team cohesion by keeping caregivers in familiar regions.
     */
    private Constraint teamCohesionConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> isCaregiverInFamiliarRegion(assignment))
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("team_cohesion"))
                .asConstraint("Team cohesion");
    }

    // =========================================================================
    // DYNAMIC CONSTRAINTS - Real-time Event Response
    // =========================================================================

    /**
     * Penalize assignments affected by traffic delays.
     */
    private Constraint trafficDelayPenaltyConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> hasSignificantTrafficDelay(assignment))
                .penalize(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("traffic_delay_penalty") * 
                            getTrafficDelayPenalty(assignment))
                .asConstraint("Traffic delay penalty");
    }

    /**
     * Boost priority for emergency response assignments.
     */
    private Constraint emergencyResponseConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getPatient().isEmergency())
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("emergency_response") * 1000)
                .asConstraint("Emergency response");
    }

    /**
     * Penalize frequent last-minute schedule changes.
     */
    private Constraint lastMinuteChangesPenaltyConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> isLastMinuteAssignment(assignment))
                .penalize(HardSoftScore.ONE_SOFT,
                        assignment -> getSoftConstraintWeight("last_minute_changes_penalty"))
                .asConstraint("Last minute changes penalty");
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    private boolean isCaregiverAvailable(PatientAssignment assignment) {
        Caregiver caregiver = assignment.getCaregiver();
        LocalDateTime startTime = assignment.getStartTime();
        LocalDateTime endTime = assignment.getEndTime();

        return caregiver.isAvailableAt(startTime, endTime);
    }

    private boolean hasRequiredSkills(PatientAssignment assignment) {
        Set<String> requiredSkills = assignment.getPatient().getRequiredSkills();
        Set<String> caregiverSkills = assignment.getCaregiver().getSkills();
        return caregiverSkills.containsAll(requiredSkills);
    }

    private boolean isWithinTimeWindow(PatientAssignment assignment) {
        LocalTime startTime = assignment.getStartTime().toLocalTime();
        Patient patient = assignment.getPatient();
        
        return !startTime.isBefore(patient.getEarliestStartTime()) &&
               !startTime.isAfter(patient.getLatestStartTime());
    }

    private int getDurationMinutes(LocalDateTime start, LocalDateTime end) {
        return (int) Duration.between(start, end).toMinutes();
    }

    private int getRealTimeTravelTimeMinutes(PatientAssignment from, PatientAssignment to) {
        try {
            Duration travelTime = trafficAwareService.getRealTimeTravelTime(
                from.getCaregiver(), to.getPatient());
            return (int) travelTime.toMinutes();
        } catch (Exception e) {
            log.warn("Failed to get real-time travel time, using default: {}", e.getMessage());
            return 30; // Default travel time
        }
    }

    private boolean isPreferredCaregiver(PatientAssignment assignment) {
        Patient patient = assignment.getPatient();
        return patient.getPreferredCaregivers().contains(assignment.getCaregiver().getId());
    }

    private boolean isPatientPreferredCaregiver(PatientAssignment assignment) {
        return assignment.getPatient().getPreferredCaregivers()
                .contains(assignment.getCaregiver().getId());
    }

    private int getUrgencyMultiplier(Patient patient) {
        return switch (patient.getUrgencyLevel()) {
            case "CRITICAL" -> 1000;
            case "HIGH" -> 500;
            case "MEDIUM" -> 200;
            default -> 100;
        };
    }

    private int calculateWorkloadBalance(int assignmentCount) {
        // Reward balanced workload, penalize extreme values
        int ideal = 6; // Ideal number of patients per caregiver
        return Math.abs(assignmentCount - ideal) * -1;
    }

    private boolean isCaregiverInFamiliarRegion(PatientAssignment assignment) {
        String patientRegion = assignment.getPatient().getRegion();
        return assignment.getCaregiver().getFamiliarRegions().contains(patientRegion);
    }

    private boolean hasSignificantTrafficDelay(PatientAssignment assignment) {
        TrafficAwareService.TrafficCondition condition = 
            trafficAwareService.getRegionTrafficCondition(assignment.getPatient().getRegion());
        return condition == TrafficAwareService.TrafficCondition.HEAVY || 
               condition == TrafficAwareService.TrafficCondition.SEVERE;
    }

    private int getTrafficDelayPenalty(PatientAssignment assignment) {
        TrafficAwareService.TrafficCondition condition = 
            trafficAwareService.getRegionTrafficCondition(assignment.getPatient().getRegion());
        return switch (condition) {
            case SEVERE -> 500;
            case HEAVY -> 200;
            case NORMAL -> 50;
            default -> 0;
        };
    }

    private boolean isLastMinuteAssignment(PatientAssignment assignment) {
        return assignment.getCreatedAt() != null &&
               assignment.getCreatedAt().isAfter(LocalDateTime.now().minusHours(2));
    }

    // =========================================================================
    // CONSTRAINT WEIGHT MANAGEMENT
    // =========================================================================

    private int getHardConstraintWeight(String constraintName) {
        return constraintConfigService.getConstraintWeight(constraintName, true);
    }

    private int getSoftConstraintWeight(String constraintName) {
        return constraintConfigService.getConstraintWeight(constraintName, false);
    }
}
