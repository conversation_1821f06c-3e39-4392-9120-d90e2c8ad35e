# Configuration Guide - CareStaff Matching Service

## Overview

The CareStaff Matching Service uses a flexible, JSON-based configuration system that allows runtime adjustments of matching behavior without code changes. This guide explains all configuration options and how to customize them.

## Configuration Architecture

### Configuration Storage
- **Database-driven**: Configurations stored in `matching_configuration` table
- **JSON format**: Flexible, hierarchical configuration structure
- **Runtime changes**: No application restart required
- **Version control**: Multiple configurations with activation control

### Configuration Hierarchy

```
MatchingCriteria
├── filters (Hard Constraints)
├── scoring_weights (Soft Constraints)
├── geography (Spatial Settings)
└── availability (Time Settings)
```

## Default Configuration

### Complete Default Configuration

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 50.0,
    "mustRespectGeoServiceArea": true,
    "mustRespectAvailability": true,
    "mustNotBeBarred": true,
    "minScoreThreshold": 0.0
  },
  "scoring_weights": {
    "skillMatchBonusPerRequiredSkill": 10.0,
    "skillMatchAllBonus": 50.0,
    "proximityKmPenalty": -1.0,
    "geoServiceAreaBonus": 20.0,
    "availabilityWindowFitPenaltyPerMinuteDeviation": -0.5,
    "preferredCareStaffBonus": 15.0,
    "continuityBonusPerRecentVisit": 5.0,
    "languageMatchBonus": 8.0,
    "experienceLevelBonusPerYear": 2.0
  },
  "geography": {
    "staffServiceGeofenceTypes": ["service_area", "county_area"],
    "geofenceStrictContainmentOnly": true
  },
  "availability": {
    "overlapThresholdMinutes": 1,
    "minTimeBeforeVisitMinutes": 30,
    "minTimeAfterVisitMinutes": 30
  }
}
```

## Configuration Sections

### 1. Filters (Hard Constraints)

Hard constraints that must be satisfied for a match to be considered.

#### `requiredSkillsMustMatchAll`
- **Type**: Boolean
- **Default**: `false`
- **Description**: Whether all required skills must be matched (true) or any skill match is sufficient (false)
- **Impact**: Stricter matching when true, more flexible when false

#### `proximitySearchRadiusKm`
- **Type**: Double
- **Default**: `50.0`
- **Description**: Maximum search radius in kilometers for finding potential carestaff
- **Range**: 1.0 - 500.0
- **Impact**: Larger radius finds more candidates but may include distant staff

#### `mustRespectGeoServiceArea`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Whether to enforce geofence service area boundaries
- **Impact**: Ensures staff only work in their designated service areas

#### `mustRespectAvailability`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Whether to check for appointment conflicts
- **Impact**: Prevents double-booking when true

#### `mustNotBeBarred`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Whether to exclude staff barred by patients
- **Impact**: Respects patient preferences when true

#### `minScoreThreshold`
- **Type**: Double
- **Default**: `0.0`
- **Description**: Minimum score required for a suggestion to be included
- **Range**: -100.0 - 100.0
- **Impact**: Higher thresholds reduce suggestion count but improve quality

### 2. Scoring Weights (Soft Constraints)

Weights used for optimizing and ranking matches.

#### Skill-Related Weights

**`skillMatchBonusPerRequiredSkill`**
- **Default**: `10.0`
- **Description**: Points awarded for each required skill that the staff possesses
- **Example**: If 3 skills required and staff has 2, bonus = 2 × 10.0 = 20 points

**`skillMatchAllBonus`**
- **Default**: `50.0`
- **Description**: Additional bonus when staff has ALL required skills
- **Usage**: Only applied when `requiredSkillsMustMatchAll` is false

#### Geographic Weights

**`proximityKmPenalty`**
- **Default**: `-1.0`
- **Description**: Penalty per kilometer distance from patient
- **Example**: Staff 15km away gets penalty = 15 × (-1.0) = -15 points

**`geoServiceAreaBonus`**
- **Default**: `20.0`
- **Description**: Bonus for staff working within their designated service area
- **Usage**: Applied when staff passes geofence validation

#### Relationship Weights

**`preferredCareStaffBonus`**
- **Default**: `15.0`
- **Description**: Bonus for staff explicitly preferred by patient
- **Impact**: Encourages continuity of care relationships

**`continuityBonusPerRecentVisit`**
- **Default**: `5.0`
- **Description**: Bonus multiplier for recent visit history
- **Usage**: Multiplied by continuity score from ContinuityOfCareService

**`languageMatchBonus`**
- **Default**: `8.0`
- **Description**: Bonus when staff speaks patient's preferred language
- **Impact**: Improves communication and patient satisfaction

#### Experience and Time Weights

**`experienceLevelBonusPerYear`**
- **Default**: `2.0`
- **Description**: Bonus per year of staff experience
- **Example**: Staff with 5 years gets bonus = 5 × 2.0 = 10 points

**`availabilityWindowFitPenaltyPerMinuteDeviation`**
- **Default**: `-0.5`
- **Description**: Penalty per minute deviation from optimal scheduling
- **Usage**: Applied by AvailabilityWindowScoringService

### 3. Geography Settings

Spatial and geographic configuration options.

#### `staffServiceGeofenceTypes`
- **Type**: Array of Strings
- **Default**: `["service_area", "county_area"]`
- **Description**: Types of geofences to check for service area validation
- **Options**: Any geofence types defined in your geofences table

#### `geofenceStrictContainmentOnly`
- **Type**: Boolean
- **Default**: `true`
- **Description**: Whether to use strict containment (ST_Contains) or allow intersection (ST_Intersects)
- **Impact**: Strict mode requires patient to be fully within service area

### 4. Availability Settings

Time-related configuration for availability checking.

#### `overlapThresholdMinutes`
- **Type**: Integer
- **Default**: `1`
- **Description**: Minimum overlap in minutes to consider appointments conflicting
- **Range**: 1 - 60
- **Impact**: Lower values are more strict about conflicts

#### `minTimeBeforeVisitMinutes`
- **Type**: Integer
- **Default**: `30`
- **Description**: Minimum buffer time required before a visit
- **Usage**: Used by AvailabilityWindowScoringService for scheduling optimization

#### `minTimeAfterVisitMinutes`
- **Type**: Integer
- **Default**: `30`
- **Description**: Minimum buffer time required after a visit
- **Usage**: Allows for travel time and documentation

## Configuration Management

### Viewing Active Configuration

```sql
-- View the active configuration
SELECT config_name, criteria_json, created_at, updated_at
FROM matching_configuration 
WHERE is_active = true;
```

### Creating New Configuration

```sql
-- Insert new configuration
INSERT INTO matching_configuration (
    config_id, config_name, criteria_json, is_active, created_by
) VALUES (
    uuid_generate_v4(),
    'high_priority_config',
    '{
        "filters": {
            "requiredSkillsMustMatchAll": true,
            "proximitySearchRadiusKm": 25.0,
            "minScoreThreshold": 30.0
        },
        "scoring_weights": {
            "skillMatchAllBonus": 100.0,
            "experienceLevelBonusPerYear": 5.0
        }
    }',
    false,
    'admin'
);
```

### Activating Configuration

```sql
-- Deactivate current configuration
UPDATE matching_configuration SET is_active = false WHERE is_active = true;

-- Activate new configuration
UPDATE matching_configuration 
SET is_active = true 
WHERE config_name = 'high_priority_config';
```

## Configuration Scenarios

### Scenario 1: Rural Area Configuration

For areas with limited staff and longer travel distances:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 100.0,
    "mustRespectGeoServiceArea": false,
    "minScoreThreshold": -10.0
  },
  "scoring_weights": {
    "proximityKmPenalty": -0.5,
    "skillMatchBonusPerRequiredSkill": 15.0,
    "continuityBonusPerRecentVisit": 10.0
  }
}
```

### Scenario 2: Urban High-Density Configuration

For urban areas with many available staff:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": true,
    "proximitySearchRadiusKm": 15.0,
    "mustRespectGeoServiceArea": true,
    "minScoreThreshold": 25.0
  },
  "scoring_weights": {
    "proximityKmPenalty": -2.0,
    "skillMatchAllBonus": 75.0,
    "preferredCareStaffBonus": 25.0
  }
}
```

### Scenario 3: Emergency/High-Priority Configuration

For urgent cases requiring immediate response:

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 75.0,
    "mustRespectAvailability": false,
    "minScoreThreshold": 0.0
  },
  "scoring_weights": {
    "proximityKmPenalty": -3.0,
    "experienceLevelBonusPerYear": 10.0,
    "skillMatchBonusPerRequiredSkill": 20.0
  }
}
```

## Application Properties

### Core Application Configuration

```yaml
# Database Configuration
spring:
  datasource:
    url: ****************************************************
    username: caxl_user
    password: caxl_password
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.spatial.dialect.postgis.PostgisPG10Dialect

# CareStaff Matching Service Configuration
carestaff:
  matching:
    default-search-radius-km: 50.0
    min-score-threshold: 0.0
    overlap-threshold-minutes: 1
    min-time-before-visit-minutes: 30
    min-time-after-visit-minutes: 30
    geofence-strict-containment: true
    staff-service-geofence-types:
      - service_area
      - county_area
```

### Environment-Specific Configuration

#### Development (`application-dev.yml`)
```yaml
logging:
  level:
    com.caxl.assignment: DEBUG
    org.hibernate.SQL: DEBUG

spring:
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update
```

#### Production (`application-prod.yml`)
```yaml
logging:
  level:
    root: INFO
    com.caxl.assignment: INFO

spring:
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

## Configuration Validation

### JSON Schema Validation

The system validates configuration JSON against a schema. Invalid configurations are rejected with detailed error messages.

### Runtime Validation

- **Range checks**: Numeric values within acceptable ranges
- **Type validation**: Correct data types for all fields
- **Dependency validation**: Related settings are consistent

### Testing Configuration Changes

```bash
# Test configuration with sample request
curl -X POST http://localhost:8080/api/v1/service-requests/{request_id}/suggest-matches

# Monitor logs for configuration usage
tail -f logs/application.log | grep "MatchingCriteria"
```

## Best Practices

### Configuration Management
1. **Version control**: Keep configuration changes in version control
2. **Testing**: Test configuration changes in development first
3. **Backup**: Backup configurations before major changes
4. **Documentation**: Document the purpose of each configuration

### Performance Optimization
1. **Reasonable thresholds**: Set appropriate minimum score thresholds
2. **Balanced weights**: Avoid extreme weight values
3. **Geographic limits**: Use reasonable search radius values
4. **Monitor impact**: Track performance after configuration changes

### Business Alignment
1. **Stakeholder input**: Involve business stakeholders in weight decisions
2. **Regular review**: Periodically review and adjust configurations
3. **Metrics tracking**: Monitor matching quality metrics
4. **Feedback loop**: Incorporate user feedback into configuration tuning

## Troubleshooting

### Common Configuration Issues

#### No Match Suggestions Generated
- Check `minScoreThreshold` - may be too high
- Verify `proximitySearchRadiusKm` - may be too small
- Review hard constraint filters - may be too restrictive

#### Poor Match Quality
- Adjust scoring weights to emphasize important factors
- Increase `minScoreThreshold` to filter low-quality matches
- Review skill matching requirements

#### Performance Issues
- Reduce `proximitySearchRadiusKm` if too large
- Increase `minScoreThreshold` to reduce processing
- Optimize database queries and indexes

For additional support, see the [Troubleshooting Guide](troubleshooting.md).
