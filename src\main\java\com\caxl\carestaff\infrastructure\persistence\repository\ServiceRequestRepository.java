package com.caxl.carestaff.infrastructure.persistence.repository;

import com.caxl.carestaff.infrastructure.persistence.entity.ServiceRequestEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Spring Data JPA repository for service request entities.
 */
@Repository
public interface ServiceRequestRepository extends JpaRepository<ServiceRequestEntity, UUID> {

    /**
     * Update service request status.
     */
    @Modifying
    @Query("UPDATE ServiceRequestEntity sr SET sr.status = :status WHERE sr.requestId = :requestId")
    void updateStatus(@Param("requestId") UUID requestId, @Param("status") String status);

    /**
     * Update multiple service request statuses.
     */
    @Modifying
    @Query("UPDATE ServiceRequestEntity sr SET sr.status = :status WHERE sr.requestId IN :requestIds")
    void updateStatusBatch(@Param("requestIds") List<UUID> requestIds, @Param("status") String status);

    /**
     * Find service requests by status.
     */
    List<ServiceRequestEntity> findByStatus(String status);

    /**
     * Find service requests by patient ID.
     */
    List<ServiceRequestEntity> findByPatientId(UUID patientId);
}
