package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Assignment;

import java.time.LocalDate;
import java.util.List;

/**
 * Use case for validating schedules against business rules and constraints.
 * Ensures all assignments meet hard constraints and optimization criteria.
 */
public interface ValidateScheduleUseCase {

    /**
     * Validate a complete schedule for a given date.
     * 
     * @param assignments List of assignments to validate
     * @param schedulingDate Date of the schedule
     * @return Validation result
     */
    ValidationResult validateSchedule(List<Assignment> assignments, LocalDate schedulingDate);

    /**
     * Validate a single assignment against all applicable rules.
     * 
     * @param assignment Assignment to validate
     * @return Single assignment validation result
     */
    AssignmentValidationResult validateAssignment(Assignment assignment);

    /**
     * Check if schedule meets minimum quality thresholds.
     * 
     * @param assignments List of assignments
     * @param qualityThresholds Quality criteria
     * @return Quality check result
     */
    QualityCheckResult checkScheduleQuality(
        List<Assignment> assignments, 
        QualityThresholds qualityThresholds
    );

    /**
     * Validate schedule feasibility before optimization.
     * 
     * @param patients List of patients requiring care
     * @param clinicians Available carestaff
     * @param schedulingDate Target date
     * @return Feasibility result
     */
    FeasibilityResult validateScheduleFeasibility(
        List<Object> patients,
        List<Object> clinicians,
        LocalDate schedulingDate
    );

    /**
     * Quality thresholds for schedule validation.
     */
    record QualityThresholds(
        double minAssignmentRate,
        double minAverageScore,
        int maxUnassignedHighPriority,
        double maxAverageDistance,
        int maxOvertimeHours
    ) {
        public static QualityThresholds defaultThresholds() {
            return new QualityThresholds(0.95, 0.7, 0, 25.0, 2);
        }
    }

    /**
     * Result of schedule validation.
     */
    record ValidationResult(
        boolean isValid,
        List<ValidationIssue> hardConstraintViolations,
        List<ValidationIssue> softConstraintViolations,
        List<ValidationIssue> warnings,
        ValidationMetrics metrics
    ) {}

    /**
     * Result of single assignment validation.
     */
    record AssignmentValidationResult(
        boolean isValid,
        List<String> violatedRules,
        List<String> warnings,
        double assignmentScore
    ) {}

    /**
     * Result of quality check.
     */
    record QualityCheckResult(
        boolean meetsQuality,
        double assignmentRate,
        double averageScore,
        int unassignedHighPriority,
        double averageDistance,
        int totalOvertimeHours,
        List<String> qualityIssues
    ) {}

    /**
     * Result of feasibility check.
     */
    record FeasibilityResult(
        boolean isFeasible,
        int totalPatients,
        int totalClinicians,
        int estimatedPossibleAssignments,
        List<String> feasibilityIssues
    ) {}

    /**
     * Validation issue details.
     */
    record ValidationIssue(
        String ruleId,
        String description,
        String severity,
        String affectedEntity,
        String recommendation
    ) {}

    /**
     * Validation performance metrics.
     */
    record ValidationMetrics(
        long validationTimeMs,
        int totalRulesChecked,
        int hardRulesPassed,
        int softRulesPassed,
        double overallScore
    ) {}
}
