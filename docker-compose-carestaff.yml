version: '3.8'

services:
  # PostgreSQL Database with PostGIS
  postgres-carestaff:
    image: postgis/postgis:16-3.4-alpine
    container_name: caxl-postgres-carestaff
    environment:
      POSTGRES_DB: homecare_scheduling
      POSTGRES_USER: caxl_user
      POSTGRES_PASSWORD: caxl_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5433:5432"
    volumes:
      - postgres_carestaff_data:/var/lib/postgresql/data
      - ./docker/init-scripts-carestaff:/docker-entrypoint-initdb.d
    networks:
      - caxl-carestaff-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U caxl_user -d homecare_scheduling"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching (optional)
  redis-carestaff:
    image: redis:7-alpine
    container_name: caxl-redis-carestaff
    ports:
      - "6380:6379"
    volumes:
      - redis_carestaff_data:/data
    networks:
      - caxl-carestaff-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # CareStaff Matching Service
  carestaff-matching-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: caxl-carestaff-matching-service
    environment:
      # Database Configuration
      SPRING_DATASOURCE_URL: *************************************************************
      SPRING_DATASOURCE_USERNAME: caxl_user
      SPRING_DATASOURCE_PASSWORD: caxl_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: false
      SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: org.hibernate.spatial.dialect.postgis.PostgisPG10Dialect
      
      # Redis Configuration (optional)
      SPRING_REDIS_HOST: redis-carestaff
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_TIMEOUT: 2000ms
      
      # CareStaff Matching Configuration
      CARESTAFF_MATCHING_DEFAULT_SEARCH_RADIUS_KM: 50.0
      CARESTAFF_MATCHING_MIN_SCORE_THRESHOLD: 0.0
      CARESTAFF_MATCHING_OVERLAP_THRESHOLD_MINUTES: 1
      CARESTAFF_MATCHING_GEOFENCE_STRICT_CONTAINMENT: true
      
      # Logging
      LOGGING_LEVEL_COM_CAXL_CARESTAFF: DEBUG
      LOGGING_LEVEL_ROOT: INFO
      
      # JVM Options
      JAVA_OPTS: "-Xmx1g -Xms512m"
    ports:
      - "8081:8080"
    depends_on:
      postgres-carestaff:
        condition: service_healthy
      redis-carestaff:
        condition: service_healthy
    networks:
      - caxl-carestaff-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_carestaff_data:
    driver: local
  redis_carestaff_data:
    driver: local

networks:
  caxl-carestaff-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
