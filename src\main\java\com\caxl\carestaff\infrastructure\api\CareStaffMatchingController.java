package com.caxl.carestaff.infrastructure.api;

import com.caxl.carestaff.application.port.in.*;
import com.caxl.carestaff.domain.entities.MatchSuggestion;
import com.caxl.carestaff.infrastructure.api.dto.*;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

/**
 * REST controller for carestaff matching operations.
 * API adapter in hexagonal architecture.
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Slf4j
public class CareStaffMatchingController {

    private final SuggestMatchesUseCase suggestMatchesUseCase;
    private final OverrideMatchUseCase overrideMatchUseCase;
    private final AssignBulkMatchesUseCase assignBulkMatchesUseCase;
    private final RetrieveSuggestionsUseCase retrieveSuggestionsUseCase;

    /**
     * Suggest matches for a service request.
     * POST /api/v1/service-requests/{request_id}/suggest-matches
     */
    @PostMapping("/service-requests/{request_id}/suggest-matches")
    public ResponseEntity<ApiResponse<List<MatchSuggestionDto>>> suggestMatches(
            @PathVariable("request_id") UUID requestId) {
        
        log.info("Received request to suggest matches for service request: {}", requestId);
        
        try {
            List<MatchSuggestion> suggestions = suggestMatchesUseCase.suggestMatches(requestId);
            List<MatchSuggestionDto> suggestionDtos = suggestions.stream()
                    .map(this::mapToDto)
                    .toList();
            
            String message = String.format("Generated %d match suggestions", suggestionDtos.size());
            return ResponseEntity.ok(ApiResponse.success(message, suggestionDtos));
            
        } catch (Exception e) {
            log.error("Error suggesting matches for request {}: {}", requestId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error generating match suggestions: " + e.getMessage()));
        }
    }

    /**
     * Override match suggestion with manual selection.
     * POST /api/v1/service-requests/{request_id}/override-match
     */
    @PostMapping("/service-requests/{request_id}/override-match")
    public ResponseEntity<ApiResponse<Boolean>> overrideMatch(
            @PathVariable("request_id") UUID requestId,
            @Valid @RequestBody OverrideRequestDto overrideRequest) {
        
        log.info("Received request to override match for service request: {}", requestId);
        
        try {
            OverrideMatchUseCase.OverrideRequestDto useCaseDto = 
                    new OverrideMatchUseCase.OverrideRequestDto(
                            overrideRequest.getSelectedCareStaffId(),
                            overrideRequest.getReason(),
                            overrideRequest.getExplanation()
                    );
            
            // TODO: Get schedulerId from authenticated user
            UUID schedulerId = UUID.randomUUID();
            
            boolean success = overrideMatchUseCase.overrideMatch(requestId, useCaseDto, schedulerId);
            
            if (success) {
                return ResponseEntity.ok(ApiResponse.success("Match successfully overridden", true));
            } else {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponse.error("Failed to override match"));
            }
            
        } catch (Exception e) {
            log.error("Error overriding match for request {}: {}", requestId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error overriding match: " + e.getMessage()));
        }
    }

    /**
     * Bulk assign match suggestions.
     * POST /api/v1/matches/bulk-assign
     */
    @PostMapping("/matches/bulk-assign")
    public ResponseEntity<ApiResponse<BulkAssignmentResultDto>> bulkAssign(
            @Valid @RequestBody BulkAssignRequestDto bulkRequest) {
        
        log.info("Received request to bulk assign {} suggestions", bulkRequest.getSuggestionIds().size());
        
        try {
            // TODO: Get schedulerId from authenticated user
            UUID schedulerId = bulkRequest.getSchedulerId() != null ? 
                    bulkRequest.getSchedulerId() : UUID.randomUUID();
            
            AssignBulkMatchesUseCase.BulkAssignmentResult result = 
                    assignBulkMatchesUseCase.assignBulk(bulkRequest.getSuggestionIds(), schedulerId);
            
            BulkAssignmentResultDto resultDto = BulkAssignmentResultDto.builder()
                    .totalRequested(result.totalRequested())
                    .successfulAssignments(result.successfulAssignments())
                    .failedAssignments(result.failedAssignments())
                    .errors(result.errors())
                    .success(result.failedAssignments() == 0)
                    .build();
            
            String message = String.format("Bulk assignment completed: %d successful, %d failed", 
                    result.successfulAssignments(), result.failedAssignments());
            
            return ResponseEntity.ok(ApiResponse.success(message, resultDto));
            
        } catch (Exception e) {
            log.error("Error in bulk assignment: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error in bulk assignment: " + e.getMessage()));
        }
    }

    /**
     * Retrieve existing suggestions for a service request.
     * GET /api/v1/service-requests/{request_id}/suggestions
     */
    @GetMapping("/service-requests/{request_id}/suggestions")
    public ResponseEntity<ApiResponse<List<MatchSuggestionDto>>> getSuggestions(
            @PathVariable("request_id") UUID requestId) {
        
        log.info("Received request to retrieve suggestions for service request: {}", requestId);
        
        try {
            List<MatchSuggestion> suggestions = retrieveSuggestionsUseCase.getSuggestions(requestId);
            List<MatchSuggestionDto> suggestionDtos = suggestions.stream()
                    .map(this::mapToDto)
                    .toList();
            
            String message = String.format("Found %d existing suggestions", suggestionDtos.size());
            return ResponseEntity.ok(ApiResponse.success(message, suggestionDtos));
            
        } catch (Exception e) {
            log.error("Error retrieving suggestions for request {}: {}", requestId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error retrieving suggestions: " + e.getMessage()));
        }
    }

    /**
     * Map domain entity to DTO.
     */
    private MatchSuggestionDto mapToDto(MatchSuggestion suggestion) {
        return MatchSuggestionDto.builder()
                .suggestionId(suggestion.getSuggestionId())
                .serviceRequestId(suggestion.getServiceRequestId())
                .suggestedCareStaffId(suggestion.getSuggestedCareStaffId())
                .score(suggestion.getScore())
                .rationale(suggestion.getRationale())
                .suggestedDateTime(suggestion.getSuggestedDateTime())
                .createdAt(suggestion.getCreatedAt())
                .isActive(suggestion.isActive())
                .build();
    }
}
