package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Clinician;
import org.locationtech.jts.geom.Point;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for carestaff/clinician data access.
 * Supports spatial queries and availability checking for scheduling optimization.
 */
public interface CareStaffRepository {

    /**
     * Find all active carestaff available for a specific date.
     * 
     * @param schedulingDate Date to check availability
     * @return List of available carestaff
     */
    List<Clinician> findAvailableCareStaff(LocalDate schedulingDate);

    /**
     * Find carestaff by required skills and proximity to patient location.
     * 
     * @param requiredSkills Required skills for the assignment
     * @param patientLocation Patient location coordinates
     * @param maxDistanceKm Maximum distance in kilometers
     * @param schedulingDate Date of service
     * @return List of matching carestaff
     */
    List<Clinician> findCareStaffBySkillsAndProximity(
        List<String> requiredSkills,
        Point patientLocation,
        double maxDistanceKm,
        LocalDate schedulingDate
    );

    /**
     * Check if carestaff is available during specific time window.
     * 
     * @param careStaffId Carestaff ID
     * @param startTime Start of time window
     * @param endTime End of time window
     * @return True if available
     */
    boolean isCareStaffAvailable(UUID careStaffId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Get carestaff workload for a specific date.
     * 
     * @param careStaffId Carestaff ID
     * @param schedulingDate Date to check
     * @return Current workload information
     */
    WorkloadInfo getCareStaffWorkload(UUID careStaffId, LocalDate schedulingDate);

    /**
     * Find carestaff by ID.
     * 
     * @param careStaffId Carestaff ID
     * @return Carestaff if found
     */
    Optional<Clinician> findById(UUID careStaffId);

    /**
     * Get all carestaff with their current status.
     * 
     * @return List of all carestaff
     */
    List<Clinician> findAll();

    /**
     * Update carestaff availability status.
     * 
     * @param careStaffId Carestaff ID
     * @param startTime Start of unavailability
     * @param endTime End of unavailability
     * @param reason Reason for unavailability
     */
    void updateAvailability(UUID careStaffId, LocalDateTime startTime, LocalDateTime endTime, String reason);

    /**
     * Get carestaff performance metrics.
     * 
     * @param careStaffId Carestaff ID
     * @param startDate Start of analysis period
     * @param endDate End of analysis period
     * @return Performance metrics
     */
    PerformanceMetrics getCareStaffPerformance(UUID careStaffId, LocalDate startDate, LocalDate endDate);

    /**
     * Workload information for carestaff.
     */
    record WorkloadInfo(
        UUID careStaffId,
        LocalDate date,
        int currentAssignments,
        int maxDailyAssignments,
        double currentWorkloadPoints,
        double maxWorkloadPoints,
        boolean isOverloaded
    ) {}

    /**
     * Performance metrics for carestaff.
     */
    record PerformanceMetrics(
        UUID careStaffId,
        int totalAssignments,
        double averageScore,
        double completionRate,
        double patientSatisfaction,
        int cancellations,
        double averageDistance
    ) {}
}
