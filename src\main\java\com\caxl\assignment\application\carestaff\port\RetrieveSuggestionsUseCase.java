package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.entities.MatchSuggestion;

import java.util.List;
import java.util.UUID;

/**
 * Use case interface for retrieving existing match suggestions.
 * Driven port in hexagonal architecture.
 */
public interface RetrieveSuggestionsUseCase {

    /**
     * Retrieve existing match suggestions for a service request.
     * 
     * @param requestId The service request ID
     * @return List of existing match suggestions
     */
    List<MatchSuggestion> getSuggestions(UUID requestId);
}
