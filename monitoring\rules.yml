# Alerting rules for homecare scheduling system
groups:
  - name: homecare-scheduling-alerts
    rules:
      # High scheduling optimization time
      - alert: HighOptimizationTime
        expr: homecare_optimization_duration_seconds > 300
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Homecare scheduling optimization taking too long"
          description: "Optimization for {{ $labels.organization_id }} is taking {{ $value }}s"

      # Failed scheduling attempts
      - alert: SchedulingFailures
        expr: rate(homecare_scheduling_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High rate of scheduling failures"
          description: "{{ $value }} scheduling failures per second in the last 5 minutes"

      # State compliance violations
      - alert: ComplianceViolations
        expr: homecare_compliance_violations_total > 0
        for: 0m
        labels:
          severity: critical
        annotations:
          summary: "State compliance violations detected"
          description: "{{ $value }} compliance violations for {{ $labels.state_code }}"

      # Geofencing violations
      - alert: GeofencingViolations
        expr: homecare_geofencing_violations_total > 5
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Geofencing violations detected"
          description: "{{ $value }} geofencing violations in zone {{ $labels.zone_id }}"

      # Care staff overutilization
      - alert: StaffOverutilization
        expr: homecare_staff_utilization_ratio > 0.95
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Care staff overutilization"
          description: "Staff {{ $labels.staff_id }} utilization is {{ $value }}"

      # Visit assignment failures
      - alert: UnassignedVisits
        expr: homecare_unassigned_visits_total > 10
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High number of unassigned visits"
          description: "{{ $value }} visits remain unassigned"

  - name: infrastructure-alerts
    rules:
      # Database connection issues
      - alert: DatabaseDown
        expr: up{job="postgresql"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL database is down"
          description: "PostgreSQL database has been down for more than 1 minute"

      # Redis cache issues
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Redis cache is down"
          description: "Redis cache has been down for more than 1 minute"

      # High memory usage
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90%: {{ $value }}"

      # High CPU usage
      - alert: HighCPUUsage
        expr: 100 - (avg(irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80%: {{ $value }}%"
