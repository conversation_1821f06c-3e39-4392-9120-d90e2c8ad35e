package com.caxl.carestaff.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Domain entity representing a patient.
 * Framework-agnostic domain model for patient data.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Patient {
    
    private UUID patientId;
    private String encFirstName;
    private String encLastName;
    private String encEmail;
    private String encPhone;
    private LocalDate dateOfBirth;
    private String gender;
    private UUID locationId;
    private Location location;
    private String preferredLanguage;
    private String emergencyContact;
    private String encEmergencyPhone;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Patient preferences and profile data
    private List<UUID> preferredCareStaffIds;
    private List<UUID> barredCareStaffIds;
    private String medicalConditions;
    private String specialInstructions;
    private String insuranceProvider;
    private String encInsuranceNumber;
    
    /**
     * Check if a care staff member is preferred by this patient.
     */
    public boolean isPreferredCareStaff(UUID careStaffId) {
        return preferredCareStaffIds != null && preferredCareStaffIds.contains(careStaffId);
    }
    
    /**
     * Check if a care staff member is barred by this patient.
     */
    public boolean isBarredCareStaff(UUID careStaffId) {
        return barredCareStaffIds != null && barredCareStaffIds.contains(careStaffId);
    }
    
    /**
     * Get patient's location coordinates if available.
     */
    public org.locationtech.jts.geom.Point getLocationCoordinates() {
        return location != null ? location.getCoordinates() : null;
    }
}
