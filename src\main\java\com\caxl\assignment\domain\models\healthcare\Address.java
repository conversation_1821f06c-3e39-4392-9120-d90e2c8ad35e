package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Address information.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Address {

    @NotBlank(message = "Street is required")
    @JsonProperty("street")
    private String street;

    @NotBlank(message = "City is required")
    @JsonProperty("city")
    private String city;

    @NotBlank(message = "State is required")
    @JsonProperty("state")
    private String state;

    @NotBlank(message = "ZIP code is required")
    @JsonProperty("zip_code")
    private String zipCode;    @Builder.Default
    @JsonProperty("country")
    private String country = "US";

    @NotNull(message = "Latitude is required")
    @JsonProperty("latitude")
    private Double latitude;

    @NotNull(message = "Longitude is required")
    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("apartment_unit")
    private String apartmentUnit;

    @JsonProperty("special_instructions")
    private String specialInstructions;
}
