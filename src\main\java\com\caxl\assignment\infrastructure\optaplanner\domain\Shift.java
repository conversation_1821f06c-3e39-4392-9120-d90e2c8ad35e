package com.caxl.assignment.infrastructure.optaplanner.domain;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.variable.PlanningVariable;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Shift represents a work period that can be assigned to a clinician.
 * This is a planning entity that Timefold will optimize.
 */
@PlanningEntity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Shift {

    private String id;
    private LocalDate shiftDate;
    private LocalTime shiftStartTime;
    private LocalTime shiftEndTime;
    private ShiftType shiftType;
    private String serviceArea;
    private int maxPatients;
    private int maxWorkloadPoints;
    
    /**
     * The clinician assigned to this shift (planning variable).
     */
    @PlanningVariable(valueRangeProviderRefs = "clinicianRange")
    private Clinician assignedClinician;
    
    /**
     * List of patients assigned to this shift.
     */
    private List<Patient> patientVisits;

    /**
     * Shift type enumeration.
     */
    public enum ShiftType {
        MORNING, AFTERNOON, EVENING, NIGHT, FULL_DAY
    }

    // === HELPER METHODS ===

    /**
     * Get the number of patients assigned to this shift.
     */
    public int getPatientCount() {
        return patientVisits != null ? patientVisits.size() : 0;
    }

    /**
     * Get the total workload points for this shift.
     */
    public int getTotalWorkloadPoints() {
        if (patientVisits == null) return 0;
        return patientVisits.stream()
                .mapToInt(Patient::getWorkloadPoints)
                .sum();
    }

    /**
     * Check if this shift has any patients assigned.
     */
    public boolean hasPatients() {
        return patientVisits != null && !patientVisits.isEmpty();
    }

    /**
     * Get the shift date.
     */
    public LocalDate getShiftDate() {
        return shiftDate;
    }

    /**
     * Get the list of patient visits for this shift.
     */
    public List<Patient> getPatientVisits() {
        return patientVisits != null ? patientVisits : List.of();
    }

    /**
     * Get the assigned clinician for this shift.
     */
    public Clinician getAssignedClinician() {
        return assignedClinician;
    }

    /**
     * Get the maximum number of patients for this shift.
     */
    public int getMaxPatients() {
        return maxPatients;
    }

    /**
     * Get the shift start time.
     */
    public LocalTime getShiftStartTime() {
        return shiftStartTime;
    }

    /**
     * Get the shift end time.
     */
    public LocalTime getShiftEndTime() {
        return shiftEndTime;
    }

    /**
     * Get the shift type.
     */
    public ShiftType getShiftType() {
        return shiftType;
    }

    /**
     * Get the service area for this shift.
     */
    public String getServiceArea() {
        return serviceArea;
    }
}
