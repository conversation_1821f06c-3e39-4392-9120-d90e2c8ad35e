{"name": "Complex Constraints with Real-Time Distance Calculation", "description": "Advanced scenario testing complex constraint combinations, real-time Google Maps distance calculations, multi-stage filtering, and edge cases. Tests hard/soft constraint prioritization, conflict resolution, and optimal routing.", "service_requests": [{"request_id": "req-urgent-001", "patient_id": "patient-urgent-001", "status": "pending", "required_skill_ids": ["skill-wound-care", "skill-iv-therapy", "skill-pain-management"], "required_certification_ids": ["cert-rn", "cert-iv-certified"], "time_window": {"arrival_window_start": "2025-06-01T08:00:00", "arrival_window_end": "2025-06-01T10:00:00", "visit_duration_minutes": 120, "preferred_start_time": "2025-06-01T08:30:00", "latest_end_time": "2025-06-01T10:30:00"}, "visit_type": "urgent_care", "priority": 1, "workload_points": 5, "special_instructions": "High-priority patient with complex wound care needs"}, {"request_id": "req-routine-002", "patient_id": "patient-routine-002", "status": "pending", "required_skill_ids": ["skill-medication-mgmt", "skill-vital-signs"], "required_certification_ids": ["cert-cna"], "time_window": {"arrival_window_start": "2025-06-01T14:00:00", "arrival_window_end": "2025-06-01T16:00:00", "visit_duration_minutes": 60, "preferred_start_time": "2025-06-01T15:00:00", "latest_end_time": "2025-06-01T16:00:00"}, "visit_type": "routine_care", "priority": 3, "workload_points": 2, "special_instructions": "Regular medication administration and vitals check"}, {"request_id": "req-complex-003", "patient_id": "patient-complex-003", "status": "pending", "required_skill_ids": ["skill-physical-therapy", "skill-respiratory-therapy", "skill-wound-care"], "required_certification_ids": ["cert-pt", "cert-rt"], "time_window": {"arrival_window_start": "2025-06-01T10:30:00", "arrival_window_end": "2025-06-01T13:00:00", "visit_duration_minutes": 90, "preferred_start_time": "2025-06-01T11:00:00", "latest_end_time": "2025-06-01T13:00:00"}, "visit_type": "specialized_therapy", "priority": 2, "workload_points": 4, "special_instructions": "Requires specialized respiratory and physical therapy"}], "patients": [{"patient_id": "patient-urgent-001", "enc_first_name": "<PERSON>", "enc_last_name": "<PERSON>", "date_of_birth": "1945-08-22", "gender": "female", "location": {"location_id": "loc-urgent-001", "address": "1234 Sunset Blvd, West Hollywood, CA 90069", "postal_code": "90069", "coordinates": {"latitude": 34.09, "longitude": -118.3617}}, "preferred_language": "spanish", "preferred_care_staff_ids": ["carestaff-002"], "barred_care_staff_ids": ["carestaff-004"], "medical_conditions": "diabetes, chronic wounds, hypertension", "special_instructions": "Prefers Spanish-speaking caregivers, has mobility issues", "is_active": true}, {"patient_id": "patient-routine-002", "enc_first_name": "<PERSON>", "enc_last_name": "<PERSON>", "date_of_birth": "1960-12-10", "gender": "male", "location": {"location_id": "loc-routine-002", "address": "5678 Melrose Ave, Los Angeles, CA 90038", "postal_code": "90038", "coordinates": {"latitude": 34.0836, "longitude": -118.3365}}, "preferred_language": "english", "preferred_care_staff_ids": [], "barred_care_staff_ids": [], "medical_conditions": "hypertension, arthritis", "special_instructions": "Prefers male caregivers, very punctual", "is_active": true}, {"patient_id": "patient-complex-003", "enc_first_name": "<PERSON>", "enc_last_name": "<PERSON>", "date_of_birth": "1938-04-15", "gender": "female", "location": {"location_id": "loc-complex-003", "address": "9012 Beverly Blvd, Beverly Hills, CA 90210", "postal_code": "90210", "coordinates": {"latitude": 34.0736, "longitude": -118.3754}}, "preferred_language": "english", "preferred_care_staff_ids": ["carestaff-001", "carestaff-003"], "barred_care_staff_ids": [], "medical_conditions": "COPD, post-surgical recovery, limited mobility", "special_instructions": "Requires specialized equipment, prefers experienced therapists", "is_active": true}], "carestaff": [{"care_staff_id": "carestaff-001", "name": "Dr. <PERSON>", "base_location": {"latitude": 34.0522, "longitude": -118.2437}, "skill_ids": ["skill-wound-care", "skill-iv-therapy", "skill-pain-management", "skill-medication-mgmt"], "certification_ids": ["cert-rn", "cert-iv-certified", "cert-wound-specialist"], "languages": ["english"], "experience_years": 12, "is_active": true, "operating_zones": ["90210", "90069", "90038"], "availability_windows": [{"start_time": "2025-06-01T07:00:00", "end_time": "2025-06-01T15:00:00"}], "current_appointments": [{"start_time": "2025-06-01T07:00:00", "end_time": "2025-06-01T08:00:00", "patient_id": "existing-patient-001"}], "max_daily_appointments": 6}, {"care_staff_id": "carestaff-002", "name": "<PERSON>", "base_location": {"latitude": 34.09, "longitude": -118.3617}, "skill_ids": ["skill-wound-care", "skill-iv-therapy", "skill-medication-mgmt", "skill-vital-signs"], "certification_ids": ["cert-rn", "cert-iv-certified"], "languages": ["english", "spanish"], "experience_years": 8, "is_active": true, "operating_zones": ["90069", "90038", "90210"], "availability_windows": [{"start_time": "2025-06-01T08:00:00", "end_time": "2025-06-01T16:00:00"}], "current_appointments": [], "max_daily_appointments": 5}, {"care_staff_id": "carestaff-003", "name": "<PERSON>", "base_location": {"latitude": 34.0736, "longitude": -118.3754}, "skill_ids": ["skill-physical-therapy", "skill-respiratory-therapy", "skill-wound-care"], "certification_ids": ["cert-pt", "cert-rt", "cert-rn"], "languages": ["english"], "experience_years": 15, "is_active": true, "operating_zones": ["90210", "90069"], "availability_windows": [{"start_time": "2025-06-01T09:00:00", "end_time": "2025-06-01T17:00:00"}], "current_appointments": [{"start_time": "2025-06-01T09:00:00", "end_time": "2025-06-01T10:00:00", "patient_id": "existing-patient-002"}], "max_daily_appointments": 4}, {"care_staff_id": "carestaff-004", "name": "<PERSON>", "base_location": {"latitude": 34.0836, "longitude": -118.3365}, "skill_ids": ["skill-medication-mgmt", "skill-vital-signs", "skill-wound-care"], "certification_ids": ["cert-cna", "cert-medication-admin"], "languages": ["english"], "experience_years": 6, "is_active": true, "operating_zones": ["90038", "90069"], "availability_windows": [{"start_time": "2025-06-01T12:00:00", "end_time": "2025-06-01T20:00:00"}], "current_appointments": [{"start_time": "2025-06-01T12:00:00", "end_time": "2025-06-01T13:00:00", "patient_id": "existing-patient-003"}, {"start_time": "2025-06-01T17:00:00", "end_time": "2025-06-01T18:30:00", "patient_id": "existing-patient-004"}], "max_daily_appointments": 6}, {"care_staff_id": "carestaff-005", "name": "<PERSON>", "base_location": {"latitude": 34.0522, "longitude": -118.2437}, "skill_ids": ["skill-physical-therapy", "skill-respiratory-therapy"], "certification_ids": ["cert-pt", "cert-rt"], "languages": ["english", "korean"], "experience_years": 10, "is_active": true, "operating_zones": ["90210", "90038"], "availability_windows": [{"start_time": "2025-06-01T10:00:00", "end_time": "2025-06-01T18:00:00"}], "current_appointments": [{"start_time": "2025-06-01T16:00:00", "end_time": "2025-06-01T17:00:00", "patient_id": "existing-patient-005"}], "max_daily_appointments": 5}], "matching_criteria": {"filters": {"required_skills_must_match_all": true, "proximity_search_radius_km": 25.0, "must_respect_geo_service_area": true, "must_respect_availability": true, "must_not_be_barred": true, "min_score_threshold": 50.0}, "scoring_weights": {"skill_match_bonus_per_required_skill": 15.0, "skill_match_all_bonus": 75.0, "proximity_km_penalty": -2.0, "geo_service_area_bonus": 25.0, "availability_window_fit_penalty_per_minute_deviation": -1.0, "preferred_care_staff_bonus": 30.0, "continuity_bonus_per_recent_visit": 10.0, "language_match_bonus": 20.0, "experience_level_bonus_per_year": 3.0}, "geography": {"staff_service_geofence_types": ["service_area", "county_area"], "geofence_strict_containment_only": true}, "availability": {"overlap_threshold_minutes": 15, "min_time_before_visit_minutes": 45, "min_time_after_visit_minutes": 30}}, "google_maps_config": {"api_key": "AIzaSyC7z-cWYB_Ylcq4H_t7ok5-gmPg6dNbAE4", "enable_real_time_distance": true, "enable_traffic_optimization": true, "max_travel_time_minutes": 45}, "expected_matches": {"req-urgent-001": ["carestaff-002", "carestaff-001"], "req-routine-002": ["carestaff-004"], "req-complex-003": ["carestaff-003", "carestaff-005"]}, "expected_conflicts": [{"type": "skill_mismatch", "description": "carestaff-004 lacks required certifications for urgent care", "affected_requests": ["req-urgent-001"], "affected_carestaff": ["carestaff-004"]}, {"type": "availability_conflict", "description": "carestaff-003 has existing appointment during preferred time", "affected_requests": ["req-complex-003"], "affected_carestaff": ["carestaff-003"]}], "test_scenarios": [{"name": "hard_constraint_validation", "description": "Verify all hard constraints are properly enforced"}, {"name": "soft_constraint_optimization", "description": "Verify soft constraints are properly weighted and optimized"}, {"name": "real_time_distance_calculation", "description": "Verify Google Maps API integration for accurate travel times"}, {"name": "conflict_detection", "description": "Verify system detects and reports scheduling conflicts"}, {"name": "priority_based_assignment", "description": "Verify high-priority requests get preference in resource allocation"}]}