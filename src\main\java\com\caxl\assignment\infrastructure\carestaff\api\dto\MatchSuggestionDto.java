package com.caxl.assignment.infrastructure.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * DTO for match suggestion API responses.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchSuggestionDto {

    @JsonProperty("suggestion_id")
    private UUID suggestionId;

    @JsonProperty("service_request_id")
    private UUID serviceRequestId;

    @JsonProperty("suggested_carestaff_id")
    private UUID suggestedCareStaffId;

    @JsonProperty("score")
    private Double score;

    @JsonProperty("rationale")
    private String rationale;

    @JsonProperty("suggested_datetime")
    private LocalDateTime suggestedDateTime;

    @JsonProperty("created_at")
    private LocalDateTime createdAt;

    @JsonProperty("is_active")
    private Boolean isActive;
}
