package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;

/**
 * Comprehensive test suite for availability constraints in homecare scheduling.
 * Tests care staff availability validation during visit scheduling.
 */
@DisplayName("Availability Constraint Tests")
class AvailabilityConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Availability Validation")
    class BasicAvailabilityValidation {        @Test
        @DisplayName("testAvailabilityConstraint_whenStaffAvailableDuringVisit_shouldPass")
        void testAvailabilityConstraint_whenStaffAvailableDuringVisit_shouldPass() {
            // Given: Care staff available 9 AM - 5 PM
            LocalDateTime startTime = LocalDateTime.of(2024, 1, 15, 9, 0);
            LocalDateTime endTime = LocalDateTime.of(2024, 1, 15, 17, 0);
            
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(startTime, endTime)
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Visit scheduled within availability window (10 AM - 11 AM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass without penalties
            constraintVerifier.verifyThat((provider, factory) -> provider.careStaffAvailability(factory))
                    .given(availableStaff, visit)
                    .penalizesBy(0);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenStaffUnavailableDuringVisit_shouldFail")
        void testAvailabilityConstraint_whenStaffUnavailableDuringVisit_shouldFail() {
            // Given: Care staff available 9 AM - 5 PM
            LocalDateTime startTime = LocalDateTime.of(2024, 1, 15, 9, 0);
            LocalDateTime endTime = LocalDateTime.of(2024, 1, 15, 17, 0);
            
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(startTime, endTime)
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Visit scheduled outside availability window (7 AM - 8 AM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 7, 0), Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for unavailability
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(availableStaff, visit)
                    .penalizesBy(1);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitUnassigned_shouldPass")
        void testAvailabilityConstraint_whenVisitUnassigned_shouldPass() {
            // Given: Care staff with availability
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Unassigned visit
            HomecareVisit unassignedVisit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (constraint only applies to assigned visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(availableStaff, unassignedVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Complex Availability Scenarios")
    class ComplexAvailabilityScenarios {        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitSpansMultipleAvailabilityWindows_shouldPass")
        void testAvailabilityConstraint_whenVisitSpansMultipleAvailabilityWindows_shouldPass() {
            // Given: Care staff with split availability (9-12, 13-17)
            List<AvailabilityWindow> splitAvailability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 12, 0)
                ),
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 13, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff splitAvailableStaff = testDataFactory.createCareStaffWithAvailability("staff1", splitAvailability);
            
            // And: Visit scheduled within one availability window (10 AM - 11 AM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (visit is within one of the windows)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(splitAvailableStaff, visit)
                    .penalizesBy(0);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitSpansAvailabilityGap_shouldFail")
        void testAvailabilityConstraint_whenVisitSpansAvailabilityGap_shouldFail() {
            // Given: Care staff with split availability (9-12, 13-17)
            List<AvailabilityWindow> splitAvailability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 12, 0)
                ),
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 13, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff splitAvailableStaff = testDataFactory.createCareStaffWithAvailability("staff1", splitAvailability);
            
            // And: Visit scheduled during the gap (12 PM - 1:30 PM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 12, 0), Duration.ofMinutes(90));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (visit spans the unavailable gap)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(splitAvailableStaff, visit)
                    .penalizesBy(1);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitExactlyMatchesAvailabilityWindow_shouldPass")
        void testAvailabilityConstraint_whenVisitExactlyMatchesAvailabilityWindow_shouldPass() {
            // Given: Care staff available for exact visit duration
            LocalDateTime startTime = LocalDateTime.of(2024, 1, 15, 10, 0);
            LocalDateTime endTime = LocalDateTime.of(2024, 1, 15, 11, 0);
            
            List<AvailabilityWindow> exactAvailability = List.of(
                testDataFactory.createAvailabilityWindow(startTime, endTime)
            );
            CareStaff exactAvailableStaff = testDataFactory.createCareStaffWithAvailability("staff1", exactAvailability);
            
            // And: Visit scheduled for exact same time window
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", startTime, Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (exact match is valid)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(exactAvailableStaff, visit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Boundary Conditions")
    class EdgeCasesAndBoundaryConditions {        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitStartsAtAvailabilityEnd_shouldFail")
        void testAvailabilityConstraint_whenVisitStartsAtAvailabilityEnd_shouldFail() {
            // Given: Care staff available until 5 PM
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Visit starting exactly at availability end (5 PM - 6 PM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 17, 0), Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (visit extends beyond availability)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(availableStaff, visit)
                    .penalizesBy(1);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenVisitEndsAtAvailabilityStart_shouldFail")
        void testAvailabilityConstraint_whenVisitEndsAtAvailabilityStart_shouldFail() {
            // Given: Care staff available from 9 AM
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Visit ending exactly at availability start (8 AM - 9 AM)
            HomecareVisit visit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 8, 0), Duration.ofHours(1));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (visit is before availability)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(availableStaff, visit)
                    .penalizesBy(1);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenZeroDurationVisitWithinAvailability_shouldPass")
        void testAvailabilityConstraint_whenZeroDurationVisitWithinAvailability_shouldPass() {
            // Given: Care staff available during day
            List<AvailabilityWindow> availability = List.of(
                testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                )
            );
            CareStaff availableStaff = testDataFactory.createCareStaffWithAvailability("staff1", availability);
            
            // And: Zero-duration visit (consultation call)
            HomecareVisit instantVisit = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ZERO);
            instantVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (instant visit within availability)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(availableStaff, instantVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple Staff Availability")
    class MultipleStaffAvailability {        @Test
        @DisplayName("testAvailabilityConstraint_whenMultipleStaffWithDifferentAvailability_shouldEvaluateIndependently")
        void testAvailabilityConstraint_whenMultipleStaffWithDifferentAvailability_shouldEvaluateIndependently() {
            // Given: Morning staff (9 AM - 1 PM) and afternoon staff (1 PM - 5 PM)
            CareStaff morningStaff = testDataFactory.createCareStaffWithAvailability("morningStaff", 
                List.of(testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 9, 0),
                    LocalDateTime.of(2024, 1, 15, 13, 0)
                ))
            );
            
            CareStaff afternoonStaff = testDataFactory.createCareStaffWithAvailability("afternoonStaff", 
                List.of(testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 13, 0),
                    LocalDateTime.of(2024, 1, 15, 17, 0)
                ))
            );
            
            // And: Morning visit assigned to morning staff
            HomecareVisit morningVisit = testDataFactory.createVisitWithTimeWindow("morningVisit", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            morningVisit.setAssignedCareStaffId("morningStaff");
            
            // And: Afternoon visit assigned to afternoon staff
            HomecareVisit afternoonVisit = testDataFactory.createVisitWithTimeWindow("afternoonVisit", 
                    LocalDateTime.of(2024, 1, 15, 14, 0), Duration.ofHours(1));
            afternoonVisit.setAssignedCareStaffId("afternoonStaff");
            
            // When: Verifying the constraint
            // Then: Both should pass (each visit is within respective staff availability)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(morningStaff, afternoonStaff, morningVisit, afternoonVisit)
                    .penalizesBy(0);
        }        @Test
        @DisplayName("testAvailabilityConstraint_whenStaffAssignedToMultipleVisitsWithinAvailability_shouldPass")
        void testAvailabilityConstraint_whenStaffAssignedToMultipleVisitsWithinAvailability_shouldPass() {
            // Given: Care staff available all day
            CareStaff allDayStaff = testDataFactory.createCareStaffWithAvailability("allDayStaff", 
                List.of(testDataFactory.createAvailabilityWindow(
                    LocalDateTime.of(2024, 1, 15, 8, 0),
                    LocalDateTime.of(2024, 1, 15, 18, 0)
                ))
            );
            
            // And: Multiple visits assigned to same staff, all within availability
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(1));
            visit1.setAssignedCareStaffId("allDayStaff");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("allDayStaff");
            
            HomecareVisit visit3 = testDataFactory.createVisitWithTimeWindow("visit3", 
                    LocalDateTime.of(2024, 1, 15, 15, 0), Duration.ofHours(1));
            visit3.setAssignedCareStaffId("allDayStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (all visits within staff availability)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffAvailability)
                    .given(allDayStaff, visit1, visit2, visit3)
                    .penalizesBy(0);
        }
    }
}
