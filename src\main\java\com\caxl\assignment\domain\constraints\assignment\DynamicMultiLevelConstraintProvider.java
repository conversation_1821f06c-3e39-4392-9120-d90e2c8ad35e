package com.caxl.assignment.domain.constraints.assignment;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintCollectors;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import com.caxl.assignment.domain.models.assignment.DynamicClinicianAssignment;
import com.caxl.assignment.domain.models.assignment.DynamicRule;
import com.caxl.assignment.domain.models.assignment.MultiLevelAssignmentSolution;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Dynamic constraint provider for multi-level clinician assignment system.
 * Evaluates constraints based on dynamic rules loaded from configuration.
 * Supports three levels of filtering with relaxation strategies.
 */
@Component
@Slf4j
public class DynamicMultiLevelConstraintProvider implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory factory) {
        return new Constraint[]{
            // === LEVEL 1 CONSTRAINTS (Mandatory Assignment Criteria) ===
            level1HardConstraints(factory),
            
            // === LEVEL 2 CONSTRAINTS (Resource Capacity and Load Management) ===
            level2CapacityConstraints(factory),
            level2WorkloadConstraints(factory),
            level2TravelTimeConstraints(factory),
            
            // === LEVEL 3 CONSTRAINTS (Alternative Assignment Pathways) ===
            level3RelaxationConstraints(factory),
            
            // === SOFT CONSTRAINTS (Optimization Objectives) ===
            dynamicSoftConstraints(factory),
            continuityOfCareConstraint(factory),
            preferredClinicianConstraint(factory),
            geographicOptimizationConstraint(factory),
            workloadBalanceConstraint(factory),
            
            // === PENALTY CONSTRAINTS (Relaxation Penalties) ===
            skillSubstitutionPenalty(factory),
            extendedRadiusPenalty(factory),
            overtimePenalty(factory),
            reschedulingPenalty(factory)
        };
    }

    // =========================================================================
    // LEVEL 1 CONSTRAINTS - Mandatory Assignment Criteria (Hard Constraints)
    // =========================================================================

    /**
     * Level 1 hard constraints that must be satisfied for any assignment.
     * These include service date/time, geographic area, required skills, and compliance.
     */
    private Constraint level1HardConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete() && !satisfiesLevel1Rules(assignment))
                .penalize(HardSoftScore.ONE_HARD, assignment -> 1000)
                .asConstraint("Level 1 mandatory criteria");
    }

    // =========================================================================
    // LEVEL 2 CONSTRAINTS - Resource Capacity and Load Management
    // =========================================================================

    /**
     * Level 2 capacity constraints for clinician availability and capacity.
     */
    private Constraint level2CapacityConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete() && !satisfiesLevel2CapacityRules(assignment))
                .penalize(HardSoftScore.ONE_HARD, assignment -> 500)
                .asConstraint("Level 2 capacity constraints");
    }

    /**
     * Level 2 workload constraints for daily/weekly limits.
     */
    private Constraint level2WorkloadConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete())
                .groupBy(assignment -> assignment.getEffectiveAssignedClinician(),
                        assignment -> assignment.getAssignmentRequest().getServiceDateTime().toLocalDate(),
                        ConstraintCollectors.sum(assignment -> assignment.getAssignmentRequest().getWorkloadPoints()))
                .filter((clinician, date, totalWorkload) -> 
                    totalWorkload > clinician.getMaxDailyAppointments() * 2) // Workload threshold
                .penalize(HardSoftScore.ONE_HARD,
                        (clinician, date, totalWorkload) -> 
                            totalWorkload - (clinician.getMaxDailyAppointments() * 2))
                .asConstraint("Level 2 workload limits");
    }

    /**
     * Level 2 travel time and buffer constraints.
     */
    private Constraint level2TravelTimeConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete() && exceedsTravelTimeLimit(assignment))
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 200)
                .asConstraint("Level 2 travel time constraints");
    }

    // =========================================================================
    // LEVEL 3 CONSTRAINTS - Alternative Assignment Pathways (Relaxation)
    // =========================================================================

    /**
     * Level 3 relaxation constraints that allow alternative assignment strategies.
     */
    private Constraint level3RelaxationConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.getProcessingLevel() == 3 && 
                                    assignment.isComplete() && 
                                    !satisfiesLevel3RelaxationRules(assignment))
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 100)
                .asConstraint("Level 3 relaxation constraints");
    }

    // =========================================================================
    // SOFT CONSTRAINTS - Optimization Objectives
    // =========================================================================

    /**
     * Dynamic soft constraints based on configured rules.
     */
    private Constraint dynamicSoftConstraints(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete())
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> calculateDynamicSoftScore(assignment))
                .asConstraint("Dynamic soft constraints");
    }

    /**
     * Continuity of care constraint - prefer same clinician for repeat patients.
     */
    private Constraint continuityOfCareConstraint(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete() && hasHistoricalAssignment(assignment))
                .reward(HardSoftScore.ONE_SOFT, assignment -> 300)
                .asConstraint("Continuity of care");
    }

    /**
     * Preferred clinician constraint - match patient preferences.
     */
    private Constraint preferredClinicianConstraint(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete() && isPreferredClinician(assignment))
                .reward(HardSoftScore.ONE_SOFT, assignment -> 200)
                .asConstraint("Preferred clinician");
    }

    /**
     * Geographic optimization - minimize travel distances.
     */
    private Constraint geographicOptimizationConstraint(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete())
                .penalize(HardSoftScore.ONE_SOFT,
                        assignment -> calculateDistancePenalty(assignment))
                .asConstraint("Geographic optimization");
    }

    /**
     * Workload balance constraint - distribute assignments evenly.
     */
    private Constraint workloadBalanceConstraint(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.isComplete())
                .groupBy(assignment -> assignment.getEffectiveAssignedClinician(),
                        ConstraintCollectors.count())
                .penalize(HardSoftScore.ONE_SOFT,
                        (clinician, count) -> calculateWorkloadImbalance(count))
                .asConstraint("Workload balance");
    }

    // =========================================================================
    // PENALTY CONSTRAINTS - Relaxation Strategy Penalties
    // =========================================================================

    /**
     * Penalty for using skill substitution in Level 3.
     */
    private Constraint skillSubstitutionPenalty(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.usedSkillSubstitution())
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 150)
                .asConstraint("Skill substitution penalty");
    }

    /**
     * Penalty for using extended service radius in Level 3.
     */
    private Constraint extendedRadiusPenalty(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.usedExtendedRadius())
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 100)
                .asConstraint("Extended radius penalty");
    }

    /**
     * Penalty for overtime assignments in Level 3.
     */
    private Constraint overtimePenalty(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.usedOvertime())
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 250)
                .asConstraint("Overtime penalty");
    }

    /**
     * Penalty for visit rescheduling in Level 3.
     */
    private Constraint reschedulingPenalty(ConstraintFactory factory) {
        return factory.forEach(DynamicClinicianAssignment.class)
                .filter(assignment -> assignment.usedRescheduling())
                .penalize(HardSoftScore.ONE_SOFT, assignment -> 80)
                .asConstraint("Rescheduling penalty");
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    /**
     * Check if assignment satisfies Level 1 rules (mandatory criteria).
     */
    private boolean satisfiesLevel1Rules(DynamicClinicianAssignment assignment) {
        MultiLevelAssignmentSolution solution = getSolutionFromAssignment(assignment);
        if (solution == null || solution.getLevel1Rules() == null) {
            return true;
        }

        Map<String, Object> patientData = assignment.getAssignmentRequest().toAttributeMap();
        Map<String, Object> clinicianData = getClinicianAttributeMap(assignment.getEffectiveAssignedClinician());

        return solution.getLevel1Rules().stream()
                .filter(DynamicRule::isHardConstraint)
                .allMatch(rule -> rule.evaluate(patientData, clinicianData));
    }

    /**
     * Check if assignment satisfies Level 2 capacity rules.
     */
    private boolean satisfiesLevel2CapacityRules(DynamicClinicianAssignment assignment) {
        MultiLevelAssignmentSolution solution = getSolutionFromAssignment(assignment);
        if (solution == null || solution.getLevel2Rules() == null) {
            return true;
        }

        Map<String, Object> patientData = assignment.getAssignmentRequest().toAttributeMap();
        Map<String, Object> clinicianData = getClinicianAttributeMap(assignment.getEffectiveAssignedClinician());

        return solution.getLevel2Rules().stream()
                .filter(DynamicRule::isHardConstraint)
                .allMatch(rule -> rule.evaluate(patientData, clinicianData));
    }

    /**
     * Check if assignment satisfies Level 3 relaxation rules.
     */
    private boolean satisfiesLevel3RelaxationRules(DynamicClinicianAssignment assignment) {
        MultiLevelAssignmentSolution solution = getSolutionFromAssignment(assignment);
        if (solution == null || solution.getLevel3Rules() == null) {
            return true;
        }

        Map<String, Object> patientData = assignment.getAssignmentRequest().toAttributeMap();
        Map<String, Object> clinicianData = getClinicianAttributeMap(assignment.getEffectiveAssignedClinician());

        return solution.getLevel3Rules().stream()
                .allMatch(rule -> rule.evaluate(patientData, clinicianData));
    }

    /**
     * Calculate dynamic soft score based on configured soft rules.
     */
    private int calculateDynamicSoftScore(DynamicClinicianAssignment assignment) {
        MultiLevelAssignmentSolution solution = getSolutionFromAssignment(assignment);
        if (solution == null || solution.getDynamicRules() == null) {
            return 0;
        }

        Map<String, Object> patientData = assignment.getAssignmentRequest().toAttributeMap();
        Map<String, Object> clinicianData = getClinicianAttributeMap(assignment.getEffectiveAssignedClinician());

        return solution.getDynamicRules().stream()
                .filter(DynamicRule::isSoftConstraint)
                .mapToInt(rule -> {
                    if (rule.evaluate(patientData, clinicianData)) {
                        return (int) rule.getEffectiveWeight();
                    }
                    return 0;
                })
                .sum();
    }

    // Additional helper methods would be implemented here...
    // (Placeholder implementations for brevity)
    
    private MultiLevelAssignmentSolution getSolutionFromAssignment(DynamicClinicianAssignment assignment) {
        // Implementation would retrieve the solution context
        return null;
    }

    private Map<String, Object> getClinicianAttributeMap(Object clinician) {
        // Implementation would convert clinician to attribute map
        return Map.of();
    }

    private boolean exceedsTravelTimeLimit(DynamicClinicianAssignment assignment) {
        // Implementation would check travel time constraints
        return false;
    }

    private boolean hasHistoricalAssignment(DynamicClinicianAssignment assignment) {
        // Implementation would check assignment history
        return false;
    }

    private boolean isPreferredClinician(DynamicClinicianAssignment assignment) {
        // Implementation would check patient preferences
        return false;
    }

    private int calculateDistancePenalty(DynamicClinicianAssignment assignment) {
        // Implementation would calculate distance-based penalty
        return 0;
    }

    private int calculateWorkloadImbalance(int assignmentCount) {
        // Implementation would calculate workload imbalance penalty
        int idealWorkload = 6;
        return Math.abs(assignmentCount - idealWorkload) * 10;
    }
}
