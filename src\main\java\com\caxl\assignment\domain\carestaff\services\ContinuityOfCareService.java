package com.caxl.assignment.domain.services;

import com.caxl.assignment.application.port.out.persistence.CareStaffPort;
import com.caxl.assignment.application.port.out.persistence.PatientPort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Domain service for calculating continuity of care scores and managing care relationships.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContinuityOfCareService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Calculate continuity score for a care staff member with a patient.
     */
    public ContinuityScore calculateContinuityScore(UUID careStaffId, UUID patientId, 
                                                   PatientPort.PatientDomain patient,
                                                   int recentVisitDays) {
        
        log.debug("Calculating continuity score for staff {} and patient {}", careStaffId, patientId);

        double totalScore = 0.0;
        StringBuilder rationale = new StringBuilder();

        // 1. Check if staff is explicitly preferred by patient
        boolean isPreferred = patient.preferredCareStaffIds() != null && 
                             patient.preferredCareStaffIds().contains(careStaffId);
        
        if (isPreferred) {
            totalScore += 20.0; // High bonus for preferred staff
            rationale.append("Preferred(+20) ");
            log.debug("Staff {} is preferred by patient {}", careStaffId, patientId);
        }

        // 2. Count recent visits by this staff member
        int recentVisitCount = countRecentVisits(careStaffId, patientId, recentVisitDays);
        double recentVisitBonus = recentVisitCount * 5.0; // 5 points per recent visit
        totalScore += recentVisitBonus;
        
        if (recentVisitCount > 0) {
            rationale.append(String.format("RecentVisits(%d,+%.1f) ", recentVisitCount, recentVisitBonus));
            log.debug("Staff {} has {} recent visits with patient {}", careStaffId, recentVisitCount, patientId);
        }

        // 3. Calculate total historical visits
        int totalVisitCount = countTotalVisits(careStaffId, patientId);
        double historicalBonus = Math.min(totalVisitCount * 2.0, 20.0); // Max 20 points for history
        totalScore += historicalBonus;
        
        if (totalVisitCount > 0) {
            rationale.append(String.format("History(%d,+%.1f) ", totalVisitCount, historicalBonus));
            log.debug("Staff {} has {} total historical visits with patient {}", careStaffId, totalVisitCount, patientId);
        }

        // 4. Check for successful outcomes in previous visits
        double outcomeBonus = calculateOutcomeBonus(careStaffId, patientId);
        totalScore += outcomeBonus;
        
        if (outcomeBonus > 0) {
            rationale.append(String.format("Outcomes(+%.1f) ", outcomeBonus));
        }

        // 5. Check for care plan familiarity
        double familiarityBonus = calculateFamiliarityBonus(careStaffId, patientId);
        totalScore += familiarityBonus;
        
        if (familiarityBonus > 0) {
            rationale.append(String.format("Familiarity(+%.1f) ", familiarityBonus));
        }

        // 6. Check for care team consistency
        double teamConsistencyBonus = calculateTeamConsistencyBonus(careStaffId, patientId);
        totalScore += teamConsistencyBonus;
        
        if (teamConsistencyBonus > 0) {
            rationale.append(String.format("TeamConsistency(+%.1f) ", teamConsistencyBonus));
        }

        ContinuityLevel level = determineContinuityLevel(totalScore, recentVisitCount, totalVisitCount, isPreferred);
        
        log.info("Continuity score for staff {} and patient {}: {} (Level: {})", 
                careStaffId, patientId, totalScore, level);

        return new ContinuityScore(
                totalScore,
                level,
                recentVisitCount,
                totalVisitCount,
                isPreferred,
                rationale.toString().trim()
        );
    }

    /**
     * Count recent visits by care staff for patient within specified days.
     */
    private int countRecentVisits(UUID careStaffId, UUID patientId, int recentDays) {
        String sql = """
            SELECT COUNT(*) 
            FROM appointment a
            WHERE a.carestaff_id = ? 
            AND a.patient_id = ? 
            AND a.status IN ('completed', 'in_progress')
            AND a.scheduled_start_time >= ?
            """;
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(recentDays);
        
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, 
                    careStaffId, patientId, cutoffDate);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.warn("Error counting recent visits for staff {} and patient {}: {}", 
                    careStaffId, patientId, e.getMessage());
            return 0;
        }
    }

    /**
     * Count total historical visits by care staff for patient.
     */
    private int countTotalVisits(UUID careStaffId, UUID patientId) {
        String sql = """
            SELECT COUNT(*) 
            FROM appointment a
            WHERE a.carestaff_id = ? 
            AND a.patient_id = ? 
            AND a.status = 'completed'
            """;
        
        try {
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, careStaffId, patientId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.warn("Error counting total visits for staff {} and patient {}: {}", 
                    careStaffId, patientId, e.getMessage());
            return 0;
        }
    }

    /**
     * Calculate bonus based on successful outcomes in previous visits.
     */
    private double calculateOutcomeBonus(UUID careStaffId, UUID patientId) {
        String sql = """
            SELECT AVG(CASE 
                WHEN a.notes ILIKE '%excellent%' OR a.notes ILIKE '%successful%' THEN 3.0
                WHEN a.notes ILIKE '%good%' OR a.notes ILIKE '%satisfactory%' THEN 2.0
                WHEN a.notes ILIKE '%poor%' OR a.notes ILIKE '%difficult%' THEN -1.0
                ELSE 1.0
            END) as avg_outcome
            FROM appointment a
            WHERE a.carestaff_id = ? 
            AND a.patient_id = ? 
            AND a.status = 'completed'
            AND a.notes IS NOT NULL
            AND a.scheduled_start_time >= ?
            """;
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(6); // Last 6 months
        
        try {
            Double avgOutcome = jdbcTemplate.queryForObject(sql, Double.class, 
                    careStaffId, patientId, cutoffDate);
            return avgOutcome != null ? Math.max(0, avgOutcome * 2.0) : 0.0; // Scale to 0-6 points
        } catch (Exception e) {
            log.debug("No outcome data found for staff {} and patient {}", careStaffId, patientId);
            return 0.0;
        }
    }

    /**
     * Calculate bonus for care plan familiarity.
     */
    private double calculateFamiliarityBonus(UUID careStaffId, UUID patientId) {
        // Check if staff has worked with similar conditions/care plans
        String sql = """
            SELECT COUNT(DISTINCT sr.visit_type)
            FROM appointment a
            JOIN service_request sr ON a.service_request_id = sr.request_id
            WHERE a.carestaff_id = ?
            AND a.patient_id = ?
            AND a.status = 'completed'
            """;
        
        try {
            Integer visitTypeCount = jdbcTemplate.queryForObject(sql, Integer.class, careStaffId, patientId);
            // More visit types = more familiarity with patient's needs
            return visitTypeCount != null ? Math.min(visitTypeCount * 1.5, 10.0) : 0.0;
        } catch (Exception e) {
            log.debug("Error calculating familiarity bonus for staff {} and patient {}: {}", 
                    careStaffId, patientId, e.getMessage());
            return 0.0;
        }
    }

    /**
     * Calculate bonus for care team consistency.
     */
    private double calculateTeamConsistencyBonus(UUID careStaffId, UUID patientId) {
        // Check if this staff member is part of a consistent care team for this patient
        String sql = """
            SELECT COUNT(DISTINCT a.carestaff_id) as team_size
            FROM appointment a
            WHERE a.patient_id = ?
            AND a.status = 'completed'
            AND a.scheduled_start_time >= ?
            """;
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(3); // Last 3 months
        
        try {
            Integer teamSize = jdbcTemplate.queryForObject(sql, Integer.class, patientId, cutoffDate);
            if (teamSize != null && teamSize <= 3) {
                // Smaller, consistent team gets bonus
                return 5.0 - (teamSize * 1.0); // 4 points for 1 person, 2 points for 3 people
            }
            return 0.0;
        } catch (Exception e) {
            log.debug("Error calculating team consistency bonus for staff {} and patient {}: {}", 
                    careStaffId, patientId, e.getMessage());
            return 0.0;
        }
    }

    /**
     * Determine continuity level based on score and visit history.
     */
    private ContinuityLevel determineContinuityLevel(double score, int recentVisits, 
                                                   int totalVisits, boolean isPreferred) {
        if (isPreferred && recentVisits >= 2) {
            return ContinuityLevel.EXCELLENT;
        } else if (score >= 30.0 || (recentVisits >= 3 && totalVisits >= 5)) {
            return ContinuityLevel.HIGH;
        } else if (score >= 15.0 || (recentVisits >= 1 && totalVisits >= 2)) {
            return ContinuityLevel.MODERATE;
        } else if (totalVisits > 0) {
            return ContinuityLevel.LOW;
        } else {
            return ContinuityLevel.NONE;
        }
    }

    /**
     * Continuity score result.
     */
    public record ContinuityScore(
            double score,
            ContinuityLevel level,
            int recentVisitCount,
            int totalVisitCount,
            boolean isPreferred,
            String rationale
    ) {}

    /**
     * Continuity level enumeration.
     */
    public enum ContinuityLevel {
        NONE(0.0),
        LOW(1.0),
        MODERATE(1.5),
        HIGH(2.0),
        EXCELLENT(2.5);

        private final double multiplier;

        ContinuityLevel(double multiplier) {
            this.multiplier = multiplier;
        }

        public double getMultiplier() {
            return multiplier;
        }
    }
}
