{"rules": [{"section": "HARD CONSTRAINTS", "type": "section_header"}, {"ruleId": "skill-match", "name": "Required Skills Match", "description": "Carestaff must have all required skills for a patient or higher level skills in the hierarchy", "type": "HARD", "condition": {"operator": "CONTAINS_ALL", "leftOperand": "$carestaff.skills", "rightOperand": "$patient.required_skills", "function": null, "parameters": null}, "enabled": true, "tags": ["matching", "skills"], "domain": "matching", "rationalePositive": "Care staff has all required skills", "rationaleNegative": "Care staff is missing required skills", "weight": 1.0, "checkHierarchy": true}]}