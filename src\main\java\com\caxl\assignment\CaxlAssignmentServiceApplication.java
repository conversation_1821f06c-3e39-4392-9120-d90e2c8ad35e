package com.caxl.assignment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * Main application class for the CAXL Assignment Service.
 *
 * A comprehensive assignment optimization system that includes:
 * - Sophisticated clinician-patient matching with OptaPlanner constraint solver
 * - CareStaff matching service with configurable constraints and spatial filtering
 * - Hexagonal architecture with clean separation of concerns
 * - Production-ready with comprehensive monitoring and error handling
 */
@SpringBootApplication
@ConfigurationPropertiesScan
public class CaxlAssignmentServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CaxlAssignmentServiceApplication.class, args);
    }
}
