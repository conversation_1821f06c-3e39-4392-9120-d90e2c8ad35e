package com.caxl.assignment.application.services.common;

import com.caxl.assignment.application.services.common.ServiceResults.*;
import com.caxl.assignment.application.services.common.ValidationUtils.ValidationResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

/**
 * Base service class that provides common patterns and utilities
 * used across different service implementations.
 */
@Slf4j
public abstract class BaseService {
    
    /**
     * Execute an operation with common error handling and logging.
     */
    protected <T> ServiceResult<T> executeWithErrorHandling(String operationName, Supplier<T> operation) {
        log.debug("Starting operation: {}", operationName);
        try {
            T result = operation.get();
            log.debug("Operation completed successfully: {}", operationName);
            return ServiceResult.success(result);
        } catch (IllegalArgumentException e) {
            log.warn("Validation error in operation {}: {}", operationName, e.getMessage());
            return ServiceResult.failure("Validation error: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error in operation {}: {}", operationName, e.getMessage(), e);
            return ServiceResult.failure("Operation failed: " + e.getMessage());
        }
    }
    
    /**
     * Execute an async operation with common error handling.
     */
    protected <T> CompletableFuture<ServiceResult<T>> executeAsyncWithErrorHandling(
            String operationName, 
            Supplier<CompletableFuture<T>> operation) {
        
        log.debug("Starting async operation: {}", operationName);
        try {
            return operation.get()
                    .thenApply(result -> {
                        log.debug("Async operation completed successfully: {}", operationName);
                        return ServiceResult.success(result);
                    })
                    .exceptionally(throwable -> {
                        log.error("Error in async operation {}: {}", operationName, throwable.getMessage(), throwable);
                        return ServiceResult.failure("Async operation failed: " + throwable.getMessage());
                    });
        } catch (Exception e) {
            log.error("Error starting async operation {}: {}", operationName, e.getMessage(), e);
            return CompletableFuture.completedFuture(
                ServiceResult.failure("Failed to start operation: " + e.getMessage())
            );
        }
    }
    
    /**
     * Validate and execute an operation.
     */
    protected <T> ServiceResult<T> validateAndExecute(String operationName, 
                                                      Supplier<ValidationResult> validator,
                                                      Supplier<T> operation) {
        log.debug("Validating and executing operation: {}", operationName);
        
        ValidationResult validation = validator.get();
        if (!validation.isValid()) {
            log.warn("Validation failed for operation {}: {}", operationName, validation.getErrors());
            return ServiceResult.failure(validation.getErrors());
        }
        
        return executeWithErrorHandling(operationName, operation);
    }
    
    /**
     * Common validation for null or empty parameters.
     */
    protected ValidationResult validateRequiredParameters(Object... parameters) {
        return ValidationUtils.builder()
                .validate(parameters != null, "Parameters cannot be null")
                .build();
    }
    
    /**
     * Common method to find an entity by ID from a list.
     */
    protected <T> T findEntityById(List<T> entities, String id, java.util.function.Function<T, String> idExtractor) {
        if (entities == null || id == null) {
            return null;
        }
        
        return entities.stream()
                .filter(entity -> id.equals(idExtractor.apply(entity)))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Common method to check if an operation should be rate limited.
     */
    protected boolean shouldRateLimit(String operationId, LocalDateTime lastExecution, java.time.Duration minInterval) {
        if (lastExecution == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return now.isBefore(lastExecution.plus(minInterval));
    }
    
    /**
     * Create a standardized operation result with timing information.
     */
    protected OperationResult createTimedOperationResult(String operationName, 
                                                        LocalDateTime startTime, 
                                                        boolean success, 
                                                        String message) {
        java.time.Duration duration = java.time.Duration.between(startTime, LocalDateTime.now());
        String timedMessage = String.format("%s (completed in %d ms)", message, duration.toMillis());
        
        if (success) {
            return OperationResult.success(timedMessage);
        } else {
            return OperationResult.failure(timedMessage);
        }
    }
    
    /**
     * Log method entry for debugging.
     */
    protected void logMethodEntry(String methodName, Object... parameters) {
        if (log.isDebugEnabled()) {
            log.debug("Entering method: {} with parameters: {}", methodName, java.util.Arrays.toString(parameters));
        }
    }
    
    /**
     * Log method exit for debugging.
     */
    protected void logMethodExit(String methodName, Object result) {
        if (log.isDebugEnabled()) {
            log.debug("Exiting method: {} with result type: {}", methodName, 
                     result != null ? result.getClass().getSimpleName() : "null");
        }
    }
    
    /**
     * Common pattern for copying schedules with null safety.
     */
    protected <T> T copyScheduleSafely(T original, java.util.function.Function<T, T> copyFunction, String operationName) {
        if (original == null) {
            log.warn("Cannot copy null schedule for operation: {}", operationName);
            return null;
        }
        
        try {
            return copyFunction.apply(original);
        } catch (Exception e) {
            log.error("Error copying schedule for operation {}: {}", operationName, e.getMessage(), e);
            return null;
        }
    }
}
