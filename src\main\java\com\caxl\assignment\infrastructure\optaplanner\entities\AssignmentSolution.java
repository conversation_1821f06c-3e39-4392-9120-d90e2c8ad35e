package com.caxl.assignment.infrastructure.optaplanner.entities;

import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty;
import ai.timefold.solver.core.api.domain.solution.PlanningScore;
import ai.timefold.solver.core.api.domain.solution.PlanningSolution;
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * AssignmentSolution represents the complete solution for shift scheduling.
 * This is the main planning solution that Timefold will optimize.
 */
@PlanningSolution
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentSolution {

    /**
     * List of shifts to be assigned (planning entities).
     */
    @PlanningEntityCollectionProperty
    private List<Shift> shifts;

    /**
     * Available clinicians for assignment (value range for planning variables).
     */
    @ValueRangeProvider(id = "clinicianRange")
    @ProblemFactCollectionProperty
    private List<Clinician> clinicians;

    /**
     * Patients requiring care (problem facts).
     */
    @ProblemFactCollectionProperty
    private List<Patient> patients;

    /**
     * The planning score calculated by Timefold.
     */
    @PlanningScore
    private HardSoftScore score;

    // === HELPER METHODS ===

    /**
     * Get clinician by ID.
     */
    public Clinician getClinicianById(String clinicianId) {
        return clinicians.stream()
                .filter(clinician -> clinician.getId().equals(clinicianId))
                .findFirst()
                .orElse(null);
    }

    /**
     * Get patient by ID.
     */
    public Patient getPatientById(String patientId) {
        return patients.stream()
                .filter(patient -> patient.getId().equals(patientId))
                .findFirst()
                .orElse(null);
    }

    /**
     * Get total number of unassigned shifts.
     */
    public long getUnassignedShiftsCount() {
        return shifts.stream()
                .filter(shift -> shift.getAssignedClinician() == null)
                .count();
    }

    /**
     * Get total number of assigned shifts.
     */
    public long getAssignedShiftsCount() {
        return shifts.stream()
                .filter(shift -> shift.getAssignedClinician() != null)
                .count();
    }

    /**
     * Check if solution is feasible (no hard constraint violations).
     */
    public boolean isFeasible() {
        return score != null && score.hardScore() >= 0;
    }

    /**
     * Get the total number of shifts.
     */
    public int getTotalShifts() {
        return shifts != null ? shifts.size() : 0;
    }

    /**
     * Get the total number of clinicians.
     */
    public int getTotalClinicians() {
        return clinicians != null ? clinicians.size() : 0;
    }

    /**
     * Get the total number of patients.
     */
    public int getTotalPatients() {
        return patients != null ? patients.size() : 0;
    }
}
