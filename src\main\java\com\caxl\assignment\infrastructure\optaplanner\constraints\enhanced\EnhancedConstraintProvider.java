package com.caxl.assignment.infrastructure.optaplanner.constraints.enhanced;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintCollectors;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.Shift;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * Enhanced constraint provider implementing Timefold best practices for healthcare scheduling.
 * Features advanced constraint patterns for:
 * - Skill-based matching with hierarchies
 * - Workload balancing and fairness
 * - Travel time optimization
 * - Continuity of care
 * - Multi-objective optimization
 */
@Component
public class EnhancedConstraintProvider implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
        return new Constraint[]{
            // === HARD CONSTRAINTS ===
            // Capacity and resource constraints
            shiftCapacityConstraint(constraintFactory),
            clinicianAvailabilityConstraint(constraintFactory),
            skillRequirementConstraint(constraintFactory),
            workloadLimitConstraint(constraintFactory),

            // Scheduling constraints
            oneShiftPerClinicianPerDayConstraint(constraintFactory),
            patientTimeSlotConstraint(constraintFactory),

            // === SOFT CONSTRAINTS ===
            // Optimization objectives
            workloadFairnessConstraint(constraintFactory),
            travelTimeMinimizationConstraint(constraintFactory),
            continuityOfCareConstraint(constraintFactory),
            skillMatchQualityConstraint(constraintFactory),
            clinicianPreferenceConstraint(constraintFactory),
            patientPreferenceConstraint(constraintFactory),

            // Balancing constraints
            evenWorkloadDistributionConstraint(constraintFactory),
            minimizeOvertimeConstraint(constraintFactory),
            maximizeUtilizationConstraint(constraintFactory)
        };
    }

    // =========================================================================
    // HARD CONSTRAINTS
    // =========================================================================

    /**
     * Hard constraint: Shift cannot exceed maximum patient capacity.
     */
    private Constraint shiftCapacityConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getPatientCount() > shift.getMaxPatients())
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> shift.getPatientCount() - shift.getMaxPatients())
                .asConstraint("Shift patient capacity exceeded");
    }

    /**
     * Hard constraint: Clinician must be available for assigned shift.
     */
    private Constraint clinicianAvailabilityConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> !isClinicianAvailableForShift(shift))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Clinician not available for shift");
    }

    /**
     * Hard constraint: Clinician must have required skills for all patients in shift.
     */
    private Constraint skillRequirementConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> !hasRequiredSkillsForAllPatients(shift))
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> countMissingSkills(shift))
                .asConstraint("Missing required skills");
    }

    /**
     * Hard constraint: Shift workload cannot exceed clinician's daily limit.
     */
    private Constraint workloadLimitConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> shift.getTotalWorkloadPoints() >
                        shift.getAssignedClinician().getMaxDailyWorkloadPoints())
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> shift.getTotalWorkloadPoints() -
                                shift.getAssignedClinician().getMaxDailyWorkloadPoints())
                .asConstraint("Daily workload limit exceeded");
    }

    /**
     * Hard constraint: One shift per clinician per day.
     */
    private Constraint oneShiftPerClinicianPerDayConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .groupBy(Shift::getAssignedClinician, Shift::getShiftDate, ConstraintCollectors.count())
                .filter((clinician, date, count) -> count > 1)
                .penalize(HardSoftScore.ONE_HARD,
                        (clinician, date, count) -> count - 1)
                .asConstraint("Multiple shifts per clinician per day");
    }

    /**
     * Hard constraint: Patient time slot requirements must be met.
     */
    private Constraint patientTimeSlotConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> !shift.getPatientVisits().isEmpty())
                .filter(shift -> !canAccommodateAllPatientTimeSlots(shift))
                .penalize(HardSoftScore.ONE_HARD,
                        shift -> countTimeSlotViolations(shift))
                .asConstraint("Patient time slot requirements not met");
    }

    // =========================================================================
    // SOFT CONSTRAINTS - OPTIMIZATION OBJECTIVES
    // =========================================================================

    /**
     * Soft constraint: Minimize workload variance for fairness.
     */
    private Constraint workloadFairnessConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .groupBy(Shift::getAssignedClinician,
                        ConstraintCollectors.sum(Shift::getTotalWorkloadPoints))
                .penalize(HardSoftScore.ONE_SOFT,
                        (clinician, totalWorkload) -> totalWorkload * totalWorkload / 100)
                .asConstraint("Workload fairness");
    }

    /**
     * Soft constraint: Minimize travel time between consecutive visits.
     */
    private Constraint travelTimeMinimizationConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getPatientVisits().size() > 1)
                .penalize(HardSoftScore.ONE_SOFT,
                        shift -> calculateTotalTravelTime(shift))
                .asConstraint("Travel time minimization");
    }

    /**
     * Soft constraint: Reward continuity of care (same clinician for returning patients).
     */
    private Constraint continuityOfCareConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculateContinuityScore(shift))
                .asConstraint("Continuity of care");
    }

    /**
     * Soft constraint: Reward high-quality skill matches.
     */
    private Constraint skillMatchQualityConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculateSkillMatchScore(shift))
                .asConstraint("Skill match quality");
    }

    /**
     * Soft constraint: Consider clinician preferences for shift types and areas.
     */
    private Constraint clinicianPreferenceConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculateClinicianPreferenceScore(shift))
                .asConstraint("Clinician preferences");
    }

    /**
     * Soft constraint: Consider patient preferences for clinician characteristics.
     */
    private Constraint patientPreferenceConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> calculatePatientPreferenceScore(shift))
                .asConstraint("Patient preferences");
    }

    /**
     * Soft constraint: Promote even distribution of workload across time periods.
     */
    private Constraint evenWorkloadDistributionConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .groupBy(shift -> shift.getShiftDate().getDayOfWeek(),
                        ConstraintCollectors.sum(Shift::getTotalWorkloadPoints))
                .penalize(HardSoftScore.ONE_SOFT,
                        (dayOfWeek, totalWorkload) -> Math.abs(totalWorkload - getTargetDailyWorkload()) / 10)
                .asConstraint("Even workload distribution");
    }

    /**
     * Soft constraint: Minimize overtime assignments.
     */
    private Constraint minimizeOvertimeConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .filter(shift -> isOvertimeShift(shift))
                .penalize(HardSoftScore.ONE_SOFT,
                        shift -> calculateOvertimePenalty(shift))
                .asConstraint("Minimize overtime");
    }

    /**
     * Soft constraint: Maximize clinician utilization.
     */
    private Constraint maximizeUtilizationConstraint(ConstraintFactory constraintFactory) {
        return constraintFactory
                .forEach(Shift.class)
                .filter(shift -> shift.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        shift -> shift.getTotalWorkloadPoints())
                .asConstraint("Maximize utilization");
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    private boolean isClinicianAvailableForShift(Shift shift) {
        // Implementation would check clinician's availability slots
        return true; // Simplified for example
    }

    private boolean hasRequiredSkillsForAllPatients(Shift shift) {
        // Implementation would check skill requirements
        return true; // Simplified for example
    }

    private int countMissingSkills(Shift shift) {
        // Implementation would count missing skills
        return 0; // Simplified for example
    }

    private boolean canAccommodateAllPatientTimeSlots(Shift shift) {
        // Implementation would check time slot compatibility
        return true; // Simplified for example
    }

    private int countTimeSlotViolations(Shift shift) {
        // Implementation would count time slot violations
        return 0; // Simplified for example
    }

    private int calculateTotalTravelTime(Shift shift) {
        List<Patient> visits = shift.getPatientVisits();
        if (visits.size() < 2) return 0;

        int totalTravelTime = 0;
        for (int i = 0; i < visits.size() - 1; i++) {
            Patient from = visits.get(i);
            Patient to = visits.get(i + 1);
            totalTravelTime += estimateTravelTime(from, to);
        }
        return totalTravelTime;
    }

    private int estimateTravelTime(Patient from, Patient to) {
        // Simplified travel time calculation based on postal codes
        String fromPostal = from.getLocation().getPostalCode();
        String toPostal = to.getLocation().getPostalCode();

        if (fromPostal.equals(toPostal)) return 5; // Same area
        if (fromPostal.substring(0, 3).equals(toPostal.substring(0, 3))) return 15; // Same region
        return 30; // Different regions
    }

    private int calculateContinuityScore(Shift shift) {
        if (shift.getAssignedClinician() == null) return 0;

        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    // Check if this clinician has treated this patient before
                    if (patient.getPreferences() != null &&
                        patient.getPreferences().getPreferredCarestaffIds()
                               .contains(shift.getAssignedClinician().getId())) {
                        return 15; // High continuity bonus
                    }
                    if (patient.getPreferences() != null &&
                        patient.getPreferences().getPreviousCarestaffIds()
                               .contains(shift.getAssignedClinician().getId())) {
                        return 10; // Previous care bonus
                    }
                    return 0;
                })
                .sum();
    }

    private int calculateSkillMatchScore(Shift shift) {
        if (shift.getAssignedClinician() == null) return 0;

        Set<String> clinicianSkills = Set.copyOf(shift.getAssignedClinician().getSkills());

        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    Set<String> requiredSkills = Set.copyOf(patient.getRequiredSkills());

                    // Exact match bonus
                    if (clinicianSkills.containsAll(requiredSkills)) {
                        return 10;
                    }

                    // Partial match scoring
                    long matchingSkills = requiredSkills.stream()
                            .filter(clinicianSkills::contains)
                            .count();

                    return (int) (matchingSkills * 5 / requiredSkills.size());
                })
                .sum();
    }

    private int calculateClinicianPreferenceScore(Shift shift) {
        if (shift.getAssignedClinician() == null) return 0;

        int score = 0;
        Clinician clinician = shift.getAssignedClinician();

        // Preferred shift type (simplified - would need to add this field to ClinicianProfile)
        // For now, use a simplified preference based on experience
        if (shift.getShiftType() == Shift.ShiftType.MORNING && clinician.getProfile().getExperienceYears() > 5) {
            score += 20;
        }

        // Preferred service area
        if (clinician.getLocation().getServiceAreas().contains(shift.getServiceArea())) {
            score += 15;
        }

        return score;
    }

    private int calculatePatientPreferenceScore(Shift shift) {
        if (shift.getAssignedClinician() == null) return 0;

        return shift.getPatientVisits().stream()
                .mapToInt(patient -> {
                    int score = 0;

                    // Gender preference
                    if (patient.getPreferences() != null &&
                        patient.getPreferences().getPreferredCarestaffGender() != null &&
                        patient.getPreferences().getPreferredCarestaffGender()
                               .equals(shift.getAssignedClinician().getPii().getGender())) {
                        score += 10;
                    }

                    // Language preference
                    if (patient.getPreferences() != null &&
                        patient.getPreferences().getPreferredLanguages().stream()
                               .anyMatch(lang -> shift.getAssignedClinician().getLanguages().contains(lang))) {
                        score += 8;
                    }

                    return score;
                })
                .sum();
    }

    private int getTargetDailyWorkload() {
        return 60; // Target daily workload points
    }

    private boolean isOvertimeShift(Shift shift) {
        if (shift.getAssignedClinician() == null) return false;

        Duration shiftDuration = Duration.between(shift.getShiftStartTime(), shift.getShiftEndTime());
        Duration standardShift = Duration.ofHours(8);

        return shiftDuration.compareTo(standardShift) > 0 ||
               shift.getTotalWorkloadPoints() > shift.getAssignedClinician().getMaxDailyWorkloadPoints();
    }

    private int calculateOvertimePenalty(Shift shift) {
        int penalty = 0;

        // Time-based overtime
        Duration shiftDuration = Duration.between(shift.getShiftStartTime(), shift.getShiftEndTime());
        Duration standardShift = Duration.ofHours(8);
        if (shiftDuration.compareTo(standardShift) > 0) {
            penalty += (int) (shiftDuration.minus(standardShift).toMinutes() / 15);
        }

        // Workload-based overtime
        if (shift.getAssignedClinician() != null) {
            int excess = shift.getTotalWorkloadPoints() - shift.getAssignedClinician().getMaxDailyWorkloadPoints();
            if (excess > 0) {
                penalty += excess * 2;
            }
        }

        return penalty;
    }
}
