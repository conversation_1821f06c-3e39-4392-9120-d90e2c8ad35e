spring:
  application:
    name: caxl-assignment-service-test
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  h2:
    console:
      enabled: true

logging:
  level:
    root: INFO
    com.caxl.assignment: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG

# Disable Timefold for tests
timefold:
  solver:
    termination:
      spent-limit: 1s

# CareStaff Matching Service Test Configuration
carestaff:
  matching:
    default-search-radius-km: 100.0
    min-score-threshold: 0.0
    overlap-threshold-minutes: 1
    min-time-before-visit-minutes: 30
    min-time-after-visit-minutes: 30
    geofence-strict-containment: false
    staff-service-geofence-types:
      - service_area
      - test_area
