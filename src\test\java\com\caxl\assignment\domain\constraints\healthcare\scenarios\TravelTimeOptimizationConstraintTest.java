package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.models.healthcare.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * Comprehensive test suite for travel time optimization constraints.
 * Tests soft constraints that minimize travel time between consecutive visits.
 */
@DisplayName("Travel Time Optimization Constraint Tests")
class TravelTimeOptimizationConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
            new EnhancedHomecareConstraintProvider(),
            HomecareSchedule.class,
            CareStaff.class,
            HomecareVisit.class
        );
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Travel Time Optimization")
    class BasicTravelTimeOptimization {

        @Test
        @DisplayName("testTravelTimeOptimization_whenVisitsAreNearby_shouldHaveMinimalPenalty")
        void testTravelTimeOptimization_whenVisitsAreNearby_shouldHaveMinimalPenalty() {
            // Given: Two visits close to each other
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(10.1, 20.1); // Very close
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 12, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time optimization
            // Then: Should have minimal penalty due to short distance
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(1); // Minimal penalty for short distance
        }

        @Test
        @DisplayName("testTravelTimeOptimization_whenVisitsAreFarApart_shouldHaveHigherPenalty")
        void testTravelTimeOptimization_whenVisitsAreFarApart_shouldHaveHigherPenalty() {
            // Given: Two visits far from each other
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(15.0, 25.0); // Far apart
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 12, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time optimization
            // Then: Should have higher penalty due to long distance
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(50); // Higher penalty for long distance
        }

        @Test
        @DisplayName("testTravelTimeOptimization_whenSingleVisit_shouldHaveNoPenalty")
        void testTravelTimeOptimization_whenSingleVisit_shouldHaveNoPenalty() {
            // Given: Staff with only one visit
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit = testDataFactory.createBasicHomecareVisit();
            staff.setVisits(Arrays.asList(visit));

            // When: Checking travel time optimization
            // Then: Should have no penalty for single visit
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit)
                .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple Visit Sequences")
    class MultipleVisitSequences {

        @Test
        @DisplayName("testTravelTimeOptimization_whenOptimalSequence_shouldHaveMinimalPenalty")
        void testTravelTimeOptimization_whenOptimalSequence_shouldHaveMinimalPenalty() {
            // Given: Three visits in optimal geographical sequence
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(10.5, 20.5);
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            
            HomecareVisit visit3 = testDataFactory.createVisitAtLocation(11.0, 21.0);
            visit3.setStartTime(LocalDateTime.of(2025, 5, 28, 13, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2, visit3));

            // When: Checking travel time optimization for sequence
            // Then: Should have minimal penalty for optimal routing
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2, visit3)
                .penalizesBy(10); // Low penalty for good sequence
        }

        @Test
        @DisplayName("testTravelTimeOptimization_whenSuboptimalSequence_shouldHaveHigherPenalty")
        void testTravelTimeOptimization_whenSuboptimalSequence_shouldHaveHigherPenalty() {
            // Given: Three visits in suboptimal sequence (zigzag pattern)
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(15.0, 25.0); // Far
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            
            HomecareVisit visit3 = testDataFactory.createVisitAtLocation(10.5, 20.5); // Back to start area
            visit3.setStartTime(LocalDateTime.of(2025, 5, 28, 13, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2, visit3));

            // When: Checking travel time optimization for zigzag sequence
            // Then: Should have higher penalty for poor routing
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2, visit3)
                .penalizesBy(100); // High penalty for zigzag pattern
        }

        @Test
        @DisplayName("testTravelTimeOptimization_whenTightSchedule_shouldPrioritizeProximity")
        void testTravelTimeOptimization_whenTightSchedule_shouldPrioritizeProximity() {
            // Given: Visits with tight schedule requiring proximity
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(10.1, 20.1); // Close
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 10, 30)); // Tight schedule
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 11, 30));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time with tight schedule
            // Then: Should have very low penalty for close visits with tight timing
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(1); // Very low penalty for proximity with tight schedule
        }
    }

    @Nested
    @DisplayName("Rush Hour and Traffic Considerations")
    class RushHourTrafficConsiderations {

        @Test
        @DisplayName("testTravelTimeOptimization_duringRushHour_shouldHaveIncreasedPenalty")
        void testTravelTimeOptimization_duringRushHour_shouldHaveIncreasedPenalty() {
            // Given: Travel during rush hour
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 7, 30)); // Rush hour start
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 8, 30));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(12.0, 22.0);
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0)); // Rush hour travel
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time during rush hour
            // Then: Should have increased penalty for rush hour travel
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(75); // Higher penalty for rush hour travel
        }

        @Test
        @DisplayName("testTravelTimeOptimization_duringOffPeakHours_shouldHaveStandardPenalty")
        void testTravelTimeOptimization_duringOffPeakHours_shouldHaveStandardPenalty() {
            // Given: Travel during off-peak hours
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0)); // Off-peak
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 12, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(12.0, 22.0);
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 13, 0)); // Off-peak travel
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 14, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time during off-peak hours
            // Then: Should have standard penalty for off-peak travel
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(30); // Standard penalty for off-peak travel
        }
    }

    @Nested
    @DisplayName("Geographic Zone Optimization")
    class GeographicZoneOptimization {

        @Test
        @DisplayName("testTravelTimeOptimization_withinSameZone_shouldHaveMinimalPenalty")
        void testTravelTimeOptimization_withinSameZone_shouldHaveMinimalPenalty() {
            // Given: Visits within the same geographic zone
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitInZone("North");
            HomecareVisit visit2 = testDataFactory.createVisitInZone("North");
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time within same zone
            // Then: Should have minimal penalty for same zone travel
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(5); // Very low penalty for same zone
        }

        @Test
        @DisplayName("testTravelTimeOptimization_acrossDifferentZones_shouldHaveHigherPenalty")
        void testTravelTimeOptimization_acrossDifferentZones_shouldHaveHigherPenalty() {
            // Given: Visits across different geographic zones
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitInZone("North");
            HomecareVisit visit2 = testDataFactory.createVisitInZone("South");
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time across zones
            // Then: Should have higher penalty for cross-zone travel
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(40); // Higher penalty for cross-zone travel
        }

        @Test
        @DisplayName("testTravelTimeOptimization_withAdjacentZones_shouldHaveModeratePenalty")
        void testTravelTimeOptimization_withAdjacentZones_shouldHaveModeratePenalty() {
            // Given: Visits in adjacent geographic zones
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitInZone("North");
            HomecareVisit visit2 = testDataFactory.createVisitInZone("Central"); // Adjacent to North
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time between adjacent zones
            // Then: Should have moderate penalty for adjacent zone travel
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(20); // Moderate penalty for adjacent zones
        }
    }

    @Nested
    @DisplayName("Multiple Staff Travel Optimization")
    class MultipleStaffTravelOptimization {

        @Test
        @DisplayName("testTravelTimeOptimization_withMultipleStaff_shouldOptimizeIndependently")
        void testTravelTimeOptimization_withMultipleStaff_shouldOptimizeIndependently() {
            // Given: Multiple staff with different visit patterns
            CareStaff staff1 = testDataFactory.createCareStaffWithId("STAFF1");
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(10.1, 20.1);
            staff1.setVisits(Arrays.asList(visit1, visit2));

            CareStaff staff2 = testDataFactory.createCareStaffWithId("STAFF2");
            HomecareVisit visit3 = testDataFactory.createVisitAtLocation(15.0, 25.0);
            HomecareVisit visit4 = testDataFactory.createVisitAtLocation(16.0, 26.0);
            staff2.setVisits(Arrays.asList(visit3, visit4));

            // When: Checking travel optimization for both staff
            // Then: Each staff should be optimized independently
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff1, visit1, visit2, staff2, visit3, visit4)
                .penalizesBy(10); // Combined penalty for both staff optimizations
        }

        @Test
        @DisplayName("testTravelTimeOptimization_withOverlappingZones_shouldAvoidConflicts")
        void testTravelTimeOptimization_withOverlappingZones_shouldAvoidConflicts() {
            // Given: Multiple staff working in overlapping zones
            CareStaff staff1 = testDataFactory.createCareStaffWithId("STAFF1");
            CareStaff staff2 = testDataFactory.createCareStaffWithId("STAFF2");
            
            HomecareVisit visit1 = testDataFactory.createVisitInZone("Central");
            HomecareVisit visit2 = testDataFactory.createVisitInZone("Central");
            
            staff1.setVisits(Arrays.asList(visit1));
            staff2.setVisits(Arrays.asList(visit2));

            // When: Checking travel optimization with zone overlap
            // Then: Should handle zone overlap without conflicts
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff1, visit1, staff2, visit2)
                .penalizesBy(0); // No penalty for single visits per staff
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandling {

        @Test
        @DisplayName("testTravelTimeOptimization_withNullLocations_shouldHandleGracefully")
        void testTravelTimeOptimization_withNullLocations_shouldHandleGracefully() {
            // Given: Visits with null locations
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createBasicHomecareVisit();
            visit1.setPatient(testDataFactory.createPatientWithoutAddress());
            
            HomecareVisit visit2 = testDataFactory.createBasicHomecareVisit();
            visit2.setPatient(testDataFactory.createPatientWithoutAddress());
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel optimization with null locations
            // Then: Should handle gracefully without errors
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(0); // No penalty for unmeasurable travel
        }

        @Test
        @DisplayName("testTravelTimeOptimization_withSameLocation_shouldHaveNoPenalty")
        void testTravelTimeOptimization_withSameLocation_shouldHaveNoPenalty() {
            // Given: Multiple visits at the same location
            CareStaff staff = testDataFactory.createBasicCareStaff();
            HomecareVisit visit1 = testDataFactory.createVisitAtLocation(10.0, 20.0);
            HomecareVisit visit2 = testDataFactory.createVisitAtLocation(10.0, 20.0); // Same location
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel optimization for same location
            // Then: Should have no penalty for zero travel distance
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff, visit1, visit2)
                .penalizesBy(0); // No penalty for same location
        }

        @Test
        @DisplayName("testTravelTimeOptimization_withEmptyVisitList_shouldHaveNoPenalty")
        void testTravelTimeOptimization_withEmptyVisitList_shouldHaveNoPenalty() {
            // Given: Staff with no visits
            CareStaff staff = testDataFactory.createBasicCareStaff();
            staff.setVisits(Arrays.asList());

            // When: Checking travel optimization with no visits
            // Then: Should have no penalty for empty visit list
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(staff)
                .penalizesBy(0); // No penalty for no visits
        }
    }
}
