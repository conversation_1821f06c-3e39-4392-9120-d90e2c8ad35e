package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import static org.junit.jupiter.api.Assertions.*;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;
import java.util.Set;

/**
 * Comprehensive test suite for skill matching constraints in homecare scheduling.
 * Tests individual constraint validations, edge cases, and fallback scenarios.
 */
@DisplayName("Skill Matching Constraint Tests")
class SkillMatchingConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Skill Matching")
    class BasicSkillMatching {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianHasRequiredSkill_shouldPass")
        void testSkillMatchingConstraint_whenClinicianHasRequiredSkill_shouldPass() {
            // Given: Care staff with nursing skill
            CareStaff nursingStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "medication-administration"));
            
            // And: Visit requiring nursing skill
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass without penalties
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianLacksRequiredSkill_shouldFail")
        void testSkillMatchingConstraint_whenClinicianLacksRequiredSkill_shouldFail() {
            // Given: Care staff without specialized skill
            CareStaff basicStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("basic-care"));
            
            // And: Visit requiring specialized skill
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("wound-care", "iv-therapy"));
            visit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for missing skills
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenVisitUnassigned_shouldPass")
        void testSkillMatchingConstraint_whenVisitUnassigned_shouldPass() {
            // Given: Unassigned visit (no care staff assigned)
            HomecareVisit unassignedVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (constraint only applies to assigned visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(unassignedVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple Skills Scenarios")
    class MultipleSkillsScenarios {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianHasAllRequiredSkills_shouldPass")
        void testSkillMatchingConstraint_whenClinicianHasAllRequiredSkills_shouldPass() {
            // Given: Care staff with multiple skills
            CareStaff multiSkillStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "wound-care", "medication-administration", "physical-therapy"));
            
            // And: Visit requiring multiple skills
            HomecareVisit complexVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing", "wound-care", "medication-administration"));
            complexVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass without penalties
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(complexVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianHasPartialRequiredSkills_shouldFail")
        void testSkillMatchingConstraint_whenClinicianHasPartialRequiredSkills_shouldFail() {
            // Given: Care staff with some but not all required skills
            CareStaff partialSkillStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "basic-care"));
            
            // And: Visit requiring additional skills
            HomecareVisit complexVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing", "wound-care", "iv-therapy"));
            complexVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for missing skills
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(complexVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianHasExtraSkills_shouldPass")
        void testSkillMatchingConstraint_whenClinicianHasExtraSkills_shouldPass() {
            // Given: Care staff with more skills than required
            CareStaff overQualifiedStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "wound-care", "iv-therapy", "physical-therapy", 
                           "occupational-therapy", "speech-therapy"));
            
            // And: Visit requiring basic skills
            HomecareVisit basicVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            basicVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (extra skills are allowed)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(basicVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Fallback Scenarios")
    class EdgeCasesAndFallbacks {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenNoSkillsRequired_shouldPass")
        void testSkillMatchingConstraint_whenNoSkillsRequired_shouldPass() {
            // Given: Care staff with any skills
            CareStaff anyStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("basic-care"));
            
            // And: Visit requiring no specific skills (companionship)
            HomecareVisit companionshipVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of());
            companionshipVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (no skills required)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(companionshipVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenStaffHasNoSkills_shouldFailForRequiredSkills")
        void testSkillMatchingConstraint_whenStaffHasNoSkills_shouldFailForRequiredSkills() {
            // Given: Care staff with no specialized skills
            CareStaff noSkillStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of());
            
            // And: Visit requiring specific skills
            HomecareVisit skilledVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            skilledVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for missing required skills
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(skilledVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenMultipleVisitsWithDifferentSkillRequirements_shouldEvaluateIndependently")
        void testSkillMatchingConstraint_whenMultipleVisitsWithDifferentSkillRequirements_shouldEvaluateIndependently() {
            // Given: Care staff with specific skills
            CareStaff nursingStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "medication-administration"));
            
            // And: Multiple visits with different skill requirements
            HomecareVisit nursingVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            nursingVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit therapyVisit = testDataFactory.createVisitWithRequiredSkills("visit2", 
                    List.of("physical-therapy"));
            therapyVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass for nursing visit, fail for therapy visit
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(nursingVisit, therapyVisit)
                    .penalizesBy(1); // Only therapy visit should fail
        }
    }

    @Nested
    @DisplayName("Skill Hierarchy and Substitution")
    class SkillHierarchyAndSubstitution {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenHigherLevelSkillCanSubstituteBasic_shouldPass")
        void testSkillMatchingConstraint_whenHigherLevelSkillCanSubstituteBasic_shouldPass() {
            // Given: Care staff with advanced nursing skills
            CareStaff advancedNurse = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("registered-nurse", "critical-care"));
            
            // And: Visit requiring basic nursing
            HomecareVisit basicNursingVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            basicNursingVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (higher skill can substitute lower)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(basicNursingVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenBasicSkillCannotSubstituteAdvanced_shouldFail")
        void testSkillMatchingConstraint_whenBasicSkillCannotSubstituteAdvanced_shouldFail() {
            // Given: Care staff with only basic skills
            CareStaff basicStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing-assistant", "basic-care"));
            
            // And: Visit requiring advanced skills
            HomecareVisit advancedVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("registered-nurse", "iv-therapy"));
            advancedVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (basic skill cannot substitute advanced)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(advancedVisit)
                    .penalizesBy(1);
        }
    }
}
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, nursingStaff)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenClinicianLacksRequiredSkill_shouldFail")
        void testSkillMatchingConstraint_whenClinicianLacksRequiredSkill_shouldFail() {
            // Given: Care staff without wound care skill
            CareStaff basicStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("personal-care", "companionship"));
            
            // And: Visit requiring wound care
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("wound-care"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize heavily
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, basicStaff)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenMultipleSkillsRequired_allPresent_shouldPass")
        void testSkillMatchingConstraint_whenMultipleSkillsRequired_allPresent_shouldPass() {
            // Given: Care staff with multiple skills
            CareStaff skilledStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "wound-care", "medication-administration", "iv-therapy"));
            
            // And: Visit requiring multiple skills
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing", "wound-care", "iv-therapy"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, skilledStaff)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenMultipleSkillsRequired_oneMissing_shouldFail")
        void testSkillMatchingConstraint_whenMultipleSkillsRequired_oneM issing_shouldFail() {
            // Given: Care staff missing one required skill
            CareStaff partiallySkilled = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing", "medication-administration"));
            
            // And: Visit requiring additional skill
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing", "wound-care", "medication-administration"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, partiallySkilled)
                    .penalizesBy(1);
        }
    }

    @Nested
    @DisplayName("Specialized Care Requirements")
    class SpecializedCareRequirements {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenDementiaSpecialtyRequired_matchingStaff_shouldPass")
        void testSkillMatchingConstraint_whenDementiaSpecialtyRequired_matchingStaff_shouldPass() {
            // Given: Care staff with dementia specialty
            CareStaff dementiaSpecialist = testDataFactory.createCareStaffWithSpecialties("staff1", 
                    Set.of("dementia-care", "alzheimers-care", "behavioral-management"));
            
            // And: Visit requiring dementia care
            HomecareVisit visit = testDataFactory.createVisitWithSpecialtyRequirements("visit1", 
                    List.of("dementia-care"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, dementiaSpecialist)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenPediatricSpecialtyRequired_adultOnlyStaff_shouldFail")
        void testSkillMatchingConstraint_whenPediatricSpecialtyRequired_adultOnlyStaff_shouldFail() {
            // Given: Care staff without pediatric training
            CareStaff adultCareStaff = testDataFactory.createCareStaffWithSpecialties("staff1", 
                    Set.of("geriatric-care", "adult-nursing"));
            
            // And: Visit requiring pediatric care
            HomecareVisit visit = testDataFactory.createVisitWithSpecialtyRequirements("visit1", 
                    List.of("pediatric-care"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, adultCareStaff)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenPalliativeCareRequired_appropriateStaff_shouldPass")
        void testSkillMatchingConstraint_whenPalliativeCareRequired_appropriateStaff_shouldPass() {
            // Given: Care staff with palliative care training
            CareStaff palliativeStaff = testDataFactory.createCareStaffWithSpecialties("staff1", 
                    Set.of("palliative-care", "pain-management", "end-of-life-care"));
            
            // And: Visit requiring palliative care
            HomecareVisit visit = testDataFactory.createVisitWithSpecialtyRequirements("visit1", 
                    List.of("palliative-care"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, palliativeStaff)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Skill Hierarchy and Fallback")
    class SkillHierarchyAndFallback {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenRNAssignedToCNATask_skillHierarchy_shouldPass")
        void testSkillMatchingConstraint_whenRNAssignedToCNATask_skillHierarchy_shouldPass() {
            // Given: Registered Nurse with higher credentials
            CareStaff registeredNurse = testDataFactory.createCareStaffWithLicense("staff1", 
                    "RN", Set.of("nursing", "medication-administration", "assessment"));
            
            // And: Visit requiring basic personal care (CNA level)
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("personal-care"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize (RN can do CNA tasks)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, registeredNurse)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenCNAAssignedToRNTask_insufficientCredentials_shouldFail")
        void testSkillMatchingConstraint_whenCNAAssignedToRNTask_insufficientCredentials_shouldFail() {
            // Given: CNA with basic credentials
            CareStaff nursingAssistant = testDataFactory.createCareStaffWithLicense("staff1", 
                    "CNA", Set.of("personal-care", "vital-signs"));
            
            // And: Visit requiring medication administration (RN level)
            HomecareVisit visit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("medication-administration"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize heavily
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, nursingAssistant)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenAlternativeSkillAcceptable_shouldPass")
        void testSkillMatchingConstraint_whenAlternativeSkillAcceptable_shouldPass() {
            // Given: Care staff with equivalent alternative skill
            CareStaff therapist = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("physical-therapy", "mobility-assistance", "rehabilitation"));
            
            // And: Visit requiring mobility assistance (can be done by PT or OT)
            HomecareVisit visit = testDataFactory.createVisitWithAlternativeSkills("visit1", 
                    List.of("mobility-assistance"), List.of("physical-therapy", "occupational-therapy"));
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, therapist)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Emergency and Critical Care")
    class EmergencyAndCriticalCare {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenEmergencyVisit_qualifiedStaff_shouldPass")
        void testSkillMatchingConstraint_whenEmergencyVisit_qualifiedStaff_shouldPass() {
            // Given: Emergency-qualified care staff
            CareStaff emergencyStaff = testDataFactory.createCareStaffWithEmergencySkills("staff1", 
                    Set.of("emergency-response", "cpr", "first-aid", "crisis-intervention"));
            
            // And: Emergency visit
            HomecareVisit emergencyVisit = testDataFactory.createEmergencyVisit("visit1");
            emergencyVisit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(emergencyVisit, emergencyStaff)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenEmergencyVisit_unqualifiedStaff_shouldFail")
        void testSkillMatchingConstraint_whenEmergencyVisit_unqualifiedStaff_shouldFail() {
            // Given: Basic care staff without emergency training
            CareStaff basicStaff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("personal-care", "companionship"));
            
            // And: Emergency visit
            HomecareVisit emergencyVisit = testDataFactory.createEmergencyVisit("visit1");
            emergencyVisit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize heavily
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(emergencyVisit, basicStaff)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenCriticalPatient_experiencedStaff_shouldPass")
        void testSkillMatchingConstraint_whenCriticalPatient_experiencedStaff_shouldPass() {
            // Given: Experienced critical care staff
            CareStaff criticalCareStaff = testDataFactory.createExperiencedCareStaff("staff1", 
                    5, Set.of("critical-care", "ventilator-management", "cardiac-monitoring"));
            
            // And: Visit for critical patient
            HomecareVisit criticalVisit = testDataFactory.createCriticalPatientVisit("visit1");
            criticalVisit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(criticalVisit, criticalCareStaff)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Boundary Conditions")
    class EdgeCasesAndBoundaryConditions {

        @Test
        @DisplayName("testSkillMatchingConstraint_whenNoSkillsRequired_anyStaff_shouldPass")
        void testSkillMatchingConstraint_whenNoSkillsRequired_anyStaff_shouldPass() {
            // Given: Any care staff
            CareStaff anyStaff = testDataFactory.createBasicCareStaff("staff1");
            
            // And: Visit with no specific skill requirements
            HomecareVisit visit = testDataFactory.createBasicVisit("visit1");
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should not penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, anyStaff)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenVisitUnassigned_shouldNotEvaluate")
        void testSkillMatchingConstraint_whenVisitUnassigned_shouldNotEvaluate() {
            // Given: Care staff with skills
            CareStaff staff = testDataFactory.createCareStaffWithSkills("staff1", 
                    Set.of("nursing"));
            
            // And: Unassigned visit
            HomecareVisit unassignedVisit = testDataFactory.createVisitWithRequiredSkills("visit1", 
                    List.of("nursing"));
            // No assignment: unassignedVisit.setAssignedCareStaffId(null);

            // When/Then: Should not penalize (constraint doesn't apply)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(unassignedVisit, staff)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenStaffHasExpiredCertification_shouldFail")
        void testSkillMatchingConstraint_whenStaffHasExpiredCertification_shouldFail() {
            // Given: Care staff with expired certification
            CareStaff expiredCertStaff = testDataFactory.createCareStaffWithExpiredCertification("staff1", 
                    "CPR", LocalDateTime.now().minusDays(30));
            
            // And: Visit requiring current CPR certification
            HomecareVisit visit = testDataFactory.createVisitWithCertificationRequirement("visit1", 
                    "CPR");
            visit.setAssignedCareStaffId("staff1");

            // When/Then: Should penalize
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(visit, expiredCertStaff)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testSkillMatchingConstraint_whenOverqualifiedStaff_costConsideration_shouldPenalizeSoftly")
        void testSkillMatchingConstraint_whenOverqualifiedStaff_costConsideration_shouldPenalizeSoftly() {
            // Given: Highly qualified expensive staff
            CareStaff overqualifiedStaff = testDataFactory.createOverqualifiedCareStaff("staff1", 
                    "MD", Set.of("medical-doctor", "specialized-procedures"));
            
            // And: Simple basic care visit
            HomecareVisit basicVisit = testDataFactory.createBasicCareVisit("visit1");
            basicVisit.setAssignedCareStaffId("staff1");

            // When/Then: Should have soft penalty for cost optimization
            // Note: This would be tested in soft constraints, not hard constraints
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::careStaffSkillMatch)
                    .given(basicVisit, overqualifiedStaff)
                    .penalizesBy(0); // Hard constraint passes, soft constraint may penalize
        }
    }
}
