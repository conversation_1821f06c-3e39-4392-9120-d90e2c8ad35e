scenario_name: Skill Hierarchy Fallback Matching with Workload Prioritization
test_date: '2025-05-19T23:22:24.500419'
summary:
  total_patients: 3
  total_matches: 5
  validation_success: false
patients:
- patient_id: patient-001
  patient_name: <PERSON>
  required_skills:
  - LPN
  required_certifications:
  - Nursing
  matches:
  - rank: 1
    carestaff_id: carestaff-001
    carestaff_name: <PERSON>
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  - rank: 2
    carestaff_id: carestaff-005
    carestaff_name: <PERSON>
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  validation:
    success: false
    expected_order:
    - carestaff-005
    - carestaff-001
    - carestaff-003
    actual_order:
    - carestaff-001
    - carestaff-005
    missing_carestaff:
    - carestaff-003
- patient_id: patient-002
  patient_name: Alice Brown
  required_skills:
  - Therapy Aide
  required_certifications:
  - Therapy
  matches:
  - rank: 1
    carestaff_id: carestaff-002
    carestaff_name: Robert Johnson
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  - rank: 2
    carestaff_id: carestaff-006
    carestaff_name: Michael Brown
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  validation:
    success: false
    expected_order:
    - carestaff-006
    - carestaff-002
    - carestaff-004
    actual_order:
    - carestaff-002
    - carestaff-006
    missing_carestaff:
    - carestaff-004
- patient_id: patient-003
  patient_name: Bob Wilson
  required_skills:
  - CNA
  required_certifications:
  - Nursing
  matches:
  - rank: 1
    carestaff_id: carestaff-001
    carestaff_name: Jane Smith
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  validation:
    success: false
    expected_order:
    - carestaff-005
    - carestaff-001
    actual_order:
    - carestaff-001
    missing_carestaff:
    - carestaff-005
