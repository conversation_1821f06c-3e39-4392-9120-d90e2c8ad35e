package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.valueobjects.MatchingCriteria;

import java.util.Optional;
import java.util.UUID;

/**
 * Port interface for matching configuration persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface MatchingConfigurationPort {

    /**
     * Find the active matching configuration.
     * 
     * @return Active matching configuration if exists
     */
    Optional<MatchingCriteria> findActiveConfiguration();

    /**
     * Find matching configuration by ID.
     * 
     * @param configId Configuration ID
     * @return Matching configuration if exists
     */
    Optional<MatchingCriteria> findById(UUID configId);
}

