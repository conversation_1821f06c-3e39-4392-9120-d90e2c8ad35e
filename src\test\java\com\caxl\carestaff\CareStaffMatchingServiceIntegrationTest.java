package com.caxl.carestaff;

import com.caxl.carestaff.application.port.in.SuggestMatchesUseCase;
import com.caxl.carestaff.domain.entities.MatchSuggestion;
import com.caxl.carestaff.infrastructure.persistence.entity.MatchingConfigurationEntity;
import com.caxl.carestaff.infrastructure.persistence.entity.ServiceRequestEntity;
import com.caxl.carestaff.infrastructure.persistence.repository.MatchingConfigurationRepository;
import com.caxl.carestaff.infrastructure.persistence.repository.ServiceRequestRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the CareStaff Matching Service.
 * Tests the complete flow from API to database using the hexagonal architecture.
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class CareStaffMatchingServiceIntegrationTest {

    @Autowired
    private SuggestMatchesUseCase suggestMatchesUseCase;

    @Autowired
    private MatchingConfigurationRepository matchingConfigRepository;

    @Autowired
    private ServiceRequestRepository serviceRequestRepository;

    @Test
    void testSuggestMatches_WithValidConfiguration_ShouldReturnSuggestions() {
        // Given: A valid matching configuration
        String criteriaJson = """
            {
              "filters": {
                "requiredSkillsMustMatchAll": false,
                "proximitySearchRadiusKm": 100.0,
                "mustRespectGeoServiceArea": false,
                "mustRespectAvailability": false,
                "mustNotBeBarred": false,
                "minScoreThreshold": 0.0
              },
              "scoring_weights": {
                "skillMatchBonusPerRequiredSkill": 10.0,
                "skillMatchAllBonus": 50.0,
                "proximityKmPenalty": -1.0,
                "geoServiceAreaBonus": 20.0,
                "preferredCareStaffBonus": 15.0,
                "continuityBonusPerRecentVisit": 5.0,
                "languageMatchBonus": 8.0,
                "experienceLevelBonusPerYear": 2.0
              },
              "geography": {
                "staffServiceGeofenceTypes": ["service_area"],
                "geofenceStrictContainmentOnly": false
              },
              "availability": {
                "overlapThresholdMinutes": 1,
                "minTimeBeforeVisitMinutes": 30,
                "minTimeAfterVisitMinutes": 30
              }
            }
            """;

        MatchingConfigurationEntity config = MatchingConfigurationEntity.builder()
                .configId(UUID.randomUUID())
                .configName("test_config")
                .criteriaJson(criteriaJson)
                .isActive(true)
                .createdBy("test")
                .build();

        matchingConfigRepository.save(config);

        // And: A valid service request
        ServiceRequestEntity serviceRequest = ServiceRequestEntity.builder()
                .requestId(UUID.randomUUID())
                .patientId(UUID.randomUUID())
                .status("pending")
                .requiredSkillIds(List.of(UUID.randomUUID()))
                .arrivalWindowStart(LocalDateTime.now().plusDays(1))
                .arrivalWindowEnd(LocalDateTime.now().plusDays(1).plusHours(8))
                .visitDurationMinutes(60)
                .visitType("test_visit")
                .priority(3)
                .workloadPoints(2)
                .build();

        serviceRequestRepository.save(serviceRequest);

        // When: Suggesting matches
        List<MatchSuggestion> suggestions = suggestMatchesUseCase.suggestMatches(serviceRequest.getRequestId());

        // Then: The system should handle the request gracefully
        assertNotNull(suggestions);
        // Note: Suggestions may be empty if no matching carestaff exist in test data
        // The important thing is that the system doesn't throw exceptions
    }

    @Test
    void testSuggestMatches_WithNoActiveConfiguration_ShouldThrowException() {
        // Given: No active configuration exists
        matchingConfigRepository.deleteAll();

        // And: A valid service request
        ServiceRequestEntity serviceRequest = ServiceRequestEntity.builder()
                .requestId(UUID.randomUUID())
                .patientId(UUID.randomUUID())
                .status("pending")
                .requiredSkillIds(List.of(UUID.randomUUID()))
                .arrivalWindowStart(LocalDateTime.now().plusDays(1))
                .arrivalWindowEnd(LocalDateTime.now().plusDays(1).plusHours(8))
                .visitDurationMinutes(60)
                .visitType("test_visit")
                .build();

        serviceRequestRepository.save(serviceRequest);

        // When & Then: Suggesting matches should throw ConfigurationException
        assertThrows(Exception.class, () -> {
            suggestMatchesUseCase.suggestMatches(serviceRequest.getRequestId());
        });
    }

    @Test
    void testSuggestMatches_WithInvalidRequestId_ShouldThrowException() {
        // Given: A valid configuration exists (from sample data initializer)
        // And: An invalid request ID
        UUID invalidRequestId = UUID.randomUUID();

        // When & Then: Suggesting matches should throw ResourceNotFoundException
        assertThrows(Exception.class, () -> {
            suggestMatchesUseCase.suggestMatches(invalidRequestId);
        });
    }
}
