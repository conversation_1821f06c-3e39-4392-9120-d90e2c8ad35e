package com.caxl.assignment.application.services.healthcare;

import ai.timefold.solver.core.api.solver.SolverManager;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.HomecareConstraintProvider;
import com.caxl.assignment.application.services.common.BaseService;
import com.caxl.assignment.application.services.common.ValidationUtils;
import com.caxl.assignment.application.services.common.ServiceResults.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * SchedulingService that orchestrates homecare scheduling
 * with state compliance and geofencing capabilities.
 */
@Service
public class HomecareSchedulingService extends BaseService {

    private final SolverManager<HomecareSchedule, UUID> solverManager;
    private final StateComplianceService stateComplianceService;
    private final GeofencingService geofencingService;

    @Autowired
    public EnhancedHomecareSchedulingService(
            SolverManager<HomecareSchedule, UUID> solverManager,
            StateComplianceService stateComplianceService,
            GeofencingService geofencingService) {
        this.solverManager = solverManager;
        this.stateComplianceService = stateComplianceService;
        this.geofencingService = geofencingService;
    }    /**
     * Schedule visits with comprehensive state compliance and geofencing validation.
     */
    public CompletableFuture<SchedulingResult> scheduleVisits(SchedulingRequest request) {
        return executeAsyncWithErrorHandling("scheduleVisits", () -> {
            logMethodEntry("scheduleVisits", request);
            
            UUID problemId = UUID.randomUUID();
            
            // Pre-validate the request
            ValidationUtils.ValidationResult validation = validateSchedulingRequest(request);
            if (!validation.isValid()) {
                return CompletableFuture.completedFuture(
                    SchedulingResult.failed(validation.getErrors())
                );
            }

            // Prepare the problem for solving
            HomecareSchedule problem = prepareProblem(request);
            
            // Solve asynchronously
            return solverManager.solve(problemId, problem)
                    .thenApply(solution -> postProcessSolution(solution, request))
                    .exceptionally(throwable -> SchedulingResult.failed(List.of("Solving failed: " + throwable.getMessage())));
        }).thenCompose(result -> result.getData().orElse(CompletableFuture.completedFuture(SchedulingResult.failed("Unknown error"))));
    }    /**
     * Validate a scheduling request for compliance and feasibility.
     */
    public ValidationUtils.ValidationResult validateSchedulingRequest(SchedulingRequest request) {
        return ValidationUtils.builder()
                .validateNotNull(request, "Scheduling request")
                .validateNotEmpty(request.getCareStaff(), "Care staff list")
                .validateNotEmpty(request.getVisits(), "Visits list")
                .validateNotEmpty(request.getPatients(), "Patients list")
                .validateCustom(() -> validateStaffCompliance(request))
                .validateCustom(() -> validateGeographicCoverage(request))
                .build();
    }
    
    /**
     * Validate staff compliance across all states.
     */
    private String validateStaffCompliance(SchedulingRequest request) {
        for (CareStaff staff : request.getCareStaff()) {
            for (String state : request.getOperatingStates()) {
                StateComplianceService.ComplianceValidationResult compliance = 
                    stateComplianceService.validateStaffCompliance(staff, state);
                
                if (!compliance.isCompliant()) {
                    return String.format("Staff %s not compliant in state %s: %s", 
                            staff.getId(), state, compliance.getViolations());
                }
            }
        }
        return null; // Valid
    }
    
    /**
     * Validate geographic coverage for all visits.
     */
    private String validateGeographicCoverage(SchedulingRequest request) {
        for (HomecareVisit visit : request.getVisits()) {
            HomePatient patient = findEntityById(request.getPatients(), visit.getPatientId(), HomePatient::getPatientId);
            if (patient != null) {
                List<CareStaff> eligibleStaff = geofencingService.findEligibleStaffForPatient(
                    request.getCareStaff(), patient);
                
                if (eligibleStaff.isEmpty()) {
                    return String.format("No eligible staff found for patient %s at %s", 
                            patient.getPatientId(), patient.getHomeAddress());
                }
            }
        }
        return null; // Valid
    }

    /**
     * Get scheduling recommendations based on historical data and patterns.
     */
    public SchedulingRecommendations getSchedulingRecommendations(SchedulingRequest request) {
        Map<String, Integer> stateWorkloadDistribution = calculateStateWorkloadDistribution(request);
        Map<String, Double> zoneUtilizationRates = calculateZoneUtilizationRates(request);
        List<String> optimizationSuggestions = generateOptimizationSuggestions(request);
        
        return SchedulingRecommendations.builder()
                .stateWorkloadDistribution(stateWorkloadDistribution)
                .zoneUtilizationRates(zoneUtilizationRates)
                .optimizationSuggestions(optimizationSuggestions)
                .recommendationDate(LocalDateTime.now())
                .build();
    }

    /**
     * Reschedule visits due to emergency or staff unavailability.
     */
    public CompletableFuture<SchedulingResult> rescheduleVisits(RescheduleRequest request) {
        // Create a new scheduling request with updated constraints
        SchedulingRequest newRequest = SchedulingRequest.builder()
                .careStaff(request.getAvailableStaff())
                .visits(request.getVisitsToReschedule())
                .patients(request.getPatients())
                .operatingStates(request.getOperatingStates())
                .constraints(request.getEmergencyConstraints())
                .build();
        
        return scheduleVisits(newRequest);
    }

    /**
     * Prepare the problem for Timefold solving.
     */
    private HomecareSchedule prepareProblem(SchedulingRequest request) {
        return HomecareSchedule.builder()
                .scheduleId(UUID.randomUUID().toString())
                .careStaff(request.getCareStaff())
                .visits(request.getVisits())
                .patients(request.getPatients())
                .organizations(request.getOrganizations())
                .teams(request.getTeams())
                .schedulingDate(LocalDateTime.now())
                .build();
    }

    /**
     * Post-process the solution and validate final assignments.
     */
    private SchedulingResult postProcessSolution(HomecareSchedule solution, SchedulingRequest request) {
        List<String> warnings = new java.util.ArrayList<>();
          // Validate state compliance of final assignments
        for (HomecareVisit visit : solution.getVisits()) {
            if (visit.getAssignedCareStaffId() != null) {
                CareStaff assignedStaff = findEntityById(solution.getCareStaff(), visit.getAssignedCareStaffId(), CareStaff::getId);
                HomePatient patient = findEntityById(solution.getPatients(), visit.getPatientId(), HomePatient::getPatientId);
                  if (assignedStaff != null && patient != null) {
                    // Check state compliance
                    StateComplianceService.ComplianceValidationResult compliance = 
                        stateComplianceService.validateStaffCompliance(assignedStaff, patient.getState());
                    
                    if (!compliance.isCompliant()) {
                        warnings.addAll(compliance.getViolations());
                    }
                    
                    // Check geofencing compliance
                    if (!geofencingService.canServePatient(assignedStaff, patient)) {
                        warnings.add(String.format("Staff %s assigned outside operating zone for patient %s", 
                                assignedStaff.getId(), patient.getPatientId()));
                    }
                }
            }
        }
        
        return SchedulingResult.builder()
                .success(true)
                .schedule(solution)
                .warnings(warnings)
                .solutionDate(LocalDateTime.now())
                .build();    }

    private Map<String, Integer> calculateStateWorkloadDistribution(SchedulingRequest request) {
        return request.getVisits().stream()
                .map(visit -> findEntityById(request.getPatients(), visit.getPatientId(), HomePatient::getPatientId))
                .filter(patient -> patient != null)
                .collect(java.util.stream.Collectors.groupingBy(
                        HomePatient::getState,
                        java.util.stream.Collectors.collectingAndThen(java.util.stream.Collectors.counting(), Math::toIntExact)
                ));
    }

    private Map<String, Double> calculateZoneUtilizationRates(SchedulingRequest request) {
        // Simplified calculation - in production, this would be more sophisticated
        return Map.of(
                "urban", 0.85,
                "suburban", 0.72,
                "rural", 0.58
        );
    }

    private List<String> generateOptimizationSuggestions(SchedulingRequest request) {
        List<String> suggestions = new java.util.ArrayList<>();
        
        // Analyze workload distribution
        Map<String, Integer> workloadByState = calculateStateWorkloadDistribution(request);
        if (workloadByState.values().stream().anyMatch(count -> count > 10)) {
            suggestions.add("Consider hiring additional staff in high-demand states");
        }
          // Analyze geographic coverage
        long uncoveredPatients = request.getVisits().stream()
                .map(visit -> findEntityById(request.getPatients(), visit.getPatientId(), HomePatient::getPatientId))
                .filter(patient -> patient != null)
                .filter(patient -> geofencingService.findEligibleStaffForPatient(
                        request.getCareStaff(), patient).isEmpty())
                .count();
        
        if (uncoveredPatients > 0) {
            suggestions.add(String.format("Expand geographic coverage - %d patients lack nearby staff", 
                    uncoveredPatients));
        }
        
        return suggestions;
    }

    // === REQUEST/RESPONSE CLASSES ===

    public static class SchedulingRequest {
        private final List<CareStaff> careStaff;
        private final List<HomecareVisit> visits;
        private final List<HomePatient> patients;
        private final List<Organization> organizations;
        private final List<CareTeam> teams;
        private final List<String> operatingStates;
        private final Map<String, Object> constraints;

        public SchedulingRequest(List<CareStaff> careStaff, List<HomecareVisit> visits, 
                               List<HomePatient> patients, List<Organization> organizations,
                               List<CareTeam> teams, List<String> operatingStates,
                               Map<String, Object> constraints) {
            this.careStaff = careStaff;
            this.visits = visits;
            this.patients = patients;
            this.organizations = organizations;
            this.teams = teams;
            this.operatingStates = operatingStates;
            this.constraints = constraints;
        }

        public static SchedulingRequestBuilder builder() {
            return new SchedulingRequestBuilder();
        }

        // Getters
        public List<CareStaff> getCareStaff() { return careStaff; }
        public List<HomecareVisit> getVisits() { return visits; }
        public List<HomePatient> getPatients() { return patients; }
        public List<Organization> getOrganizations() { return organizations; }
        public List<CareTeam> getTeams() { return teams; }
        public List<String> getOperatingStates() { return operatingStates; }
        public Map<String, Object> getConstraints() { return constraints; }

        public static class SchedulingRequestBuilder {
            private List<CareStaff> careStaff;
            private List<HomecareVisit> visits;
            private List<HomePatient> patients;
            private List<Organization> organizations;
            private List<CareTeam> teams;
            private List<String> operatingStates;
            private Map<String, Object> constraints;

            public SchedulingRequestBuilder careStaff(List<CareStaff> careStaff) {
                this.careStaff = careStaff;
                return this;
            }

            public SchedulingRequestBuilder visits(List<HomecareVisit> visits) {
                this.visits = visits;
                return this;
            }

            public SchedulingRequestBuilder patients(List<HomePatient> patients) {
                this.patients = patients;
                return this;
            }

            public SchedulingRequestBuilder organizations(List<Organization> organizations) {
                this.organizations = organizations;
                return this;
            }

            public SchedulingRequestBuilder teams(List<CareTeam> teams) {
                this.teams = teams;
                return this;
            }

            public SchedulingRequestBuilder operatingStates(List<String> operatingStates) {
                this.operatingStates = operatingStates;
                return this;
            }

            public SchedulingRequestBuilder constraints(Map<String, Object> constraints) {
                this.constraints = constraints;
                return this;
            }

            public SchedulingRequest build() {
                return new SchedulingRequest(careStaff, visits, patients, organizations, teams, operatingStates, constraints);
            }
        }
    }

    public static class SchedulingResult {
        private final boolean success;
        private final HomecareSchedule schedule;
        private final List<String> errors;
        private final List<String> warnings;
        private final LocalDateTime solutionDate;

        public SchedulingResult(boolean success, HomecareSchedule schedule, List<String> errors, 
                              List<String> warnings, LocalDateTime solutionDate) {
            this.success = success;
            this.schedule = schedule;
            this.errors = errors;
            this.warnings = warnings;
            this.solutionDate = solutionDate;
        }

        public static SchedulingResult failed(List<String> errors) {
            return new SchedulingResult(false, null, errors, List.of(), LocalDateTime.now());
        }

        public static SchedulingResultBuilder builder() {
            return new SchedulingResultBuilder();
        }

        // Getters
        public boolean isSuccess() { return success; }
        public HomecareSchedule getSchedule() { return schedule; }
        public List<String> getErrors() { return errors; }
        public List<String> getWarnings() { return warnings; }
        public LocalDateTime getSolutionDate() { return solutionDate; }

        public static class SchedulingResultBuilder {
            private boolean success;
            private HomecareSchedule schedule;
            private List<String> errors;
            private List<String> warnings;
            private LocalDateTime solutionDate;

            public SchedulingResultBuilder success(boolean success) {
                this.success = success;
                return this;
            }

            public SchedulingResultBuilder schedule(HomecareSchedule schedule) {
                this.schedule = schedule;
                return this;
            }

            public SchedulingResultBuilder errors(List<String> errors) {
                this.errors = errors;
                return this;
            }

            public SchedulingResultBuilder warnings(List<String> warnings) {
                this.warnings = warnings;
                return this;
            }

            public SchedulingResultBuilder solutionDate(LocalDateTime solutionDate) {
                this.solutionDate = solutionDate;
                return this;
            }

            public SchedulingResult build() {
                return new SchedulingResult(success, schedule, errors, warnings, solutionDate);
            }
        }
    }

    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;

        public ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors;
        }

        public boolean isValid() { return valid; }
        public List<String> getErrors() { return errors; }
    }

    public static class SchedulingRecommendations {
        private final Map<String, Integer> stateWorkloadDistribution;
        private final Map<String, Double> zoneUtilizationRates;
        private final List<String> optimizationSuggestions;
        private final LocalDateTime recommendationDate;

        public SchedulingRecommendations(Map<String, Integer> stateWorkloadDistribution,
                                       Map<String, Double> zoneUtilizationRates,
                                       List<String> optimizationSuggestions,
                                       LocalDateTime recommendationDate) {
            this.stateWorkloadDistribution = stateWorkloadDistribution;
            this.zoneUtilizationRates = zoneUtilizationRates;
            this.optimizationSuggestions = optimizationSuggestions;
            this.recommendationDate = recommendationDate;
        }

        public static SchedulingRecommendationsBuilder builder() {
            return new SchedulingRecommendationsBuilder();
        }

        // Getters
        public Map<String, Integer> getStateWorkloadDistribution() { return stateWorkloadDistribution; }
        public Map<String, Double> getZoneUtilizationRates() { return zoneUtilizationRates; }
        public List<String> getOptimizationSuggestions() { return optimizationSuggestions; }
        public LocalDateTime getRecommendationDate() { return recommendationDate; }

        public static class SchedulingRecommendationsBuilder {
            private Map<String, Integer> stateWorkloadDistribution;
            private Map<String, Double> zoneUtilizationRates;
            private List<String> optimizationSuggestions;
            private LocalDateTime recommendationDate;

            public SchedulingRecommendationsBuilder stateWorkloadDistribution(Map<String, Integer> stateWorkloadDistribution) {
                this.stateWorkloadDistribution = stateWorkloadDistribution;
                return this;
            }

            public SchedulingRecommendationsBuilder zoneUtilizationRates(Map<String, Double> zoneUtilizationRates) {
                this.zoneUtilizationRates = zoneUtilizationRates;
                return this;
            }

            public SchedulingRecommendationsBuilder optimizationSuggestions(List<String> optimizationSuggestions) {
                this.optimizationSuggestions = optimizationSuggestions;
                return this;
            }

            public SchedulingRecommendationsBuilder recommendationDate(LocalDateTime recommendationDate) {
                this.recommendationDate = recommendationDate;
                return this;
            }

            public SchedulingRecommendations build() {
                return new SchedulingRecommendations(stateWorkloadDistribution, zoneUtilizationRates, optimizationSuggestions, recommendationDate);
            }
        }
    }

    public static class RescheduleRequest {
        private final List<CareStaff> availableStaff;
        private final List<HomecareVisit> visitsToReschedule;
        private final List<HomePatient> patients;
        private final List<String> operatingStates;
        private final Map<String, Object> emergencyConstraints;

        public RescheduleRequest(List<CareStaff> availableStaff, List<HomecareVisit> visitsToReschedule,
                               List<HomePatient> patients, List<String> operatingStates,
                               Map<String, Object> emergencyConstraints) {
            this.availableStaff = availableStaff;
            this.visitsToReschedule = visitsToReschedule;
            this.patients = patients;
            this.operatingStates = operatingStates;
            this.emergencyConstraints = emergencyConstraints;
        }

        // Getters
        public List<CareStaff> getAvailableStaff() { return availableStaff; }
        public List<HomecareVisit> getVisitsToReschedule() { return visitsToReschedule; }
        public List<HomePatient> getPatients() { return patients; }
        public List<String> getOperatingStates() { return operatingStates; }
        public Map<String, Object> getEmergencyConstraints() { return emergencyConstraints; }
    }
}
