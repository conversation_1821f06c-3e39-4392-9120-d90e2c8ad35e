package com.caxl.assignment;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

/**
 * Main application class for the CAXL OptaPlanner Assignment Service.
 * 
 * A sophisticated clinician-patient matching system that combines business rule evaluation 
 * with mathematical optimization using OptaPlanner constraint solver.
 */
@SpringBootApplication
@ConfigurationPropertiesScan
public class CaxlAssignmentServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CaxlAssignmentServiceApplication.class, args);
    }
}
