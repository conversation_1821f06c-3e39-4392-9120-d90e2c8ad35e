# Scheduling Constraint Provider Documentation

## Overview

The `SchedulingConstraintProvider` is a comprehensive constraint provider for patient-clinician scheduling optimization using Timefold Solver. It implements both hard constraints (feasibility requirements) and soft constraints (optimization goals) to create optimal schedules for healthcare scenarios.

## Architecture

### Package Structure
```
com.caxl.assignment.domain.constraints.scheduling
├── SchedulingConstraintProvider.java    # Main constraint provider
```

### Dependencies
- **Timefold Solver Core**: For constraint stream API
- **Domain Models**: Patient, Clinician, CareStaff, PatientAssignment
- **Spring Framework**: For component management

## Constraint Categories

### Hard Constraints (Feasibility)

These constraints must be satisfied for a solution to be considered feasible:

#### 1. Clinician Availability Constraint
- **Purpose**: Ensures clinicians are only assigned during their available time slots
- **Implementation**: Checks if assigned time slot falls within clinician's availability
- **Penalty**: 1 hard score point per violation

#### 2. Skill Requirement Constraint
- **Purpose**: Ensures clinicians have all required skills for patient care
- **Implementation**: Validates that clinician skills contain all patient required skills
- **Penalty**: 1 hard score point per violation

#### 3. Time Slot Capacity Constraint
- **Purpose**: Prevents overbooking of time slots
- **Implementation**: Counts assignments per time slot and compares to capacity
- **Penalty**: 1 hard score point per excess assignment

#### 4. Clinician Workload Constraint
- **Purpose**: Prevents clinicians from exceeding daily appointment limits
- **Implementation**: Counts daily assignments per clinician
- **Penalty**: 1 hard score point per excess assignment

#### 5. One Assignment Per Patient Constraint
- **Purpose**: Ensures each patient has exactly one assignment
- **Implementation**: Groups by patient and penalizes multiple assignments
- **Penalty**: 1 hard score point per duplicate assignment

### Soft Constraints (Optimization)

These constraints improve solution quality but are not mandatory:

#### 1. Balance Workload Constraint
- **Purpose**: Distributes assignments evenly among clinicians
- **Implementation**: Calculates deviation from ideal workload (6 assignments)
- **Penalty**: Variable based on imbalance severity

#### 2. Minimize Travel Time Constraint
- **Purpose**: Reduces travel between consecutive assignments
- **Implementation**: Penalizes assignments in different geographic zones
- **Penalty**: 100 points for different zones, 0 for same zone

#### 3. Continuity of Care Constraint
- **Purpose**: Assigns patients to their preferred clinicians
- **Implementation**: Rewards assignments matching patient preferences
- **Reward**: 100 points per preferred match

#### 4. Priority Optimization Constraint
- **Purpose**: Prioritizes high-priority patients
- **Implementation**: Rewards based on patient priority level
- **Reward**: 1000 (priority 1) to 50 (priority 5) points

#### 5. Utilization Optimization Constraint
- **Purpose**: Maximizes clinician utilization
- **Implementation**: Rewards each assignment
- **Reward**: 10 points per assignment

#### 6. Geographic Cohesion Constraint
- **Purpose**: Keeps clinician assignments within same geographic areas
- **Implementation**: Rewards assignments in same geographic zone
- **Reward**: 50 points per same-zone pair

## Implementation Details

### Key Methods

#### Availability Checking
```java
private boolean isClinicianAvailable(PatientAssignment assignment)
```
- Validates time slot against clinician availability
- Returns false if clinician or time slot is null

#### Skill Validation
```java
private boolean hasRequiredSkills(PatientAssignment assignment)
```
- Compares clinician skills with patient requirements
- Uses Set operations for efficient validation

#### Travel Calculation
```java
private int calculateTravelPenalty(PatientAssignment assignment1, PatientAssignment assignment2)
```
- Simplified geographic zone comparison
- Extensible for real distance calculations

### Configuration

#### Priority Weights
- Priority 1 (Highest): 1000 points
- Priority 2: 500 points
- Priority 3: 200 points
- Priority 4: 100 points
- Priority 5 (Lowest): 50 points

#### Workload Targets
- Ideal assignments per clinician: 6
- Penalty increases with deviation from ideal

## Usage Examples

### Basic Integration
```java
@Configuration
public class SolverConfig {
    
    @Bean
    public SolverFactory<SchedulingSolution> solverFactory() {
        return SolverFactory.create(SolverConfig.createFromXmlResource(
            "com/caxl/assignment/solverConfig.xml"));
    }
}
```

### Solver Configuration XML
```xml
<solver xmlns="https://timefold.ai/xsd/solver">
    <solutionClass>com.caxl.assignment.domain.scheduling.SchedulingSolution</solutionClass>
    <entityClass>com.caxl.assignment.domain.scheduling.PatientAssignment</entityClass>
    
    <scoreDirectorFactory>
        <constraintProviderClass>
            com.caxl.assignment.domain.constraints.scheduling.SchedulingConstraintProvider
        </constraintProviderClass>
    </scoreDirectorFactory>
    
    <termination>
        <secondsSpentLimit>30</secondsSpentLimit>
    </termination>
</solver>
```

## Performance Considerations

### Optimization Tips
1. **Constraint Ordering**: Hard constraints are evaluated first for early termination
2. **Efficient Filtering**: Use null checks to avoid unnecessary processing
3. **Set Operations**: Use HashSet for skill comparisons
4. **Grouping**: Leverage Timefold's groupBy for aggregations

### Scalability
- Designed for 100-1000 assignments
- Linear complexity for most constraints
- Quadratic complexity only for travel optimization

## Extension Points

### Adding New Constraints
1. Create new constraint method following naming convention
2. Add to `defineConstraints()` array
3. Implement helper methods as needed
4. Update documentation

### Custom Scoring
```java
private Constraint customConstraint(ConstraintFactory factory) {
    return factory.forEach(PatientAssignment.class)
            .filter(/* your condition */)
            .penalize(HardSoftScore.ONE_SOFT, /* your penalty function */)
            .asConstraint("Custom constraint name");
}
```

## Testing

### Unit Tests
- Test individual constraint methods
- Verify penalty calculations
- Validate helper method logic

### Integration Tests
- Test complete constraint provider
- Verify score calculations
- Performance benchmarking

## Troubleshooting

### Common Issues
1. **Null Pointer Exceptions**: Add null checks in helper methods
2. **Performance Issues**: Review constraint complexity
3. **Unexpected Scores**: Verify penalty/reward calculations

### Debugging
- Enable Timefold logging for constraint evaluation
- Use score explanation for detailed analysis
- Monitor constraint evaluation times

## Future Enhancements

### Planned Features
1. **Dynamic Constraint Weights**: Runtime configuration
2. **Advanced Travel Calculation**: Real distance/time APIs
3. **Shift-based Scheduling**: Multi-day optimization
4. **Resource Constraints**: Equipment and room availability
5. **Compliance Rules**: State-specific healthcare regulations
