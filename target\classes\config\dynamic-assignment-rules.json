{"version": "1.0.0", "description": "Dynamic Multi-Level Clinician Assignment Rules Configuration", "metadata": {"created_by": "Assignment System", "created_date": "2024-01-01", "last_modified": "2024-01-01"}, "rules": [{"rule_id": "L1_SKILL_MATCH", "name": "Required Skills Match", "description": "Clinician must have all required skills for the patient visit", "level": "level_1", "type": "hard", "field": "skills", "operator": "contains_all", "value": "required_skills", "target_entity": "clinician", "weight": 1000.0, "enabled": true, "tags": ["skills", "mandatory", "level1"]}, {"rule_id": "L1_GEOGRAPHIC_ZONE", "name": "Geographic Service Area", "description": "Patient location must be within clinician's service area", "level": "level_1", "type": "hard", "field": "operating_zones", "operator": "contains_any", "value": "service_area_code", "target_entity": "clinician", "weight": 1000.0, "enabled": true, "tags": ["geography", "mandatory", "level1"]}, {"rule_id": "L1_AVAILABILITY", "name": "Clinician Availability", "description": "Clinician must be available at requested service time", "level": "level_1", "type": "hard", "field": "available_slots", "operator": "is_available_at", "value": "service_date_time", "target_entity": "clinician", "weight": 1000.0, "enabled": true, "tags": ["availability", "mandatory", "level1"]}, {"rule_id": "L1_ACTIVE_STATUS", "name": "Active Status", "description": "Clinician must be active and not suspended", "level": "level_1", "type": "hard", "field": "active", "operator": "equals", "value": true, "target_entity": "clinician", "weight": 1000.0, "enabled": true, "tags": ["status", "mandatory", "level1"]}, {"rule_id": "L2_DAILY_WORKLOAD", "name": "Daily Workload Limit", "description": "Clinician cannot exceed daily workload capacity", "level": "level_2", "type": "hard", "field": "current_workload", "operator": "has_capacity_for", "value": "max_daily_workload", "target_entity": "clinician", "weight": 500.0, "enabled": true, "tags": ["workload", "capacity", "level2"]}, {"rule_id": "L2_VISIT_COUNT", "name": "Maximum Daily Visits", "description": "Clinician cannot exceed maximum number of daily visits", "level": "level_2", "type": "hard", "field": "daily_visit_count", "operator": "less_than", "value": "max_daily_appointments", "target_entity": "clinician", "weight": 500.0, "enabled": true, "tags": ["visits", "capacity", "level2"]}, {"rule_id": "L3_SKILL_SUBSTITUTION", "name": "Skill Substitution", "description": "Allow alternative skills for Level 3 assignments", "level": "level_3", "type": "soft", "field": "skills", "operator": "contains_any", "value": "alternative_skills", "target_entity": "clinician", "weight": 100.0, "enabled": true, "tags": ["skills", "relaxation", "level3"]}, {"rule_id": "L3_EXTENDED_RADIUS", "name": "Extended Service Radius", "description": "Allow extended geographic radius for Level 3 assignments", "level": "level_3", "type": "soft", "field": "patient_location", "operator": "within_distance", "value": 50.0, "target_entity": "patient", "weight": 80.0, "enabled": true, "tags": ["geography", "relaxation", "level3"]}, {"rule_id": "SOFT_CONTINUITY_CARE", "name": "Continuity of Care", "description": "Prefer clinicians with previous patient history", "level": "level_1", "type": "soft", "field": "patient_history", "operator": "contains_any", "value": "clinician_id", "target_entity": "clinician", "weight": 400.0, "enabled": true, "tags": ["continuity", "optimization", "soft"]}, {"rule_id": "SOFT_PREFERRED_CLINICIAN", "name": "Patient Preferred Clinician", "description": "Assign patient to their preferred clinician when possible", "level": "level_1", "type": "soft", "field": "preferred_clinician_ids", "operator": "in", "value": "clinician_id", "target_entity": "patient", "weight": 350.0, "enabled": true, "tags": ["preference", "optimization", "soft"]}, {"rule_id": "SOFT_PRIORITY_BOOST", "name": "High Priority Visi<PERSON>", "description": "Boost score for high priority patient visits", "level": "level_1", "type": "soft", "field": "priority_level", "operator": "equals", "value": 1, "target_entity": "patient", "weight": 500.0, "enabled": true, "tags": ["priority", "optimization", "soft"]}]}