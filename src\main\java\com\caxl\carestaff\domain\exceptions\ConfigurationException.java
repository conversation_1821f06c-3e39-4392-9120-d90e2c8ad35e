package com.caxl.carestaff.domain.exceptions;

/**
 * Exception thrown when there are configuration-related issues.
 */
public class ConfigurationException extends RuntimeException {

    public ConfigurationException(String message) {
        super(message);
    }

    public ConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }

    public static ConfigurationException noActiveConfiguration() {
        return new ConfigurationException("No active matching configuration found");
    }

    public static ConfigurationException invalidConfiguration(String reason) {
        return new ConfigurationException("Invalid matching configuration: " + reason);
    }
}
