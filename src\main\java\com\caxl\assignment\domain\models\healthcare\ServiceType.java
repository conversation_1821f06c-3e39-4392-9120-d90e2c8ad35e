package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * ServiceType represents different types of homecare services.
 * Each service has specific licensing and certification requirements.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceType {

    @NotBlank(message = "Service type ID is required")
    @JsonProperty("service_type_id")
    private String serviceTypeId;

    @NotBlank(message = "Service name is required")
    @JsonProperty("service_name")
    private String serviceName;

    @JsonProperty("service_description")
    private String serviceDescription;

    @Valid
    @NotNull(message = "Required licenses are required")
    @JsonProperty("required_licenses")
    private Set<LicenseType> requiredLicenses;

    @Valid
    @JsonProperty("required_certifications")
    private Set<CertificationType> requiredCertifications;

    @NotNull(message = "Service category is required")
    @JsonProperty("service_category")
    private ServiceCategory serviceCategory;

    @JsonProperty("complexity_level")
    private ComplexityLevel complexityLevel;

    @JsonProperty("supervision_required")
    private boolean supervisionRequired;

    @JsonProperty("typical_duration_minutes")
    private Integer typicalDurationMinutes;

    @JsonProperty("billable_service")
    private boolean billableService;

    @JsonProperty("insurance_codes")
    private List<String> insuranceCodes;

    /**
     * License type required for specific services.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LicenseType {

        @NotBlank(message = "License type ID is required")
        @JsonProperty("license_type_id")
        private String licenseTypeId;

        @NotBlank(message = "License name is required")
        @JsonProperty("license_name")
        private String licenseName;

        @NotBlank(message = "Issuing state is required")
        @JsonProperty("issuing_state")
        private String issuingState;

        @JsonProperty("license_level")
        private LicenseLevel licenseLevel;

        @JsonProperty("requires_renewal")
        private boolean requiresRenewal;

        @JsonProperty("renewal_period_months")
        private Integer renewalPeriodMonths;

        @JsonProperty("continuing_education_hours")
        private Integer continuingEducationHours;
    }

    /**
     * Certification type for specialized care.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CertificationType {

        @NotBlank(message = "Certification type ID is required")
        @JsonProperty("certification_type_id")
        private String certificationTypeId;

        @NotBlank(message = "Certification name is required")
        @JsonProperty("certification_name")
        private String certificationName;

        @JsonProperty("issuing_organization")
        private String issuingOrganization;

        @JsonProperty("certification_level")
        private CertificationLevel certificationLevel;

        @JsonProperty("requires_renewal")
        private boolean requiresRenewal;

        @JsonProperty("renewal_period_months")
        private Integer renewalPeriodMonths;
    }

    /**
     * Service categories for homecare.
     */
    public enum ServiceCategory {
        SKILLED_NURSING,
        PHYSICAL_THERAPY,
        OCCUPATIONAL_THERAPY,
        SPEECH_THERAPY,
        MEDICAL_SOCIAL_WORK,
        HOME_HEALTH_AIDE,
        PERSONAL_CARE,
        COMPANIONSHIP,
        MEDICATION_MANAGEMENT,
        WOUND_CARE,
        CHRONIC_DISEASE_MANAGEMENT,
        PALLIATIVE_CARE,
        RESPITE_CARE,
        BEHAVIORAL_HEALTH
    }

    /**
     * Service complexity levels.
     */
    public enum ComplexityLevel {
        BASIC,
        INTERMEDIATE,
        ADVANCED,
        COMPLEX,
        CRITICAL
    }

    /**
     * License levels in healthcare.
     */
    public enum LicenseLevel {
        AIDE,
        ASSISTANT,
        TECHNICIAN,
        LICENSED_PRACTICAL_NURSE,
        REGISTERED_NURSE,
        NURSE_PRACTITIONER,
        PHYSICIAN_ASSISTANT,
        PHYSICIAN,
        THERAPIST,
        SOCIAL_WORKER
    }

    /**
     * Certification levels.
     */
    public enum CertificationLevel {
        BASIC,
        INTERMEDIATE,
        ADVANCED,
        SPECIALTY,
        EXPERT
    }

    /**
     * Check if a care staff member is qualified to provide this service.
     */
    public boolean isQualifiedStaff(CareStaff careStaff) {
        // Check if care staff has all required licenses
        for (LicenseType requiredLicense : requiredLicenses) {
            boolean hasLicense = careStaff.getStateLicensing().getLicenses().stream()
                    .anyMatch(license -> license.getLicenseTypeId().equals(requiredLicense.getLicenseTypeId()));
            if (!hasLicense) {
                return false;
            }
        }

        // Check if care staff has all required certifications
        if (requiredCertifications != null) {
            for (CertificationType requiredCert : requiredCertifications) {
                boolean hasCertification = careStaff.getCareSpecializations().getCertifications().stream()
                        .anyMatch(cert -> cert.getCertificationTypeId().equals(requiredCert.getCertificationTypeId()));
                if (!hasCertification) {
                    return false;
                }
            }
        }

        return true;
    }
}
