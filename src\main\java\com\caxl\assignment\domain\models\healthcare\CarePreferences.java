package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Care preferences for patients.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CarePreferences {

    @JsonProperty("preferred_gender")
    private String preferredGender;

    @Valid
    @JsonProperty("preferred_languages")
    private List<String> preferredLanguages;

    @Valid
    @JsonProperty("special_requests")
    private List<String> specialRequests;

    @JsonProperty("preferred_care_staff_ids")
    private List<String> preferredCareStaffIds;

    @JsonProperty("excluded_care_staff_ids")
    private List<String> excludedCareStaffIds;

    @JsonProperty("communication_preferences")
    private String communicationPreferences;

    @JsonProperty("cultural_considerations")
    private String culturalConsiderations;

    @JsonProperty("preferred_caregiver_id")
    private String preferredCaregiverId;

    @JsonProperty("family_member_id")
    private String familyMemberId;

    @JsonProperty("priority_level")
    private String priorityLevel;

    @Builder.Default
    @JsonProperty("is_new_patient")
    private boolean isNewPatient = false;
}
