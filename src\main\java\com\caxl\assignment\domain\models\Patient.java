package com.caxl.assignment.domain.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * Patient domain model representing a patient requiring care services.
 * Equivalent to the Python Patient model with validation and OptaPlanner annotations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Patient {
    
    @NotBlank(message = "Patient ID is required")
    private String id;
    
    @NotBlank(message = "Patient name is required")
    private String name;
    
    private String mrn; // Medical Record Number
    
    @NotNull(message = "Location is required")
    @Valid
    private Location location;
    
    @NotEmpty(message = "Required skills cannot be empty")
    private List<String> requiredSkills;
    
    @NotEmpty(message = "Required certifications cannot be empty")
    private List<String> requiredCertifications;
    
    @NotEmpty(message = "Required slots cannot be empty")
    @Valid
    private List<TimeSlot> requiredSlots;
    
    @NotNull(message = "Preferences are required")
    @Valid
    private PatientPreferences preferences;
    
    @NotNull(message = "Profile is required")
    @Valid
    private PatientProfile profile;
    
    @NotBlank(message = "Visit type is required")
    private String visitType;
    
    @Min(value = 1, message = "Visit priority must be at least 1")
    @Max(value = 5, message = "Visit priority must be at most 5")
    @Builder.Default
    private int visitPriority = 1;
    
    @Min(value = 1, message = "Workload points must be at least 1")
    @Builder.Default
    private int workloadPoints = 1;
    
    /**
     * Location information for the patient.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location {
        
        @NotBlank(message = "Postal code is required")
        @JsonProperty("postal_code")
        private String postalCode;
        
        private String address;
        private String city;
        private String state;
        
        private Map<String, Double> coordinates; // latitude, longitude
    }
    
    /**
     * Time slot representing when care is needed.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TimeSlot {
        
        @NotNull(message = "Date is required")
        private LocalDate date;
        
        @NotNull(message = "Start time is required")
        @JsonProperty("start_time")
        private LocalTime startTime;
        
        @NotNull(message = "End time is required")
        @JsonProperty("end_time")
        private LocalTime endTime;
        
        @Min(value = 15, message = "Duration must be at least 15 minutes")
        @JsonProperty("duration_minutes")
        private int durationMinutes;
        
        /**
         * Calculate duration from start and end times if not provided.
         */
        public int calculateDuration() {
            if (startTime != null && endTime != null) {
                return (int) java.time.Duration.between(startTime, endTime).toMinutes();
            }
            return durationMinutes;
        }
    }
    
    /**
     * Patient preferences for care staff selection.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PatientPreferences {
        
        @Builder.Default
        @JsonProperty("preferred_carestaff_ids")
        private List<String> preferredCarestaffIds = List.of();
        
        @Builder.Default
        @JsonProperty("excluded_carestaff_ids")
        private List<String> excludedCarestaffIds = List.of();
        
        @Builder.Default
        @JsonProperty("previous_carestaff_ids")
        private List<String> previousCarestaffIds = List.of();
        
        @JsonProperty("preferred_carestaff_gender")
        private String preferredCarestaffGender;
        
        @Builder.Default
        @JsonProperty("preferred_languages")
        private List<String> preferredLanguages = List.of();
    }
    
    /**
     * Patient profile containing care complexity and requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PatientProfile {
        
        @Min(value = 0, message = "Minimum carestaff experience cannot be negative")
        @JsonProperty("min_carestaff_experience")
        @Builder.Default
        private int minCarestaffExperience = 0;
        
        @Min(value = 1, message = "Complexity level must be at least 1")
        @Max(value = 5, message = "Complexity level must be at most 5")
        @JsonProperty("complexity_level")
        @Builder.Default
        private int complexityLevel = 1;
        
        @Min(value = 1, message = "Acuity level must be at least 1")
        @Max(value = 5, message = "Acuity level must be at most 5")
        @JsonProperty("acuity_level")
        @Builder.Default
        private int acuityLevel = 1;
    }
}
