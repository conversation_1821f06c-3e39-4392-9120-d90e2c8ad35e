package com.caxl.assignment.api.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.Clinician;

import java.util.List;
import java.util.Map;

/**
 * DTO for creating assignments.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateAssignmentRequest {

    @NotEmpty(message = "Patients list cannot be empty")
    @Valid
    private List<Patient> patients;

    @NotEmpty(message = "Clinicians list cannot be empty")
    @Valid
    private List<Clinician> clinicians;

    @JsonProperty("relaxation_details")
    private Map<String, Map<String, Object>> relaxationDetails;

    @JsonProperty("optimization_preferences")
    private OptimizationPreferences optimizationPreferences;

    /**
     * Optimization preferences.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimizationPreferences {

        @JsonProperty("time_limit_seconds")
        @Builder.Default
        private int timeLimitSeconds = 30;

        @JsonProperty("unimproved_time_limit_seconds")
        @Builder.Default
        private int unimprovedTimeLimitSeconds = 10;

        @JsonProperty("optimization_strategy")
        @Builder.Default
        private String optimizationStrategy = "DEFAULT";
    }
}
