package com.caxl.assignment.application.services.common;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * Common validation utilities used across service layers to eliminate
 * duplicate validation logic.
 */
public class ValidationUtils {
    
    /**
     * ValidationResult class to standardize validation responses across services.
     */
    @Data
    @RequiredArgsConstructor
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        
        public static ValidationResult valid() {
            return new ValidationResult(true, List.of());
        }
        
        public static ValidationResult invalid(List<String> errors) {
            return new ValidationResult(false, errors);
        }
        
        public static ValidationResult invalid(String error) {
            return new ValidationResult(false, List.of(error));
        }
        
        public ValidationResult combine(ValidationResult other) {
            if (this.valid && other.valid) {
                return valid();
            }
            
            List<String> combinedErrors = new ArrayList<>(this.errors);
            combinedErrors.addAll(other.errors);
            return invalid(combinedErrors);
        }
    }
    
    /**
     * ValidationBuilder for fluent validation construction.
     */
    public static class ValidationBuilder {
        private final List<String> errors = new ArrayList<>();
        
        public ValidationBuilder validate(boolean condition, String errorMessage) {
            if (!condition) {
                errors.add(errorMessage);
            }
            return this;
        }
        
        public ValidationBuilder validateNotNull(Object value, String fieldName) {
            if (value == null) {
                errors.add(fieldName + " cannot be null");
            }
            return this;
        }
        
        public ValidationBuilder validateNotEmpty(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                errors.add(fieldName + " cannot be null or empty");
            }
            return this;
        }
        
        public ValidationBuilder validateNotEmpty(java.util.Collection<?> value, String fieldName) {
            if (value == null || value.isEmpty()) {
                errors.add(fieldName + " cannot be null or empty");
            }
            return this;
        }
        
        public <T> ValidationBuilder validateCondition(T value, Predicate<T> condition, String errorMessage) {
            if (value != null && !condition.test(value)) {
                errors.add(errorMessage);
            }
            return this;
        }
        
        public ValidationBuilder validateRange(Integer value, int min, int max, String fieldName) {
            if (value != null && (value < min || value > max)) {
                errors.add(String.format("%s must be between %d and %d", fieldName, min, max));
            }
            return this;
        }
        
        public ValidationBuilder validateCustom(Supplier<String> validator) {
            String error = validator.get();
            if (error != null) {
                errors.add(error);
            }
            return this;
        }
        
        public ValidationResult build() {
            return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
        }
    }
    
    /**
     * Creates a new validation builder.
     */
    public static ValidationBuilder builder() {
        return new ValidationBuilder();
    }
    
    /**
     * Validates state compliance for a staff member in a specific state.
     */
    public static String validateStaffStateCompliance(Object staff, String state) {
        // This is a placeholder for the actual state compliance validation logic
        // In a real implementation, this would check licensing, certifications, etc.
        if (staff == null) {
            return "Staff cannot be null for state compliance validation";
        }
        if (state == null || state.trim().isEmpty()) {
            return "State cannot be null or empty for compliance validation";
        }
        return null; // Valid
    }
    
    /**
     * Validates geographic coverage for a patient and available staff.
     */
    public static String validateGeographicCoverage(Object patient, List<?> eligibleStaff) {
        if (patient == null) {
            return "Patient cannot be null for geographic validation";
        }
        if (eligibleStaff == null || eligibleStaff.isEmpty()) {
            return "No eligible staff found for geographic coverage";
        }
        return null; // Valid
    }
    
    /**
     * Common validation for required fields with custom error messages.
     */
    public static ValidationResult validateRequired(Object value, String fieldName, String customMessage) {
        if (value == null) {
            return ValidationResult.invalid(
                customMessage != null ? customMessage : fieldName + " is required"
            );
        }
        return ValidationResult.valid();
    }
    
    /**
     * Validates a list of objects using a common validation function.
     */
    public static <T> ValidationResult validateList(List<T> items, 
                                                  java.util.function.Function<T, ValidationResult> itemValidator,
                                                  String listName) {
        if (items == null || items.isEmpty()) {
            return ValidationResult.invalid(listName + " cannot be null or empty");
        }
        
        ValidationResult result = ValidationResult.valid();
        for (int i = 0; i < items.size(); i++) {
            ValidationResult itemResult = itemValidator.apply(items.get(i));
            if (!itemResult.isValid()) {
                List<String> prefixedErrors = itemResult.getErrors().stream()
                    .map(error -> String.format("%s[%d]: %s", listName, i, error))
                    .toList();
                result = result.combine(ValidationResult.invalid(prefixedErrors));
            }
        }
        
        return result;
    }
}
