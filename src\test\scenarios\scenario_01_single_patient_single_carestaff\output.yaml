scenario_name: Single Patient, Single Carestaff
test_date: '2025-05-19T23:22:24.353972'
summary:
  total_patients: 1
  total_matches: 1
  validation_success: true
patients:
- patient_id: patient-001
  patient_name: <PERSON>
  required_skills:
  - wound_care
  - medication_management
  required_certifications:
  - RN
  matches:
  - rank: 1
    carestaff_id: carestaff-001
    carestaff_name: <PERSON>
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  validation:
    success: true
    expected_order:
    - carestaff-001
    actual_order:
    - carestaff-001
    missing_carestaff: []
