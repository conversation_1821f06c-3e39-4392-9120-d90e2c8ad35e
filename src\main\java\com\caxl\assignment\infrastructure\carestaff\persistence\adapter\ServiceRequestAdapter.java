package com.caxl.assignment.infrastructure.carestaff.persistence.adapter;

import com.caxl.assignment.application.carestaff.port.out.persistence.ServiceRequestPort;
import com.caxl.assignment.domain.carestaff.entities.ServiceRequest;
import com.caxl.assignment.infrastructure.carestaff.persistence.entity.ServiceRequestEntity;
import com.caxl.assignment.infrastructure.carestaff.persistence.repository.ServiceRequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Persistence adapter for service request operations.
 * Implements the ServiceRequestPort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ServiceRequestAdapter implements ServiceRequestPort {

    private final ServiceRequestRepository repository;

    @Override
    public Optional<ServiceRequest> findByIdWithRelations(UUID requestId) {
        log.debug("Finding service request by ID with relations: {}", requestId);
        
        return repository.findById(requestId)
                .map(this::mapToDomain);
    }

    @Override
    @Transactional
    public void updateStatus(UUID requestId, String status) {
        log.debug("Updating service request {} status to: {}", requestId, status);
        repository.updateStatus(requestId, status);
    }

    @Override
    @Transactional
    public void updateStatus(List<UUID> requestIds, String status) {
        log.debug("Updating {} service request statuses to: {}", requestIds.size(), status);
        repository.updateStatusBatch(requestIds, status);
    }

    @Override
    public ServiceRequest save(ServiceRequest serviceRequest) {
        log.debug("Saving service request: {}", serviceRequest.getRequestId());
        
        ServiceRequestEntity entity = mapToEntity(serviceRequest);
        ServiceRequestEntity saved = repository.save(entity);
        return mapToDomain(saved);
    }

    /**
     * Map JPA entity to domain entity.
     */
    private ServiceRequest mapToDomain(ServiceRequestEntity entity) {
        ServiceRequest.TimeWindow timeWindow = ServiceRequest.TimeWindow.builder()
                .arrivalWindowStart(entity.getArrivalWindowStart())
                .arrivalWindowEnd(entity.getArrivalWindowEnd())
                .visitDurationMinutes(entity.getVisitDurationMinutes() != null ? entity.getVisitDurationMinutes() : 60)
                .preferredStartTime(entity.getPreferredStartTime())
                .latestEndTime(entity.getLatestEndTime())
                .build();

        return ServiceRequest.builder()
                .requestId(entity.getRequestId())
                .patientId(entity.getPatientId())
                .status(entity.getStatus())
                .requiredSkillIds(entity.getRequiredSkillIds())
                .requiredCertificationIds(entity.getRequiredCertificationIds())
                .timeWindow(timeWindow)
                .visitType(entity.getVisitType())
                .priority(entity.getPriority() != null ? entity.getPriority() : 3)
                .workloadPoints(entity.getWorkloadPoints() != null ? entity.getWorkloadPoints() : 1)
                .specialInstructions(entity.getSpecialInstructions())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * Map domain entity to JPA entity.
     */
    private ServiceRequestEntity mapToEntity(ServiceRequest domain) {
        return ServiceRequestEntity.builder()
                .requestId(domain.getRequestId())
                .patientId(domain.getPatientId())
                .status(domain.getStatus())
                .requiredSkillIds(domain.getRequiredSkillIds())
                .requiredCertificationIds(domain.getRequiredCertificationIds())
                .arrivalWindowStart(domain.getTimeWindow() != null ? domain.getTimeWindow().getArrivalWindowStart() : null)
                .arrivalWindowEnd(domain.getTimeWindow() != null ? domain.getTimeWindow().getArrivalWindowEnd() : null)
                .visitDurationMinutes(domain.getTimeWindow() != null ? domain.getTimeWindow().getVisitDurationMinutes() : 60)
                .preferredStartTime(domain.getTimeWindow() != null ? domain.getTimeWindow().getPreferredStartTime() : null)
                .latestEndTime(domain.getTimeWindow() != null ? domain.getTimeWindow().getLatestEndTime() : null)
                .visitType(domain.getVisitType())
                .priority(domain.getPriority())
                .workloadPoints(domain.getWorkloadPoints())
                .specialInstructions(domain.getSpecialInstructions())
                .createdAt(domain.getCreatedAt())
                .updatedAt(domain.getUpdatedAt())
                .build();
    }
}
