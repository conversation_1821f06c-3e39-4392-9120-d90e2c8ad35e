package com.caxl.assignment.domain.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;

import com.caxl.assignment.domain.models.enums.OperatorType;

/**
 * Condition class for rule evaluation. Represents a single condition to be evaluated
 * when determining if a rule applies to a patient-clinician pair.
 */
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Condition {
    @NotBlank(message = "Field path cannot be blank")
    private final String field;
    
    @NotNull(message = "Operator cannot be null")
    private final OperatorType operator;
    
    @NotNull(message = "Value cannot be null")
    private final Object value;
    
    private final String targetObject;  // "patient" or "clinician"
}
