# CareStaff Matching Service Documentation

## Overview

The CareStaff Matching Service is a configurable, geo-filtered & constraint-aware carestaff matching system built using hexagonal architecture with programmed heuristics. It intelligently matches available carestaff to incoming service requests for home-based healthcare based on configurable hard and soft constraints.

## Documentation Structure

This documentation is organized into the following sections:

### 📋 **Core Documentation**
- [**Architecture Guide**](architecture.md) - Detailed hexagonal architecture overview
- [**API Reference**](api-reference.md) - Complete API endpoint documentation
- [**Configuration Guide**](configuration.md) - System configuration and setup
- [**Database Schema**](database-schema.md) - Database design and spatial data

### 🚀 **Getting Started**
- [**Quick Start Guide**](quick-start.md) - Get up and running in 5 minutes
- [**Installation Guide**](installation.md) - Detailed installation instructions
- [**Docker Setup**](docker-setup.md) - Containerized deployment guide

### 🔧 **Development**
- [**Development Guide**](development.md) - Development environment setup
- [**Testing Guide**](testing.md) - Testing strategies and examples
- [**Contributing Guide**](contributing.md) - How to contribute to the project

### 📊 **Business Logic**
- [**Matching Algorithm**](matching-algorithm.md) - Detailed algorithm explanation
- [**Constraint System**](constraint-system.md) - Hard and soft constraints
- [**Scoring System**](scoring-system.md) - Scoring weights and calculations

### 🔍 **Implementation Details**
- [**TODO Implementation Summary**](todo-implementation-summary.md) - All implemented TODOs
- [**Domain Services**](domain-services.md) - Business logic services
- [**Spatial Features**](spatial-features.md) - PostGIS and geographic operations

### 📈 **Operations**
- [**Monitoring Guide**](monitoring.md) - System monitoring and logging
- [**Performance Tuning**](performance-tuning.md) - Optimization strategies
- [**Troubleshooting**](troubleshooting.md) - Common issues and solutions

### 🔮 **Advanced Topics**
- [**Timefold Integration**](timefold-integration.md) - Future optimization engine integration
- [**Extensibility Guide**](extensibility.md) - How to extend the system
- [**Security Considerations**](security.md) - Security best practices

## Quick Navigation

### For Developers
- Start with [Architecture Guide](architecture.md) to understand the system design
- Follow [Development Guide](development.md) to set up your environment
- Review [Domain Services](domain-services.md) for business logic implementation

### For Operations
- Begin with [Installation Guide](installation.md) for deployment
- Use [Docker Setup](docker-setup.md) for containerized deployment
- Reference [Monitoring Guide](monitoring.md) for operational oversight

### For Business Users
- Read [Matching Algorithm](matching-algorithm.md) to understand the matching logic
- Review [Constraint System](constraint-system.md) for configuration options
- Check [Scoring System](scoring-system.md) for scoring customization

### For API Users
- Start with [Quick Start Guide](quick-start.md) for immediate usage
- Reference [API Reference](api-reference.md) for detailed endpoint documentation
- Use [Configuration Guide](configuration.md) for system setup

## Key Features

✅ **Sophisticated Matching Algorithm** with 13+ scoring factors  
✅ **Spatial/Geographic Filtering** using PostGIS  
✅ **Configurable Constraint System** via JSON configuration  
✅ **Real-time Conflict Detection** for bulk operations  
✅ **Continuity of Care Scoring** with historical analysis  
✅ **Hexagonal Architecture** for clean separation of concerns  
✅ **Production-Ready** with comprehensive testing and monitoring  

## Technology Stack

- **Java 17+** with Spring Boot 3.5.0+
- **PostgreSQL with PostGIS** for spatial operations
- **JPA/Hibernate Spatial** for data persistence
- **Jackson** for JSON processing
- **JTS** for geometry operations
- **Docker** for containerization

## Support

For questions, issues, or contributions:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review existing documentation
3. Create an issue with detailed information
4. Follow the [Contributing Guide](contributing.md) for contributions

## License

This project is part of the CAXL Assignment Service system.
