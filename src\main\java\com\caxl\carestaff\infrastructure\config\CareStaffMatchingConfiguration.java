package com.caxl.carestaff.infrastructure.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Configuration class for the CareStaff Matching Service.
 * Provides beans and configuration for the hexagonal architecture implementation.
 */
@Configuration
public class CareStaffMatchingConfiguration {

    /**
     * Configure ObjectMapper for JSON processing with proper time handling.
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.findAndRegisterModules();
        return mapper;
    }
}
