package com.caxl.assignment.domain.exceptions;

/**
 * Exception thrown when there are conflicts in scheduling or assignments.
 */
public class ConflictException extends RuntimeException {

    public ConflictException(String message) {
        super(message);
    }

    public ConflictException(String message, Throwable cause) {
        super(message, cause);
    }

    public static ConflictException careStaffUnavailable(String careStaffId, String timeWindow) {
        return new ConflictException("Care staff is unavailable during time window: " + careStaffId + " at " + timeWindow);
    }

    public static ConflictException schedulingConflict(String details) {
        return new ConflictException("Scheduling conflict detected: " + details);
    }
}
