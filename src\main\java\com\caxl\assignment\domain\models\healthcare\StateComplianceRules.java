package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * State-specific compliance rules and regulations for US homecare scheduling.
 * Each state may have different requirements for licensing, working hours, and care delivery.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class StateComplianceRules {

    @NotBlank(message = "State code is required")
    @JsonProperty("state_code")
    private String stateCode; // e.g., "CA", "TX", "NY"

    @NotBlank(message = "State name is required")
    @JsonProperty("state_name")
    private String stateName;

    /**
     * Working hours and overtime regulations.
     */
    @Valid
    @NotNull(message = "Working hours rules are required")
    @JsonProperty("working_hours_rules")
    private WorkingHoursRules workingHoursRules;

    /**
     * Licensing and certification requirements.
     */
    @Valid
    @JsonProperty("licensing_requirements")
    private LicensingRequirements licensingRequirements;

    /**
     * Patient care and supervision requirements.
     */
    @Valid
    @JsonProperty("patient_care_rules")
    private PatientCareRules patientCareRules;

    /**
     * Documentation and reporting requirements.
     */
    @Valid
    @JsonProperty("documentation_rules")
    private DocumentationRules documentationRules;

    /**
     * Emergency response and on-call requirements.
     */
    @Valid
    @JsonProperty("emergency_response_rules")
    private EmergencyResponseRules emergencyResponseRules;

    // === NESTED CLASSES ===

    /**
     * Working hours and overtime regulations by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WorkingHoursRules {
        
        @JsonProperty("max_daily_hours")
        @Builder.Default
        private Integer maxDailyHours = 12;

        @JsonProperty("max_weekly_hours")
        @Builder.Default
        private Integer maxWeeklyHours = 40;

        @JsonProperty("overtime_threshold_daily")
        @Builder.Default
        private Integer overtimeThresholdDaily = 8;

        @JsonProperty("overtime_threshold_weekly")
        @Builder.Default
        private Integer overtimeThresholdWeekly = 40;

        @JsonProperty("mandatory_break_duration")
        @Builder.Default
        private Duration mandatoryBreakDuration = Duration.ofMinutes(30);

        @JsonProperty("min_rest_between_shifts")
        @Builder.Default
        private Duration minRestBetweenShifts = Duration.ofHours(8);

        @JsonProperty("max_consecutive_days")
        @Builder.Default
        private Integer maxConsecutiveDays = 6;

        @JsonProperty("weekend_overtime_rules")
        @Builder.Default
        private boolean weekendOvertimeRules = false;

        @JsonProperty("holiday_pay_multiplier")
        @Builder.Default
        private Double holidayPayMultiplier = 1.5;
    }

    /**
     * State-specific licensing and certification requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class LicensingRequirements {
        
        @JsonProperty("required_licenses")
        private List<String> requiredLicenses;

        @JsonProperty("reciprocity_states")
        private List<String> reciprocityStates; // States with license reciprocity

        @JsonProperty("background_check_requirements")
        private BackgroundCheckRequirements backgroundCheckRequirements;

        @JsonProperty("continuing_education_hours")
        private Integer continuingEducationHours;

        @JsonProperty("license_renewal_frequency_months")
        @Builder.Default
        private Integer licenseRenewalFrequencyMonths = 24;

        @JsonProperty("supervision_requirements")
        private Map<String, String> supervisionRequirements; // license_type -> supervision_required
    }

    /**
     * Background check requirements by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BackgroundCheckRequirements {
        
        @JsonProperty("criminal_background_check")
        @Builder.Default
        private boolean criminalBackgroundCheck = true;

        @JsonProperty("fingerprint_check")
        @Builder.Default
        private boolean fingerprintCheck = true;

        @JsonProperty("drug_screening")
        @Builder.Default
        private boolean drugScreening = true;

        @JsonProperty("registry_checks")
        private List<String> registryChecks; // OIG, state abuse registries

        @JsonProperty("renewal_frequency_years")
        @Builder.Default
        private Integer renewalFrequencyYears = 2;
    }

    /**
     * Patient care and clinical supervision rules.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PatientCareRules {
        
        @JsonProperty("max_patients_per_staff")
        private Integer maxPatientsPerStaff;

        @JsonProperty("skilled_nursing_supervision_ratio")
        private String skilledNursingSupervisionRatio; // e.g., "1:10"

        @JsonProperty("aide_supervision_requirements")
        @Builder.Default
        private boolean aideSupervisionRequirements = true;

        @JsonProperty("medication_administration_rules")
        private MedicationAdministrationRules medicationAdministrationRules;

        @JsonProperty("infection_control_requirements")
        private List<String> infectionControlRequirements;

        @JsonProperty("patient_rights_training")
        @Builder.Default
        private boolean patientRightsTraining = true;
    }

    /**
     * Medication administration rules by state.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MedicationAdministrationRules {
        
        @JsonProperty("aide_can_administer")
        @Builder.Default
        private boolean aideCanAdminister = false;

        @JsonProperty("required_training_hours")
        private Integer requiredTrainingHours;

        @JsonProperty("supervision_required")
        @Builder.Default
        private boolean supervisionRequired = true;

        @JsonProperty("controlled_substances_allowed")
        @Builder.Default
        private boolean controlledSubstancesAllowed = false;

        @JsonProperty("documentation_requirements")
        private List<String> documentationRequirements;
    }

    /**
     * Documentation and reporting requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DocumentationRules {
        
        @JsonProperty("visit_documentation_deadline_hours")
        @Builder.Default
        private Integer visitDocumentationDeadlineHours = 24;

        @JsonProperty("required_documentation_elements")
        private List<String> requiredDocumentationElements;

        @JsonProperty("electronic_signature_allowed")
        @Builder.Default
        private boolean electronicSignatureAllowed = true;

        @JsonProperty("supervisor_review_required")
        @Builder.Default
        private boolean supervisorReviewRequired = false;

        @JsonProperty("patient_plan_update_frequency_days")
        @Builder.Default
        private Integer patientPlanUpdateFrequencyDays = 60;

        @JsonProperty("incident_reporting_deadline_hours")
        @Builder.Default
        private Integer incidentReportingDeadlineHours = 24;
    }

    /**
     * Emergency response and on-call requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EmergencyResponseRules {
        
        @JsonProperty("on_call_coverage_required")
        @Builder.Default
        private boolean onCallCoverageRequired = true;

        @JsonProperty("emergency_response_time_minutes")
        @Builder.Default
        private Integer emergencyResponseTimeMinutes = 60;

        @JsonProperty("rn_on_call_required")
        @Builder.Default
        private boolean rnOnCallRequired = true;

        @JsonProperty("backup_staff_requirements")
        private Integer backupStaffRequirements;

        @JsonProperty("emergency_protocols")
        private List<String> emergencyProtocols;

        @JsonProperty("after_hours_service_required")
        @Builder.Default
        private boolean afterHoursServiceRequired = true;
    }

    /**
     * State-specific scheduling constraints and rules.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SchedulingConstraints {
        
        @JsonProperty("min_visit_duration_minutes")
        @Builder.Default
        private Integer minVisitDurationMinutes = 15;

        @JsonProperty("max_visit_duration_hours")
        @Builder.Default
        private Integer maxVisitDurationHours = 4;

        @JsonProperty("travel_time_compensation_required")
        @Builder.Default
        private boolean travelTimeCompensationRequired = false;

        @JsonProperty("same_day_rescheduling_allowed")
        @Builder.Default
        private boolean sameDayReschedulingAllowed = true;

        @JsonProperty("advance_scheduling_notice_hours")
        @Builder.Default
        private Integer advanceSchedulingNoticeHours = 24;

        @JsonProperty("weekend_scheduling_restrictions")
        private List<String> weekendSchedulingRestrictions;
    }
}
