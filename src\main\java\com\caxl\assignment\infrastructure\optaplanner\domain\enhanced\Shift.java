package com.caxl.assignment.infrastructure.optaplanner.domain.enhanced;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.lookup.PlanningId;
import ai.timefold.solver.core.api.domain.variable.PlanningVariable;
import ai.timefold.solver.core.api.domain.variable.PlanningListVariable;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Duration;
import java.util.List;
import java.util.ArrayList;

/**
 * Enhanced Shift entity that represents a clinician's work shift with multiple patient visits.
 * This follows Timefold's shift planning best practices for healthcare scheduling.
 */
@PlanningEntity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Shift {

    @PlanningId
    private String id;

    /**
     * The clinician assigned to this shift (planning variable).
     */
    @PlanningVariable(valueRangeProviderRefs = "clinicianRange")
    private Clinician assignedClinician;

    /**
     * Ordered list of patient visits in this shift (planning list variable).
     * Timefold will optimize both assignment and sequencing.
     */
    @PlanningListVariable(valueRangeProviderRefs = "patientRange")
    @Builder.Default
    private List<Patient> patientVisits = new ArrayList<>();

    /**
     * Shift date.
     */
    private LocalDate shiftDate;

    /**
     * Shift start time.
     */
    private LocalTime shiftStartTime;

    /**
     * Shift end time.
     */
    private LocalTime shiftEndTime;

    /**
     * Maximum number of patients that can be visited in this shift.
     */
    @Builder.Default
    private int maxPatients = 8;

    /**
     * Maximum workload points for this shift.
     */
    @Builder.Default
    private int maxWorkloadPoints = 100;

    /**
     * Shift type (e.g., MORNING, AFTERNOON, EVENING, NIGHT).
     */
    private ShiftType shiftType;

    /**
     * Required skills for this shift.
     */
    @Builder.Default
    private List<String> requiredSkills = new ArrayList<>();

    /**
     * Geographic area for this shift.
     */
    private String serviceArea;

    /**
     * Calculate total workload points for all patients in this shift.
     */
    public int getTotalWorkloadPoints() {
        return patientVisits.stream()
                .mapToInt(Patient::getWorkloadPoints)
                .sum();
    }

    /**
     * Calculate total duration needed for all patient visits.
     */
    public Duration getTotalVisitDuration() {
        return patientVisits.stream()
                .map(this::getEstimatedVisitDuration)
                .reduce(Duration.ZERO, Duration::plus);
    }

    /**
     * Get estimated duration for a patient visit based on visit type and complexity.
     */
    private Duration getEstimatedVisitDuration(Patient patient) {
        // Base duration by visit type
        Duration baseDuration = switch (patient.getVisitType().toLowerCase()) {
            case "assessment" -> Duration.ofMinutes(60);
            case "treatment" -> Duration.ofMinutes(45);
            case "follow-up" -> Duration.ofMinutes(30);
            case "medication" -> Duration.ofMinutes(15);
            default -> Duration.ofMinutes(30);
        };

        // Adjust for complexity (workload points)
        double complexityMultiplier = 1.0 + (patient.getWorkloadPoints() - 1) * 0.1;
        return baseDuration.multipliedBy((long) complexityMultiplier);
    }

    /**
     * Check if this shift can accommodate a new patient visit.
     */
    public boolean canAccommodatePatient(Patient patient) {
        if (patientVisits.size() >= maxPatients) {
            return false;
        }

        if (getTotalWorkloadPoints() + patient.getWorkloadPoints() > maxWorkloadPoints) {
            return false;
        }

        Duration newTotalDuration = getTotalVisitDuration().plus(getEstimatedVisitDuration(patient));
        Duration shiftDuration = Duration.between(shiftStartTime, shiftEndTime);
        
        // Leave 30 minutes buffer for travel time
        return newTotalDuration.plus(Duration.ofMinutes(30)).compareTo(shiftDuration) <= 0;
    }

    /**
     * Get the number of patients currently assigned to this shift.
     */
    public int getPatientCount() {
        return patientVisits.size();
    }

    /**
     * Check if the shift is feasible (within constraints).
     */
    public boolean isFeasible() {
        return patientVisits.size() <= maxPatients && 
               getTotalWorkloadPoints() <= maxWorkloadPoints;
    }

    /**
     * Shift type enumeration.
     */
    public enum ShiftType {
        MORNING("06:00", "14:00"),
        AFTERNOON("14:00", "22:00"),
        EVENING("18:00", "02:00"),
        NIGHT("22:00", "06:00"),
        FULL_DAY("08:00", "18:00");

        private final String startTime;
        private final String endTime;

        ShiftType(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getStartTime() { return startTime; }
        public String getEndTime() { return endTime; }
    }
}
