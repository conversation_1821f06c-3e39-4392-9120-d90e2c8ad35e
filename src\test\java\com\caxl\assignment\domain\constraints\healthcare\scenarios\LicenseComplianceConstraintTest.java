package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Set;

/**
 * Comprehensive test suite for license compliance constraints in homecare scheduling.
 * Tests professional licensing requirements and certifications for care staff.
 */
@DisplayName("License Compliance Constraint Tests")
class LicenseComplianceConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic License Validation")
    class BasicLicenseValidation {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenStaffHasRequiredLicense_shouldPass")
        void testLicenseComplianceConstraint_whenStaffHasRequiredLicense_shouldPass() {
            // Given: Care staff with RN license
            CareStaff rnStaff = testDataFactory.createCareStaffWithLicenses("rnStaff", Set.of("RN"));
            
            // And: Visit requiring RN skills
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("rnStaff");
            
            // When: Verifying the constraint
            // Then: Should pass (staff has required license)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(rnStaff, rnVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenStaffLacksRequiredLicense_shouldFail")
        void testLicenseComplianceConstraint_whenStaffLacksRequiredLicense_shouldFail() {
            // Given: Care staff with only CNA license
            CareStaff cnaStaff = testDataFactory.createCareStaffWithLicenses("cnaStaff", Set.of("CNA"));
            
            // And: Visit requiring RN license
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("cnaStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff lacks required license)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(cnaStaff, rnVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenVisitRequiresNoLicense_shouldPass")
        void testLicenseComplianceConstraint_whenVisitRequiresNoLicense_shouldPass() {
            // Given: Care staff with any license
            CareStaff anyStaff = testDataFactory.createCareStaffWithLicenses("anyStaff", Set.of("CNA"));
            
            // And: Visit with no specific license requirements
            HomecareVisit basicVisit = testDataFactory.createBasicVisit("basicVisit");
            basicVisit.setAssignedCareStaffId("anyStaff");
            
            // When: Verifying the constraint
            // Then: Should pass (no license required)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(anyStaff, basicVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenVisitUnassigned_shouldPass")
        void testLicenseComplianceConstraint_whenVisitUnassigned_shouldPass() {
            // Given: Care staff with any license
            CareStaff anyStaff = testDataFactory.createCareStaffWithLicenses("anyStaff", Set.of("RN"));
            
            // And: Unassigned visit requiring license
            HomecareVisit unassignedVisit = testDataFactory.createVisitRequiringLicense("unassignedVisit", "RN");
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (constraint only applies to assigned visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(anyStaff, unassignedVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple License Scenarios")
    class MultipleLicenseScenarios {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenStaffHasMultipleLicenses_shouldMatchAny")
        void testLicenseComplianceConstraint_whenStaffHasMultipleLicenses_shouldMatchAny() {
            // Given: Care staff with multiple professional licenses
            CareStaff multiLicenseStaff = testDataFactory.createCareStaffWithLicenses("multiLicenseStaff", 
                    Set.of("RN", "LPN", "CNA", "PT"));
            
            // And: Visits requiring different licenses
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("multiLicenseStaff");
            
            HomecareVisit lpnVisit = testDataFactory.createVisitRequiringLicense("lpnVisit", "LPN");
            lpnVisit.setAssignedCareStaffId("multiLicenseStaff");
            
            HomecareVisit ptVisit = testDataFactory.createVisitRequiringLicense("ptVisit", "PT");
            ptVisit.setAssignedCareStaffId("multiLicenseStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (staff qualified for all visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(multiLicenseStaff, rnVisit, lpnVisit, ptVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenMixedLicenseCompliance_shouldPenalizeViolations")
        void testLicenseComplianceConstraint_whenMixedLicenseCompliance_shouldPenalizeViolations() {
            // Given: Care staff with limited licenses
            CareStaff limitedStaff = testDataFactory.createCareStaffWithLicenses("limitedStaff", 
                    Set.of("CNA", "LPN"));
            
            // And: Mixed visits - some matching, some not
            HomecareVisit cnaVisit = testDataFactory.createVisitRequiringLicense("cnaVisit", "CNA");
            cnaVisit.setAssignedCareStaffId("limitedStaff");
            
            HomecareVisit lpnVisit = testDataFactory.createVisitRequiringLicense("lpnVisit", "LPN");
            lpnVisit.setAssignedCareStaffId("limitedStaff");
            
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("limitedStaff");
            
            HomecareVisit ptVisit = testDataFactory.createVisitRequiringLicense("ptVisit", "PT");
            ptVisit.setAssignedCareStaffId("limitedStaff");
            
            // When: Verifying the constraint
            // Then: Should penalize for RN and PT violations (2 penalties)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(limitedStaff, cnaVisit, lpnVisit, rnVisit, ptVisit)
                    .penalizesBy(2);
        }
    }

    @Nested
    @DisplayName("License Expiration and Validity")
    class LicenseExpirationAndValidity {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenLicenseExpired_shouldFail")
        void testLicenseComplianceConstraint_whenLicenseExpired_shouldFail() {
            // Given: Care staff with expired license
            CareStaff expiredLicenseStaff = testDataFactory.createCareStaffWithExpiredLicense("expiredStaff", 
                    "RN", LocalDate.now().minusDays(30));
            
            // And: Visit requiring the expired license
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("expiredStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (license expired)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(expiredLicenseStaff, rnVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenLicenseNearExpiration_shouldWarn")
        void testLicenseComplianceConstraint_whenLicenseNearExpiration_shouldWarn() {
            // Given: Care staff with license expiring soon
            CareStaff nearExpiryStaff = testDataFactory.createCareStaffWithExpiringLicense("nearExpiryStaff", 
                    "RN", LocalDate.now().plusDays(7));
            
            // And: Visit requiring the soon-to-expire license
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("nearExpiryStaff");
            
            // When: Verifying the constraint
            // Then: Should warn but not fail (license still valid)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(nearExpiryStaff, rnVisit)
                    .penalizesBy(0); // No penalty but may trigger warning in real implementation
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenLicenseValidForFuture_shouldPass")
        void testLicenseComplianceConstraint_whenLicenseValidForFuture_shouldPass() {
            // Given: Care staff with license valid for years
            CareStaff validLicenseStaff = testDataFactory.createCareStaffWithValidLicense("validStaff", 
                    "RN", LocalDate.now().plusYears(2));
            
            // And: Visit requiring the valid license
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("validStaff");
            
            // When: Verifying the constraint
            // Then: Should pass (license valid)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(validLicenseStaff, rnVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Specialized License Requirements")
    class SpecializedLicenseRequirements {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenSpecializedCareRequired_shouldValidateSpecialty")
        void testLicenseComplianceConstraint_whenSpecializedCareRequired_shouldValidateSpecialty() {
            // Given: Care staff with specialized certifications
            CareStaff specialistStaff = testDataFactory.createCareStaffWithLicenses("specialistStaff", 
                    Set.of("RN", "WOUND_CARE", "DIABETES_EDUCATOR"));
            
            // And: Visit requiring specialized wound care
            HomecareVisit woundCareVisit = testDataFactory.createVisitRequiringLicense("woundCareVisit", "WOUND_CARE");
            woundCareVisit.setAssignedCareStaffId("specialistStaff");
            
            // And: Visit requiring diabetes education
            HomecareVisit diabetesVisit = testDataFactory.createVisitRequiringLicense("diabetesVisit", "DIABETES_EDUCATOR");
            diabetesVisit.setAssignedCareStaffId("specialistStaff");
            
            // When: Verifying the constraint
            // Then: Should pass (staff has required specializations)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(specialistStaff, woundCareVisit, diabetesVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenBasicStaffAssignedSpecialty_shouldFail")
        void testLicenseComplianceConstraint_whenBasicStaffAssignedSpecialty_shouldFail() {
            // Given: Care staff with only basic license
            CareStaff basicStaff = testDataFactory.createCareStaffWithLicenses("basicStaff", Set.of("CNA"));
            
            // And: Visit requiring specialized certification
            HomecareVisit specialtyVisit = testDataFactory.createVisitRequiringLicense("specialtyVisit", "WOUND_CARE");
            specialtyVisit.setAssignedCareStaffId("basicStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff lacks specialty certification)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(basicStaff, specialtyVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenMultipleSpecialtiesRequired_shouldValidateAll")
        void testLicenseComplianceConstraint_whenMultipleSpecialtiesRequired_shouldValidateAll() {
            // Given: Care staff with some but not all required specialties
            CareStaff partialSpecialistStaff = testDataFactory.createCareStaffWithLicenses("partialSpecialistStaff", 
                    Set.of("RN", "WOUND_CARE"));
            
            // And: Visit requiring multiple specializations
            HomecareVisit complexVisit = testDataFactory.createVisitRequiringMultipleLicenses("complexVisit", 
                    Set.of("RN", "WOUND_CARE", "DIABETES_EDUCATOR"));
            complexVisit.setAssignedCareStaffId("partialSpecialistStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff missing diabetes educator certification)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(partialSpecialistStaff, complexVisit)
                    .penalizesBy(1);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Error Handling")
    class EdgeCasesAndErrorHandling {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenStaffHasNoLicenses_shouldFailForAnyRequirement")
        void testLicenseComplianceConstraint_whenStaffHasNoLicenses_shouldFailForAnyRequirement() {
            // Given: Unlicensed staff member
            CareStaff unlicensedStaff = testDataFactory.createCareStaffWithLicenses("unlicensedStaff", Set.of());
            
            // And: Visit requiring basic license
            HomecareVisit basicLicenseVisit = testDataFactory.createVisitRequiringLicense("basicLicenseVisit", "CNA");
            basicLicenseVisit.setAssignedCareStaffId("unlicensedStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff has no licenses)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(unlicensedStaff, basicLicenseVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenLicenseTypesInconsistent_shouldHandle")
        void testLicenseComplianceConstraint_whenLicenseTypesInconsistent_shouldHandle() {
            // Given: Care staff with non-standard license codes
            CareStaff nonStandardStaff = testDataFactory.createCareStaffWithLicenses("nonStandardStaff", 
                    Set.of("rn", "Lpn", "CNA")); // Mixed case
            
            // And: Visit requiring standard license code
            HomecareVisit standardVisit = testDataFactory.createVisitRequiringLicense("standardVisit", "RN");
            standardVisit.setAssignedCareStaffId("nonStandardStaff");
            
            // When: Verifying the constraint
            // Then: Should handle case-sensitivity appropriately
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(nonStandardStaff, standardVisit)
                    .penalizesBy(0); // Assuming case-insensitive matching
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenNullLicenseRequirement_shouldPass")
        void testLicenseComplianceConstraint_whenNullLicenseRequirement_shouldPass() {
            // Given: Care staff with any license
            CareStaff anyStaff = testDataFactory.createCareStaffWithLicenses("anyStaff", Set.of("CNA"));
            
            // And: Visit with null/undefined license requirement
            HomecareVisit noRequirementVisit = testDataFactory.createBasicVisit("noRequirementVisit");
            noRequirementVisit.setAssignedCareStaffId("anyStaff");
            // Note: License requirement is null/undefined
            
            // When: Verifying the constraint
            // Then: Should pass (no specific requirement)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(anyStaff, noRequirementVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple Staff License Validation")
    class MultipleStaffLicenseValidation {

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenMultipleStaffDifferentLicenses_shouldEvaluateIndependently")
        void testLicenseComplianceConstraint_whenMultipleStaffDifferentLicenses_shouldEvaluateIndependently() {
            // Given: Different staff with different license levels
            CareStaff rnStaff = testDataFactory.createCareStaffWithLicenses("rnStaff", Set.of("RN"));
            CareStaff lpnStaff = testDataFactory.createCareStaffWithLicenses("lpnStaff", Set.of("LPN"));
            CareStaff cnaStaff = testDataFactory.createCareStaffWithLicenses("cnaStaff", Set.of("CNA"));
            
            // And: Visits appropriately assigned to qualified staff
            HomecareVisit rnVisit = testDataFactory.createVisitRequiringLicense("rnVisit", "RN");
            rnVisit.setAssignedCareStaffId("rnStaff");
            
            HomecareVisit lpnVisit = testDataFactory.createVisitRequiringLicense("lpnVisit", "LPN");
            lpnVisit.setAssignedCareStaffId("lpnStaff");
            
            HomecareVisit cnaVisit = testDataFactory.createVisitRequiringLicense("cnaVisit", "CNA");
            cnaVisit.setAssignedCareStaffId("cnaStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (each staff assigned appropriate visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(rnStaff, lpnStaff, cnaStaff, rnVisit, lpnVisit, cnaVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testLicenseComplianceConstraint_whenCrossLicenseViolations_shouldPenalizeCorrectly")
        void testLicenseComplianceConstraint_whenCrossLicenseViolations_shouldPenalizeCorrectly() {
            // Given: Staff with specific license levels
            CareStaff cnaOnlyStaff = testDataFactory.createCareStaffWithLicenses("cnaOnlyStaff", Set.of("CNA"));
            CareStaff lpnOnlyStaff = testDataFactory.createCareStaffWithLicenses("lpnOnlyStaff", Set.of("LPN"));
            
            // And: Inappropriate assignments (lower license assigned higher requirement)
            HomecareVisit rnForCna = testDataFactory.createVisitRequiringLicense("rnForCna", "RN");
            rnForCna.setAssignedCareStaffId("cnaOnlyStaff"); // CNA assigned RN work
            
            HomecareVisit rnForLpn = testDataFactory.createVisitRequiringLicense("rnForLpn", "RN");
            rnForLpn.setAssignedCareStaffId("lpnOnlyStaff"); // LPN assigned RN work
            
            // And: One correct assignment
            HomecareVisit cnaWork = testDataFactory.createVisitRequiringLicense("cnaWork", "CNA");
            cnaWork.setAssignedCareStaffId("cnaOnlyStaff");
            
            // When: Verifying the constraint
            // Then: Should penalize 2 violations (both inappropriate RN assignments)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::professionalLicenseCompliance)
                    .given(cnaOnlyStaff, lpnOnlyStaff, rnForCna, rnForLpn, cnaWork)
                    .penalizesBy(2);
        }
    }
}
