{"name": "Skill Hierarchy Fallback Matching with Workload Prioritization", "description": "Tests the system's ability to use skill hierarchy for fallback matching when exact skill matches are not available, and prioritize carestaff based on workload. For example, an RN (level 1) can perform tasks requiring an LPN (level 2), and carestaff with lower workloads are prioritized over those with higher workloads.", "patients": [{"id": "patient-001", "name": "<PERSON>", "mrn": "MRN12345", "location": {"address": "123 Main St, Anytown, CA 90210", "postal_code": "90210", "coordinates": {"latitude": 34.0522, "longitude": -118.2437}}, "required_skills": ["LPN"], "required_certifications": ["Nursing"], "required_slots": [{"date": "2025-06-01", "start_time": "09:00", "end_time": "11:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 2}}, {"id": "patient-002", "name": "<PERSON>", "mrn": "MRN67890", "location": {"address": "456 Oak St, Anytown, CA 90211", "postal_code": "90211", "coordinates": {"latitude": 34.0548, "longitude": -118.25}}, "required_skills": ["Therapy Aide"], "required_certifications": ["Therapy"], "required_slots": [{"date": "2025-06-01", "start_time": "13:00", "end_time": "15:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english", "spanish"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 1}}, {"id": "patient-003", "name": "<PERSON>", "mrn": "MRN54321", "location": {"address": "789 Pine St, Anytown, CA 90212", "postal_code": "90212", "coordinates": {"latitude": 34.053, "longitude": -118.248}}, "required_skills": ["CNA"], "required_certifications": ["Nursing"], "required_slots": [{"date": "2025-06-02", "start_time": "16:00", "end_time": "18:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 1}}], "carestaff": [{"id": "carestaff-001", "name": "<PERSON>", "service_type": "home_care", "skills": ["RN"], "certifications": ["Nursing", "CPR"], "languages": ["english"], "location": {"address": "101 Elm St, Anytown, CA 90210", "service_areas": ["90210", "90211", "90212"], "coordinates": {"latitude": 34.0535, "longitude": -118.249}}, "available_slots": [{"date": "2025-06-01", "start_time": "08:00", "end_time": "12:00"}, {"date": "2025-06-01", "start_time": "15:00", "end_time": "19:00"}, {"date": "2025-06-02", "start_time": "08:00", "end_time": "20:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 5}, "current_appointment_count": 2, "max_daily_appointments": 5}, {"id": "carestaff-002", "name": "<PERSON>", "service_type": "home_care", "skills": ["Physical Therapist"], "certifications": ["Therapy", "CPR"], "languages": ["english", "spanish"], "location": {"address": "202 Maple St, Anytown, CA 90211", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.054, "longitude": -118.2495}}, "available_slots": [{"date": "2025-06-01", "start_time": "09:00", "end_time": "17:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 8}, "current_appointment_count": 1, "max_daily_appointments": 4}, {"id": "carestaff-003", "name": "<PERSON>", "service_type": "home_care", "skills": ["LPN"], "certifications": ["Nursing", "CPR"], "languages": ["english", "spanish"], "location": {"address": "303 Cedar St, Anytown, CA 90211", "service_areas": ["90211", "90212"], "coordinates": {"latitude": 34.0545, "longitude": -118.2505}}, "available_slots": [{"date": "2025-06-01", "start_time": "12:00", "end_time": "20:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 3}, "current_appointment_count": 3, "max_daily_appointments": 5}, {"id": "carestaff-004", "name": "<PERSON>", "service_type": "home_care", "skills": ["Occupational Therapist"], "certifications": ["Therapy", "CPR"], "languages": ["english", "mandarin"], "location": {"address": "404 Birch St, Anytown, CA 90212", "service_areas": ["90212"], "coordinates": {"latitude": 34.055, "longitude": -118.251}}, "available_slots": [{"date": "2025-06-01", "start_time": "14:00", "end_time": "22:00"}, {"date": "2025-06-02", "start_time": "14:00", "end_time": "22:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 6}, "current_appointment_count": 1, "max_daily_appointments": 3}, {"id": "carestaff-005", "name": "<PERSON>", "service_type": "home_care", "skills": ["RN"], "certifications": ["Nursing", "CPR"], "languages": ["english", "mandarin"], "location": {"address": "505 Pine St, Anytown, CA 90210", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.0555, "longitude": -118.2515}}, "available_slots": [{"date": "2025-06-01", "start_time": "08:00", "end_time": "20:00"}, {"date": "2025-06-02", "start_time": "08:00", "end_time": "20:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 7}, "current_appointment_count": 0, "max_daily_appointments": 5}, {"id": "carestaff-006", "name": "<PERSON>", "service_type": "home_care", "skills": ["Physical Therapist"], "certifications": ["Therapy", "CPR"], "languages": ["english"], "location": {"address": "606 Oak St, Anytown, CA 90211", "service_areas": ["90211"], "coordinates": {"latitude": 34.056, "longitude": -118.252}}, "available_slots": [{"date": "2025-06-01", "start_time": "10:00", "end_time": "18:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 4}, "current_appointment_count": 0, "max_daily_appointments": 4}], "skill_hierarchy": {"Nursing": {"RN": 1, "LPN": 2, "CNA": 3}, "Therapy": {"Physical Therapist": 1, "Occupational Therapist": 2, "Therapy Aide": 3}}, "rules": {"skill-match": {"check_hierarchy": true}, "carestaff-workload-priority": {"section": "WORKLOAD_PRIORITIZATION", "type": "prioritization", "rule_id": "carestaff-workload-priority", "description": "Prioritize carestaffs with the least workload", "priority_attribute": "current_appointment_count", "priority_order": "asc", "enabled": true}, "reschedule-options": {"section": "RESCHEDULING_RULES", "type": "rescheduling", "rule_id": "reschedule-options", "description": "Rules for rescheduling appointments, considering skill hierarchy", "options": [{"time_shift_days": 1, "weight": 0.8, "consider_hierarchy": true}, {"time_shift_days": 2, "weight": 0.5, "consider_hierarchy": true}, {"alternate_geofence": true, "weight": 0.6, "consider_hierarchy": false}, {"skill_level_preference": "exact", "weight": 0.9}, {"skill_level_preference": "higher", "weight": 0.1}]}}, "expected_matches": {"patient-001": ["carestaff-005", "carestaff-001", "carestaff-003"], "patient-002": ["carestaff-006", "carestaff-002", "carestaff-004"], "patient-003": ["carestaff-005", "carestaff-001"]}, "expected_match_explanations": {"patient-001": {"carestaff-005": "RN (level 1) can perform LPN (level 2) tasks - higher skill level fallback with lowest workload (0 appointments)", "carestaff-001": "RN (level 1) can perform LPN (level 2) tasks - higher skill level fallback with medium workload (2 appointments)", "carestaff-003": "Exact skill match for LPN but higher workload (3 appointments)"}, "patient-002": {"carestaff-006": "Physical Therapist (level 1) can perform Therapy Aide (level 3) tasks - higher skill level fallback with lowest workload (0 appointments)", "carestaff-002": "Physical Therapist (level 1) can perform Therapy Aide (level 3) tasks - higher skill level fallback with medium workload (1 appointment)", "carestaff-004": "Occupational Therapist (level 2) can perform Therapy Aide (level 3) tasks - higher skill level fallback with medium workload (1 appointment)"}, "patient-003": {"carestaff-005": "RN (level 1) can perform CNA (level 3) tasks - higher skill level fallback with lowest workload (0 appointments)", "carestaff-001": "RN (level 1) can perform CNA (level 3) tasks - higher skill level fallback with medium workload (2 appointments)"}}, "expected_prioritization": {"patient-001": ["carestaff-005", "carestaff-001", "carestaff-003"], "patient-002": ["carestaff-006", "carestaff-002", "carestaff-004"], "patient-003": ["carestaff-005", "carestaff-001"]}, "expected_prioritization_explanations": {"patient-001": {"carestaff-005": "Lowest workload (0 appointments) with highest skill level (RN)", "carestaff-001": "Medium workload (2 appointments) with highest skill level (RN)", "carestaff-003": "Highest workload (3 appointments) with exact skill match (LPN)"}, "patient-002": {"carestaff-006": "Lowest workload (0 appointments) with highest skill level (Physical Therapist)", "carestaff-002": "Medium workload (1 appointment) with highest skill level (Physical Therapist)", "carestaff-004": "Medium workload (1 appointment) but lower skill level (Occupational Therapist)"}, "patient-003": {"carestaff-005": "Lowest workload (0 appointments) with highest skill level (RN)", "carestaff-001": "Medium workload (2 appointments) with highest skill level (RN)"}}}