package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * CareTeam domain model for organizing CareStaff into manageable teams.
 * Teams operate within specific geographic areas and have specialized capabilities.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CareTeam {

    @NotBlank(message = "Team ID is required")
    @JsonProperty("team_id")
    private String teamId;

    @NotBlank(message = "Team name is required")
    @JsonProperty("team_name")
    private String teamName;

    /**
     * Team manager/supervisor information.
     */
    @Valid
    @NotNull(message = "Team lead is required")
    @JsonProperty("team_lead")
    private TeamLead teamLead;

    /**
     * List of care staff members in this team.
     */
    @JsonProperty("care_staff_ids")
    private List<String> careStaffIds; // References to CareStaff IDs

    /**
     * Geographic coverage areas for this team.
     */
    @Valid
    @JsonProperty("coverage_areas")
    private List<CoverageArea> coverageAreas;

    /**
     * Team specializations and capabilities.
     */
    @Valid
    @JsonProperty("team_specializations")
    private TeamSpecializations teamSpecializations;

    /**
     * Operating schedule for the team.
     */
    @Valid
    @JsonProperty("operating_schedule")
    private OperatingSchedule operatingSchedule;

    /**
     * Team capacity and workload metrics.
     */
    @Valid
    @JsonProperty("team_capacity")
    private TeamCapacity teamCapacity;

    // === NESTED CLASSES ===

    /**
     * Team lead/supervisor information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TeamLead {
        
        @NotBlank(message = "Team lead ID is required")
        @JsonProperty("lead_id")
        private String leadId;

        @NotBlank(message = "Team lead name is required")
        @JsonProperty("lead_name")
        private String leadName;

        @JsonProperty("contact_phone")
        private String contactPhone;

        @JsonProperty("contact_email")
        private String contactEmail;

        @JsonProperty("supervisor_level")
        private String supervisorLevel; // RN, Clinical Manager, etc.
    }

    /**
     * Geographic coverage area for the team.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CoverageArea {
        
        @NotBlank(message = "Area ID is required")
        @JsonProperty("area_id")
        private String areaId;

        @NotBlank(message = "Area name is required")
        @JsonProperty("area_name")
        private String areaName;

        @JsonProperty("zip_codes")
        private List<String> zipCodes;

        @JsonProperty("priority_level")
        private Integer priorityLevel; // 1=primary, 2=secondary, etc.

        @JsonProperty("max_travel_distance_miles")
        private Double maxTravelDistanceMiles;
    }

    /**
     * Team specializations and service capabilities.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TeamSpecializations {
        
        @JsonProperty("primary_services")
        private List<String> primaryServices; // Skilled Nursing, PT, OT, etc.

        @JsonProperty("patient_populations")
        private List<String> patientPopulations; // Pediatric, Geriatric, etc.

        @JsonProperty("medical_conditions")
        private List<String> medicalConditions; // Diabetes, COPD, etc.

        @JsonProperty("language_support")
        private List<String> languageSupport;

        @JsonProperty("special_equipment")
        private List<String> specialEquipment; // Ventilator, IV pumps, etc.
    }

    /**
     * Team operating schedule and availability.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OperatingSchedule {
        
        @JsonProperty("operates_24_7")
        @Builder.Default
        private boolean operates24_7 = false;

        @JsonProperty("weekday_hours")
        private WeekdayHours weekdayHours;

        @JsonProperty("weekend_hours")
        private WeekendHours weekendHours;

        @JsonProperty("holiday_coverage")
        @Builder.Default
        private boolean holidayCoverage = true;

        @JsonProperty("on_call_availability")
        @Builder.Default
        private boolean onCallAvailability = true;
    }

    /**
     * Weekday operating hours.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WeekdayHours {
        
        @JsonProperty("start_time")
        @Builder.Default
        private LocalTime startTime = LocalTime.of(7, 0); // 7:00 AM

        @JsonProperty("end_time")
        @Builder.Default
        private LocalTime endTime = LocalTime.of(17, 0); // 5:00 PM
    }

    /**
     * Weekend operating hours.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WeekendHours {
        
        @JsonProperty("saturday_hours")
        private WeekdayHours saturdayHours;

        @JsonProperty("sunday_hours")
        private WeekdayHours sundayHours;

        @JsonProperty("weekend_coverage")
        @Builder.Default
        private boolean weekendCoverage = true;
    }

    /**
     * Team capacity and workload management.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TeamCapacity {
        
        @JsonProperty("max_daily_visits")
        private Integer maxDailyVisits;

        @JsonProperty("max_patients_per_staff")
        private Integer maxPatientsPerStaff;

        @JsonProperty("emergency_capacity_reserve")
        private Double emergencyCapacityReserve; // percentage

        @JsonProperty("workload_distribution")
        private Map<String, Integer> workloadDistribution; // staff_id -> max visits
    }
}
