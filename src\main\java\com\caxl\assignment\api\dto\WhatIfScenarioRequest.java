package com.caxl.assignment.api.dto;

import com.caxl.assignment.domain.events.SchedulingEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Request DTO for what-if scenario analysis.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhatIfScenarioRequest {
    private String scenarioName;
    private String description;
    private List<SchedulingEvent> changes;
    private Map<String, Object> parameters;
    private boolean compareWithBaseline;
    private int maxSolvingTimeSeconds;
}
