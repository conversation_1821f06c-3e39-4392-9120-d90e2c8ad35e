package com.caxl.assignment.application.services.common;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Common service result classes to standardize responses across all services
 * and eliminate duplicate result handling patterns.
 */
public class ServiceResults {
    
    /**
     * Base service result with common fields and methods.
     */
    @Data
    @RequiredArgsConstructor
    public static abstract class BaseServiceResult {
        private final boolean success;
        private final List<String> errors;
        private final LocalDateTime timestamp;
        private final Map<String, Object> metadata;
        
        protected BaseServiceResult(boolean success, List<String> errors) {
            this.success = success;
            this.errors = errors != null ? errors : List.of();
            this.timestamp = LocalDateTime.now();
            this.metadata = Map.of();
        }
        
        protected BaseServiceResult(boolean success, List<String> errors, Map<String, Object> metadata) {
            this.success = success;
            this.errors = errors != null ? errors : List.of();
            this.timestamp = LocalDateTime.now();
            this.metadata = metadata != null ? metadata : Map.of();
        }
        
        public boolean hasErrors() {
            return !errors.isEmpty();
        }
        
        public String getFirstError() {
            return errors.isEmpty() ? null : errors.get(0);
        }
        
        public Optional<Object> getMetadata(String key) {
            return Optional.ofNullable(metadata.get(key));
        }
    }
    
    /**
     * Generic service result for operations that return data.
     */
    @Data
    public static class ServiceResult<T> extends BaseServiceResult {
        private final T data;
        
        private ServiceResult(boolean success, List<String> errors, T data, Map<String, Object> metadata) {
            super(success, errors, metadata);
            this.data = data;
        }
        
        public static <T> ServiceResult<T> success(T data) {
            return new ServiceResult<>(true, List.of(), data, Map.of());
        }
        
        public static <T> ServiceResult<T> success(T data, Map<String, Object> metadata) {
            return new ServiceResult<>(true, List.of(), data, metadata);
        }
        
        public static <T> ServiceResult<T> failure(List<String> errors) {
            return new ServiceResult<>(false, errors, null, Map.of());
        }
        
        public static <T> ServiceResult<T> failure(String error) {
            return new ServiceResult<>(false, List.of(error), null, Map.of());
        }
        
        public static <T> ServiceResult<T> failure(List<String> errors, Map<String, Object> metadata) {
            return new ServiceResult<>(false, errors, null, metadata);
        }
        
        public Optional<T> getData() {
            return Optional.ofNullable(data);
        }
    }
    
    /**
     * Scheduling-specific result with common scheduling metadata.
     */
    @Data
    public static class SchedulingResult extends BaseServiceResult {
        private final Object schedule;
        private final int totalVisits;
        private final int assignedVisits;
        private final int unassignedVisits;
        private final String optimizationId;
        
        private SchedulingResult(boolean success, List<String> errors, Object schedule, 
                               int totalVisits, int assignedVisits, int unassignedVisits, 
                               String optimizationId, Map<String, Object> metadata) {
            super(success, errors, metadata);
            this.schedule = schedule;
            this.totalVisits = totalVisits;
            this.assignedVisits = assignedVisits;
            this.unassignedVisits = unassignedVisits;
            this.optimizationId = optimizationId;
        }
        
        public static SchedulingResult success(Object schedule, int totalVisits, int assignedVisits, String optimizationId) {
            return new SchedulingResult(true, List.of(), schedule, totalVisits, assignedVisits, 
                                      totalVisits - assignedVisits, optimizationId, Map.of());
        }
        
        public static SchedulingResult success(Object schedule, int totalVisits, int assignedVisits, 
                                             String optimizationId, Map<String, Object> metadata) {
            return new SchedulingResult(true, List.of(), schedule, totalVisits, assignedVisits, 
                                      totalVisits - assignedVisits, optimizationId, metadata);
        }
        
        public static SchedulingResult failed(List<String> errors) {
            return new SchedulingResult(false, errors, null, 0, 0, 0, null, Map.of());
        }
        
        public static SchedulingResult failed(String error) {
            return new SchedulingResult(false, List.of(error), null, 0, 0, 0, null, Map.of());
        }
        
        public double getAssignmentRate() {
            return totalVisits > 0 ? (double) assignedVisits / totalVisits : 0.0;
        }
        
        public boolean isOptimal() {
            return success && unassignedVisits == 0;
        }
    }
    
    /**
     * Validation-specific result.
     */
    @Data
    public static class ValidationResult extends BaseServiceResult {
        private final Map<String, List<String>> fieldErrors;
        
        private ValidationResult(boolean success, List<String> errors, Map<String, List<String>> fieldErrors) {
            super(success, errors);
            this.fieldErrors = fieldErrors != null ? fieldErrors : Map.of();
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, List.of(), Map.of());
        }
        
        public static ValidationResult invalid(List<String> errors) {
            return new ValidationResult(false, errors, Map.of());
        }
        
        public static ValidationResult invalid(String error) {
            return new ValidationResult(false, List.of(error), Map.of());
        }
        
        public static ValidationResult invalid(Map<String, List<String>> fieldErrors) {
            List<String> allErrors = fieldErrors.values().stream()
                .flatMap(List::stream)
                .toList();
            return new ValidationResult(false, allErrors, fieldErrors);
        }
        
        public boolean hasFieldErrors() {
            return !fieldErrors.isEmpty();
        }
        
        public List<String> getFieldErrors(String field) {
            return fieldErrors.getOrDefault(field, List.of());
        }
    }
    
    /**
     * Operation result for simple success/failure operations.
     */
    @Data
    public static class OperationResult extends BaseServiceResult {
        private final String operationId;
        private final String message;
        
        private OperationResult(boolean success, List<String> errors, String operationId, String message) {
            super(success, errors);
            this.operationId = operationId;
            this.message = message;
        }
        
        public static OperationResult success(String message) {
            return new OperationResult(true, List.of(), null, message);
        }
        
        public static OperationResult success(String operationId, String message) {
            return new OperationResult(true, List.of(), operationId, message);
        }
        
        public static OperationResult failure(String error) {
            return new OperationResult(false, List.of(error), null, null);
        }
        
        public static OperationResult failure(List<String> errors) {
            return new OperationResult(false, errors, null, null);
        }
        
        public static OperationResult failure(String operationId, List<String> errors) {
            return new OperationResult(false, errors, operationId, null);
        }
    }
}
