<?xml version="1.0" encoding="UTF-8"?>
<solver xmlns="https://timefold.ai/xsd/solver" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="https://timefold.ai/xsd/solver https://timefold.ai/xsd/solver/solver.xsd">

    <!-- Enhanced domain model configuration -->
    <solutionClass>com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.EnhancedAssignmentSolution</solutionClass>
    <entityClass>com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.Shift</entityClass>

    <!-- Enhanced score configuration -->
    <scoreDirectorFactory>
        <constraintProviderClass>com.caxl.assignment.infrastructure.optaplanner.constraints.enhanced.EnhancedConstraintProvider</constraintProviderClass>
    </scoreDirectorFactory>

    <!-- Multi-stage termination configuration -->
    <termination>
        <terminationCompositionStyle>OR</terminationCompositionStyle>
        <!-- Stop after 5 minutes for production -->
        <secondsSpentLimit>300</secondsSpentLimit>
        <!-- Stop if no improvement for 60 seconds -->
        <unimprovedSecondsSpentLimit>60</unimprovedSecondsSpentLimit>
        <!-- Stop when optimal solution is found -->
        <bestScoreLimit>0hard/*soft</bestScoreLimit>
    </termination>

    <!-- Multi-phase optimization strategy -->
    
    <!-- Phase 1: Construction Heuristic - Build initial solution -->
    <constructionHeuristic>
        <constructionHeuristicType>FIRST_FIT_DECREASING</constructionHeuristicType>
        <forager>
            <pickEarlyType>FIRST_LAST_STEP_SCORE_IMPROVING</pickEarlyType>
        </forager>
    </constructionHeuristic>

    <!-- Phase 2: Local Search with advanced move selectors -->
    <localSearch>
        <termination>
            <secondsSpentLimit>180</secondsSpentLimit>
        </termination>
        
        <!-- Advanced move selector configuration -->
        <unionMoveSelector>
            <!-- Basic moves -->
            <changeMoveSelector>
                <filterClass>com.caxl.assignment.infrastructure.optaplanner.moves.SkillCompatibleMoveFilter</filterClass>
            </changeMoveSelector>
            
            <!-- Swap moves for workload balancing -->
            <swapMoveSelector>
                <entitySelector>
                    <cacheType>PHASE</cacheType>
                    <selectionOrder>SORTED</selectionOrder>
                    <sorterManner>DECREASING_DIFFICULTY</sorterManner>
                </entitySelector>
            </swapMoveSelector>
            
            <!-- List change moves for visit sequencing -->
            <listChangeMoveSelector>
                <valueSelector>
                    <cacheType>PHASE</cacheType>
                    <selectionOrder>SORTED</selectionOrder>
                    <sorterManner>DECREASING_DIFFICULTY</sorterManner>
                </valueSelector>
            </listChangeMoveSelector>
            
            <!-- List swap moves for visit reordering -->
            <listSwapMoveSelector/>
            
            <!-- Pillar moves for related entity optimization -->
            <pillarChangeMoveSelector>
                <pillarSelector>
                    <entitySelector>
                        <cacheType>PHASE</cacheType>
                    </entitySelector>
                    <minimumSubPillarSize>2</minimumSubPillarSize>
                    <maximumSubPillarSize>4</maximumSubPillarSize>
                </pillarSelector>
            </pillarChangeMoveSelector>
        </unionMoveSelector>
        
        <!-- Advanced acceptor configuration -->
        <acceptor>
            <entityTabuSize>7</entityTabuSize>
            <valueTabuSize>3</valueTabuSize>
            <moveTabuSize>5</moveTabuSize>
        </acceptor>
        
        <!-- Forager configuration for move selection -->
        <forager>
            <acceptedCountLimit>2000</acceptedCountLimit>
            <finalistPodiumType>STRATEGIC_OSCILLATION</finalistPodiumType>
        </forager>
    </localSearch>

    <!-- Phase 3: Late Acceptance Hill Climbing for fine-tuning -->
    <localSearch>
        <termination>
            <secondsSpentLimit>60</secondsSpentLimit>
        </termination>
        
        <unionMoveSelector>
            <changeMoveSelector/>
            <swapMoveSelector/>
            <listChangeMoveSelector/>
        </unionMoveSelector>
        
        <acceptor>
            <lateAcceptanceSize>400</lateAcceptanceSize>
        </acceptor>
        
        <forager>
            <acceptedCountLimit>1</acceptedCountLimit>
        </forager>
    </localSearch>

    <!-- Phase 4: Simulated Annealing for final optimization -->
    <localSearch>
        <termination>
            <secondsSpentLimit>60</secondsSpentLimit>
        </termination>
        
        <unionMoveSelector>
            <changeMoveSelector/>
            <swapMoveSelector/>
        </unionMoveSelector>
        
        <acceptor>
            <simulatedAnnealingStartingTemperature>2hard/200soft</simulatedAnnealingStartingTemperature>
        </acceptor>
        
        <forager>
            <acceptedCountLimit>1</acceptedCountLimit>
        </forager>
    </localSearch>

</solver>
