package com.caxl.carestaff.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * JPA entity for match suggestion storage.
 * Maps to MATCH_SUGGESTION table.
 */
@Entity
@Table(name = "match_suggestion")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchSuggestionEntity {

    @Id
    @Column(name = "suggestion_id")
    private UUID suggestionId;

    @Column(name = "service_request_id", nullable = false)
    private UUID serviceRequestId;

    @Column(name = "suggested_carestaff_id", nullable = false)
    private UUID suggestedCareStaffId;

    @Column(name = "score", nullable = false)
    private Double score;

    @Column(name = "rationale", columnDefinition = "TEXT")
    private String rationale;

    @Column(name = "suggested_datetime")
    private LocalDateTime suggestedDateTime;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    @PrePersist
    protected void onCreate() {
        if (suggestionId == null) {
            suggestionId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
    }
}
