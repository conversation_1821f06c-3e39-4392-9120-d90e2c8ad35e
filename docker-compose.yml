version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: caxl-postgres
    environment:
      POSTGRES_DB: homecare_scheduling
      POSTGRES_USER: caxl_user
      POSTGRES_PASSWORD: caxl_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - caxl-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U caxl_user -d homecare_scheduling"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: caxl-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - caxl-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # CAXL Homecare Scheduling Service
  caxl-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: caxl-homecare-service
    environment:
      # Database Configuration
      SPRING_DATASOURCE_URL: ***************************************************
      SPRING_DATASOURCE_USERNAME: caxl_user
      SPRING_DATASOURCE_PASSWORD: caxl_password
      SPRING_JPA_HIBERNATE_DDL_AUTO: update
      SPRING_JPA_SHOW_SQL: false
      SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: org.hibernate.dialect.PostgreSQLDialect
      
      # Redis Configuration
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_TIMEOUT: 2000ms
      
      # Timefold Configuration
      TIMEFOLD_SOLVER_TERMINATION_SPENT_LIMIT: 30s
      TIMEFOLD_SOLVER_TERMINATION_UNIMPROVED_SPENT_LIMIT: 15s
      
      # Application Configuration
      SPRING_PROFILES_ACTIVE: docker
      SERVER_PORT: 8080
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: always
      
      # Logging Configuration
      LOGGING_LEVEL_COM_CAXL: INFO
      LOGGING_LEVEL_AI_TIMEFOLD: INFO
      LOGGING_PATTERN_CONSOLE: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
      
      # Security Configuration
      SPRING_SECURITY_USER_NAME: admin
      SPRING_SECURITY_USER_PASSWORD: admin123
      
      # Homecare Specific Configuration
      HOMECARE_DEFAULT_MAX_TRAVEL_DISTANCE: 25
      HOMECARE_DEFAULT_BREAK_DURATION_MINUTES: 30
      HOMECARE_STATE_COMPLIANCE_ENABLED: true
      HOMECARE_GEOFENCING_ENABLED: true
      
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - caxl-network
    volumes:
      - app_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Prometheus for Metrics Collection
  prometheus:
    image: prom/prometheus:latest
    container_name: caxl-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - caxl-network

  # Grafana for Monitoring Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: caxl-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    networks:
      - caxl-network
    depends_on:
      - prometheus

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: caxl-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    networks:
      - caxl-network
    depends_on:
      - caxl-app

networks:
  caxl-network:
    driver: bridge
    name: caxl-homecare-network

volumes:
  postgres_data:
    name: caxl-postgres-data
  redis_data:
    name: caxl-redis-data
  app_logs:
    name: caxl-app-logs
  prometheus_data:
    name: caxl-prometheus-data
  grafana_data:
    name: caxl-grafana-data
