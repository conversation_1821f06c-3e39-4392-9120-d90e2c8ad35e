package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import static org.junit.jupiter.api.Assertions.*;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;

/**
 * Comprehensive test suite for geofencing constraints in homecare scheduling.
 * Tests that care staff operate within their designated geographic zones.
 */
@DisplayName("Geofencing Constraint Tests")
class GeofencingConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Geofencing Validation")
    class BasicGeofencingValidation {

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitWithinStaffOperatingZone_shouldPass")
        void testGeofencingConstraint_whenVisitWithinStaffOperatingZone_shouldPass() {
            // Given: Care staff with specific operating zone (34.0-34.5, -118.5--118.0)
            List<CareStaff.OperatingZone> zones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.5, -118.0)
            );
            CareStaff zoneStaff = testDataFactory.createCareStaffWithZones("staff1", zones);
            
            // And: Visit within the operating zone
            HomecareVisit visitInZone = testDataFactory.createVisitAtLocation("visit1", 34.2, -118.3);
            visitInZone.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (visit is within staff zone)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visitInZone)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitOutsideStaffOperatingZone_shouldFail")
        void testGeofencingConstraint_whenVisitOutsideStaffOperatingZone_shouldFail() {
            // Given: Care staff with specific operating zone (34.0-34.5, -118.5--118.0)
            List<CareStaff.OperatingZone> zones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.5, -118.0)
            );
            CareStaff zoneStaff = testDataFactory.createCareStaffWithZones("staff1", zones);
            
            // And: Visit outside the operating zone
            HomecareVisit visitOutsideZone = testDataFactory.createVisitAtLocation("visit1", 35.0, -119.0);
            visitOutsideZone.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (visit is outside staff zone)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visitOutsideZone)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitUnassigned_shouldPass")
        void testGeofencingConstraint_whenVisitUnassigned_shouldPass() {
            // Given: Unassigned visit anywhere
            HomecareVisit unassignedVisit = testDataFactory.createVisitAtLocation("visit1", 35.0, -119.0);
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (constraint only applies to assigned visits)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(unassignedVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multiple Zone Scenarios")
    class MultipleZoneScenarios {

        @Test
        @DisplayName("testGeofencingConstraint_whenStaffHasMultipleZones_shouldPassForAnyZone")
        void testGeofencingConstraint_whenStaffHasMultipleZones_shouldPassForAnyZone() {
            // Given: Care staff with multiple operating zones
            List<CareStaff.OperatingZone> multipleZones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.5, -118.0), // Los Angeles area
                createOperatingZone("zone2", 33.5, -117.8, 34.0, -117.3)  // Orange County area
            );
            CareStaff multiZoneStaff = testDataFactory.createCareStaffWithZones("staff1", multipleZones);
            
            // And: Visit in second zone
            HomecareVisit visitInZone2 = testDataFactory.createVisitAtLocation("visit1", 33.7, -117.5);
            visitInZone2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (visit is within one of the staff zones)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visitInZone2)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitBetweenMultipleZones_shouldFail")
        void testGeofencingConstraint_whenVisitBetweenMultipleZones_shouldFail() {
            // Given: Care staff with two separate zones
            List<CareStaff.OperatingZone> separateZones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.2, -118.3),
                createOperatingZone("zone2", 34.3, -118.2, 34.5, -118.0)
            );
            CareStaff multiZoneStaff = testDataFactory.createCareStaffWithZones("staff1", separateZones);
            
            // And: Visit in the gap between zones
            HomecareVisit visitInGap = testDataFactory.createVisitAtLocation("visit1", 34.25, -118.25);
            visitInGap.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (visit is not within any zone)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visitInGap)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenMultipleStaffWithDifferentZones_shouldEvaluateIndependently")
        void testGeofencingConstraint_whenMultipleStaffWithDifferentZones_shouldEvaluateIndependently() {
            // Given: Two staff with different zones
            CareStaff northStaff = testDataFactory.createCareStaffWithZones("northStaff", 
                List.of(createOperatingZone("north", 34.3, -118.5, 34.5, -118.0))
            );
            
            CareStaff southStaff = testDataFactory.createCareStaffWithZones("southStaff", 
                List.of(createOperatingZone("south", 34.0, -118.5, 34.2, -118.0))
            );
            
            // And: Visits assigned appropriately
            HomecareVisit northVisit = testDataFactory.createVisitAtLocation("northVisit", 34.4, -118.2);
            northVisit.setAssignedCareStaffId("northStaff");
            
            HomecareVisit southVisit = testDataFactory.createVisitAtLocation("southVisit", 34.1, -118.2);
            southVisit.setAssignedCareStaffId("southStaff");
            
            // When: Verifying the constraint
            // Then: Both should pass (each visit is within respective staff zone)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(northVisit, southVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Boundary Conditions")
    class EdgeCasesAndBoundaryConditions {

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitExactlyOnZoneBoundary_shouldPass")
        void testGeofencingConstraint_whenVisitExactlyOnZoneBoundary_shouldPass() {
            // Given: Care staff with operating zone
            List<CareStaff.OperatingZone> zones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.5, -118.0)
            );
            CareStaff boundaryStaff = testDataFactory.createCareStaffWithZones("staff1", zones);
            
            // And: Visit exactly on zone boundary
            HomecareVisit boundaryVisit = testDataFactory.createVisitAtLocation("visit1", 34.0, -118.0);
            boundaryVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (boundary is inclusive)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(boundaryVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenVisitJustOutsideZoneBoundary_shouldFail")
        void testGeofencingConstraint_whenVisitJustOutsideZoneBoundary_shouldFail() {
            // Given: Care staff with operating zone
            List<CareStaff.OperatingZone> zones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.5, -118.0)
            );
            CareStaff boundaryStaff = testDataFactory.createCareStaffWithZones("staff1", zones);
            
            // And: Visit just outside zone boundary
            HomecareVisit outsideVisit = testDataFactory.createVisitAtLocation("visit1", 33.999, -117.999);
            outsideVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (just outside boundary)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(outsideVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenStaffHasNoOperatingZones_shouldFail")
        void testGeofencingConstraint_whenStaffHasNoOperatingZones_shouldFail() {
            // Given: Care staff with no operating zones (administrative error)
            CareStaff noZoneStaff = testDataFactory.createCareStaffWithZones("staff1", List.of());
            
            // And: Any visit assigned to this staff
            HomecareVisit anyVisit = testDataFactory.createVisitAtLocation("visit1", 34.2, -118.3);
            anyVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (no valid operating zones)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(anyVisit)
                    .penalizesBy(1);
        }
    }

    @Nested
    @DisplayName("Complex Geographic Scenarios")
    class ComplexGeographicScenarios {

        @Test
        @DisplayName("testGeofencingConstraint_whenStaffAssignedToMultipleVisitsInDifferentZones_shouldValidateAll")
        void testGeofencingConstraint_whenStaffAssignedToMultipleVisitsInDifferentZones_shouldValidateAll() {
            // Given: Staff with multiple zones
            List<CareStaff.OperatingZone> multipleZones = List.of(
                createOperatingZone("zone1", 34.0, -118.5, 34.2, -118.3),
                createOperatingZone("zone2", 34.3, -118.2, 34.5, -118.0)
            );
            CareStaff multiZoneStaff = testDataFactory.createCareStaffWithZones("staff1", multipleZones);
            
            // And: Multiple visits in different zones
            HomecareVisit visitZone1 = testDataFactory.createVisitAtLocation("visit1", 34.1, -118.4);
            visitZone1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visitZone2 = testDataFactory.createVisitAtLocation("visit2", 34.4, -118.1);
            visitZone2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visitOutside = testDataFactory.createVisitAtLocation("visit3", 35.0, -119.0);
            visitOutside.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail only for the visit outside zones
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visitZone1, visitZone2, visitOutside)
                    .penalizesBy(1); // Only visitOutside should fail
        }

        @Test
        @DisplayName("testGeofencingConstraint_whenOverlappingZonesBetweenStaff_shouldRespectIndividualAssignments")
        void testGeofencingConstraint_whenOverlappingZonesBetweenStaff_shouldRespectIndividualAssignments() {
            // Given: Two staff with overlapping zones
            CareStaff staff1 = testDataFactory.createCareStaffWithZones("staff1", 
                List.of(createOperatingZone("zone1", 34.0, -118.5, 34.3, -118.2))
            );
            
            CareStaff staff2 = testDataFactory.createCareStaffWithZones("staff2", 
                List.of(createOperatingZone("zone2", 34.2, -118.3, 34.5, -118.0))
            );
            
            // And: Visit in overlapping area assigned to both staff separately
            HomecareVisit visit1ForStaff1 = testDataFactory.createVisitAtLocation("visit1", 34.25, -118.25);
            visit1ForStaff1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2ForStaff2 = testDataFactory.createVisitAtLocation("visit2", 34.25, -118.25);
            visit2ForStaff2.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Both should pass (overlapping area is valid for both)
            constraintVerifier.verifyThat((provider, factory) -> provider.geographicZoneCompliance(factory))
                    .given(visit1ForStaff1, visit2ForStaff2)
                    .penalizesBy(0);
        }
    }

    // Helper method to create operating zones
    private CareStaff.OperatingZone createOperatingZone(String zoneId, double minLat, double minLng, double maxLat, double maxLng) {
        CareStaff.ZoneBoundaries boundaries = CareStaff.ZoneBoundaries.builder()
                .coordinates(List.of(
                    new double[]{minLat, minLng},
                    new double[]{minLat, maxLng},
                    new double[]{maxLat, maxLng},
                    new double[]{maxLat, minLng},
                    new double[]{minLat, minLng} // Close the polygon
                ))
                .build();
                
        return CareStaff.OperatingZone.builder()
                .zoneId(zoneId)
                .zoneName("Test Zone " + zoneId)
                .state("CA")
                .boundaries(boundaries)
                .isActive(true)
                .build();
    }
}
