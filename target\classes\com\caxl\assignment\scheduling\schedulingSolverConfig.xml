<?xml version="1.0" encoding="UTF-8"?>
<solver xmlns="https://timefold.ai/xsd/solver" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="https://timefold.ai/xsd/solver https://timefold.ai/xsd/solver/solver.xsd">

    <!-- Domain model configuration -->
    <solutionClass>com.caxl.assignment.domain.scheduling.SchedulingSolution</solutionClass>
    <entityClass>com.caxl.assignment.domain.scheduling.PatientAssignment</entityClass>

    <!-- Score configuration using the new constraint provider -->
    <scoreDirectorFactory>
        <constraintProviderClass>com.caxl.assignment.domain.constraints.scheduling.SchedulingConstraintProvider</constraintProviderClass>
    </scoreDirectorFactory>

    <!-- Termination configuration -->
    <termination>
        <secondsSpentLimit>30</secondsSpentLimit>
        <unimprovedSecondsSpentLimit>10</unimprovedSecondsSpentLimit>
        <bestScoreLimit>0hard/-1000soft</bestScoreLimit>
    </termination>

    <!-- Construction heuristic phase -->
    <constructionHeuristic>
        <constructionHeuristicType>FIRST_FIT_DECREASING</constructionHeuristicType>
        <forager>
            <pickEarlyType>FIRST_LAST_STEP_SCORE_IMPROVING</pickEarlyType>
        </forager>
    </constructionHeuristic>

    <!-- Local search phase -->
    <localSearch>
        <unionMoveSelector>
            <!-- Change clinician assignment -->
            <changeMoveSelector>
                <valueSelector>
                    <variableName>assignedClinician</variableName>
                </valueSelector>
            </changeMoveSelector>
            
            <!-- Change time slot assignment -->
            <changeMoveSelector>
                <valueSelector>
                    <variableName>assignedTimeSlot</variableName>
                </valueSelector>
            </changeMoveSelector>
            
            <!-- Swap assignments between patients -->
            <swapMoveSelector>
                <variableNameInclude>assignedClinician</variableNameInclude>
                <variableNameInclude>assignedTimeSlot</variableNameInclude>
            </swapMoveSelector>
        </unionMoveSelector>
        
        <acceptor>
            <lateAcceptanceSize>400</lateAcceptanceSize>
        </acceptor>
        
        <forager>
            <acceptedCountLimit>1</acceptedCountLimit>
        </forager>
    </localSearch>

</solver>
