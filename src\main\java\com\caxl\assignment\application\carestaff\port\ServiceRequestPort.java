package com.caxl.assignment.application.carestaff.port;

import com.caxl.assignment.domain.entities.ServiceRequest;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Port interface for service request persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface ServiceRequestPort {

    /**
     * Find service request by ID with related entities loaded.
     * 
     * @param requestId Service request ID
     * @return Service request if exists
     */
    Optional<ServiceRequest> findByIdWithRelations(UUID requestId);

    /**
     * Update service request status.
     * 
     * @param requestId Service request ID
     * @param status New status
     */
    void updateStatus(UUID requestId, String status);

    /**
     * Update multiple service request statuses.
     * 
     * @param requestIds List of service request IDs
     * @param status New status
     */
    void updateStatus(List<UUID> requestIds, String status);

    /**
     * Save service request.
     * 
     * @param serviceRequest Service request to save
     * @return Saved service request
     */
    ServiceRequest save(ServiceRequest serviceRequest);
}

