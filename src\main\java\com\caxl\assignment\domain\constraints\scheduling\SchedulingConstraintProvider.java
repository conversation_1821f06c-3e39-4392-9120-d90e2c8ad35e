package com.caxl.assignment.domain.constraints.scheduling;

import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import ai.timefold.solver.core.api.score.stream.Constraint;
import ai.timefold.solver.core.api.score.stream.ConstraintCollectors;
import ai.timefold.solver.core.api.score.stream.ConstraintFactory;
import ai.timefold.solver.core.api.score.stream.ConstraintProvider;
import ai.timefold.solver.core.api.score.stream.Joiners;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import com.caxl.assignment.domain.scheduling.PatientAssignment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Comprehensive constraint provider for patient-clinician scheduling optimization.
 * Implements both hard constraints (feasibility) and soft constraints (optimization goals).
 * 
 * This provider works with the actual domain model structure and provides
 * realistic constraints for healthcare scheduling scenarios.
 */
@Component
@Slf4j
public class SchedulingConstraintProvider implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory factory) {
        return new Constraint[]{
            // === HARD CONSTRAINTS (Feasibility) ===
            clinicianAvailabilityConstraint(factory),
            skillRequirementConstraint(factory),
            timeSlotCapacityConstraint(factory),
            clinicianWorkloadConstraint(factory),
            oneAssignmentPerPatientConstraint(factory),
            
            // === SOFT CONSTRAINTS (Optimization) ===
            balanceWorkloadConstraint(factory),
            minimizeTravelTimeConstraint(factory),
            continuityOfCareConstraint(factory),
            priorityOptimizationConstraint(factory),
            utilizationOptimizationConstraint(factory),
            geographicCohesionConstraint(factory)
        };
    }

    // =========================================================================
    // HARD CONSTRAINTS - Core Feasibility Requirements
    // =========================================================================

    /**
     * Clinician must be available during the assigned time slot.
     */
    private Constraint clinicianAvailabilityConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> !isClinicianAvailable(assignment))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Clinician availability");
    }

    /**
     * Clinician must have all required skills for the patient.
     */
    private Constraint skillRequirementConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> !hasRequiredSkills(assignment))
                .penalize(HardSoftScore.ONE_HARD)
                .asConstraint("Skill requirements");
    }

    /**
     * Time slot cannot exceed its capacity.
     */
    private Constraint timeSlotCapacityConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getAssignedTimeSlot() != null)
                .groupBy(PatientAssignment::getAssignedTimeSlot, ConstraintCollectors.count())
                .filter((timeSlot, count) -> count > timeSlot.getCapacity())
                .penalize(HardSoftScore.ONE_HARD, 
                        (timeSlot, count) -> count - timeSlot.getCapacity())
                .asConstraint("Time slot capacity");
    }

    /**
     * Clinician cannot exceed maximum daily appointments.
     */
    private Constraint clinicianWorkloadConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .groupBy(PatientAssignment::getAssignedClinician,
                        PatientAssignment::getAssignmentDate,
                        ConstraintCollectors.count())
                .filter((clinician, date, count) -> count > clinician.getMaxDailyAppointments())
                .penalize(HardSoftScore.ONE_HARD,
                        (clinician, date, count) -> count - clinician.getMaxDailyAppointments())
                .asConstraint("Clinician workload limit");
    }

    /**
     * Each patient can only have one assignment.
     */
    private Constraint oneAssignmentPerPatientConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .groupBy(PatientAssignment::getPatient, ConstraintCollectors.count())
                .filter((patient, count) -> count > 1)
                .penalize(HardSoftScore.ONE_HARD, (patient, count) -> count - 1)
                .asConstraint("One assignment per patient");
    }

    // =========================================================================
    // SOFT CONSTRAINTS - Optimization Objectives
    // =========================================================================

    /**
     * Balance workload among clinicians.
     */
    private Constraint balanceWorkloadConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .groupBy(PatientAssignment::getAssignedClinician, ConstraintCollectors.count())
                .penalize(HardSoftScore.ONE_SOFT,
                        (clinician, count) -> calculateWorkloadImbalance(count))
                .asConstraint("Balance workload");
    }

    /**
     * Minimize travel time between consecutive assignments.
     */
    private Constraint minimizeTravelTimeConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .join(PatientAssignment.class,
                        Joiners.equal(PatientAssignment::getAssignedClinician),
                        Joiners.equal(PatientAssignment::getAssignmentDate))
                .filter((assignment1, assignment2) -> 
                    assignment1.getAssignedTimeSlot() != null &&
                    assignment2.getAssignedTimeSlot() != null &&
                    isConsecutiveTimeSlots(assignment1, assignment2))
                .penalize(HardSoftScore.ONE_SOFT,
                        (assignment1, assignment2) -> calculateTravelPenalty(assignment1, assignment2))
                .asConstraint("Minimize travel time");
    }

    /**
     * Promote continuity of care by matching patient preferences.
     */
    private Constraint continuityOfCareConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> isPreferredClinician(assignment))
                .reward(HardSoftScore.ONE_SOFT, assignment -> 100)
                .asConstraint("Continuity of care");
    }

    /**
     * Prioritize high-priority patients.
     */
    private Constraint priorityOptimizationConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .reward(HardSoftScore.ONE_SOFT,
                        assignment -> getPriorityWeight(assignment.getPriority()))
                .asConstraint("Priority optimization");
    }

    /**
     * Maximize clinician utilization.
     */
    private Constraint utilizationOptimizationConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .groupBy(PatientAssignment::getAssignedClinician, ConstraintCollectors.count())
                .reward(HardSoftScore.ONE_SOFT, (clinician, count) -> count * 10)
                .asConstraint("Utilization optimization");
    }

    /**
     * Keep assignments within the same geographic zone when possible.
     */
    private Constraint geographicCohesionConstraint(ConstraintFactory factory) {
        return factory.forEach(PatientAssignment.class)
                .join(PatientAssignment.class,
                        Joiners.equal(PatientAssignment::getAssignedClinician),
                        Joiners.equal(PatientAssignment::getAssignmentDate))
                .filter((assignment1, assignment2) -> 
                    assignment1.getGeographicZone() != null &&
                    assignment2.getGeographicZone() != null &&
                    assignment1.getGeographicZone().equals(assignment2.getGeographicZone()))
                .reward(HardSoftScore.ONE_SOFT, (assignment1, assignment2) -> 50)
                .asConstraint("Geographic cohesion");
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    /**
     * Check if clinician is available for the assignment.
     */
    private boolean isClinicianAvailable(PatientAssignment assignment) {
        if (assignment.getAssignedClinician() == null || assignment.getAssignedTimeSlot() == null) {
            return false;
        }
        
        return assignment.getAssignedClinician().getAvailableSlots().stream()
                .anyMatch(slot -> isTimeSlotWithinAvailability(assignment.getAssignedTimeSlot(), slot));
    }

    /**
     * Check if clinician has all required skills.
     */
    private boolean hasRequiredSkills(PatientAssignment assignment) {
        if (assignment.getAssignedClinician() == null || assignment.getPatient() == null) {
            return false;
        }
        
        Set<String> clinicianSkills = new HashSet<>(assignment.getAssignedClinician().getSkills());
        Set<String> requiredSkills = new HashSet<>(assignment.getPatient().getRequiredSkills());
        
        return clinicianSkills.containsAll(requiredSkills);
    }

    /**
     * Check if time slot is within clinician's availability.
     */
    private boolean isTimeSlotWithinAvailability(PatientAssignment.TimeSlot timeSlot, 
                                               Clinician.AvailabilitySlot availabilitySlot) {
        return !timeSlot.getStartTime().isBefore(availabilitySlot.getStartTime()) &&
               !timeSlot.getEndTime().isAfter(availabilitySlot.getEndTime());
    }

    /**
     * Check if two assignments have consecutive time slots.
     */
    private boolean isConsecutiveTimeSlots(PatientAssignment assignment1, PatientAssignment assignment2) {
        PatientAssignment.TimeSlot slot1 = assignment1.getAssignedTimeSlot();
        PatientAssignment.TimeSlot slot2 = assignment2.getAssignedTimeSlot();
        
        return slot1.getEndTime().equals(slot2.getStartTime()) || 
               slot2.getEndTime().equals(slot1.getStartTime());
    }

    /**
     * Calculate workload imbalance penalty.
     */
    private int calculateWorkloadImbalance(int assignmentCount) {
        int idealWorkload = 6; // Target assignments per clinician
        return Math.abs(assignmentCount - idealWorkload);
    }

    /**
     * Calculate travel penalty between assignments.
     */
    private int calculateTravelPenalty(PatientAssignment assignment1, PatientAssignment assignment2) {
        // Simplified travel penalty - in real implementation, use actual distances
        if (assignment1.getGeographicZone() != null && assignment2.getGeographicZone() != null) {
            return assignment1.getGeographicZone().equals(assignment2.getGeographicZone()) ? 0 : 100;
        }
        return 50; // Default penalty for unknown zones
    }

    /**
     * Check if clinician is preferred by patient.
     */
    private boolean isPreferredClinician(PatientAssignment assignment) {
        if (assignment.getAssignedClinician() == null || assignment.getPatient() == null) {
            return false;
        }
        
        // Check if patient has preferences and clinician matches
        Patient.PatientPreferences preferences = assignment.getPatient().getPreferences();
        if (preferences != null && preferences.getPreferredClinicians() != null) {
            return preferences.getPreferredClinicians().contains(assignment.getAssignedClinician().getId());
        }
        
        return false;
    }

    /**
     * Get priority weight for optimization.
     */
    private int getPriorityWeight(int priority) {
        return switch (priority) {
            case 1 -> 1000; // Highest priority
            case 2 -> 500;
            case 3 -> 200;
            case 4 -> 100;
            case 5 -> 50;   // Lowest priority
            default -> 0;
        };
    }
}
