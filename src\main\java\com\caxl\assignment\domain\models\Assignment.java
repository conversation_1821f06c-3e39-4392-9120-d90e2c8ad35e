package com.caxl.assignment.domain.models;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Assignment domain model for the clinician assignment system.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Assignment {

    @NotBlank(message = "Assignment ID is required")
    private String id;

    @NotBlank(message = "Patient ID is required")
    @JsonProperty("patient_id")
    private String patientId;

    @NotBlank(message = "Clinician ID is required")
    @JsonProperty("clinician_id")
    private String clinicianId;

    @NotBlank(message = "Visit date is required")
    @JsonProperty("visit_date")
    private String visitDate; // ISO format date

    @NotBlank(message = "Start time is required")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "Start time must be in HH:MM format")
    @JsonProperty("start_time")
    private String startTime; // HH:MM format

    @NotBlank(message = "End time is required")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "End time must be in HH:MM format")
    @JsonProperty("end_time")
    private String endTime; // HH:MM format

    @NotNull(message = "Assignment status is required")
    @Builder.Default
    private AssignmentStatus status = AssignmentStatus.PENDING;

    @NotNull(message = "Created at timestamp is required")
    @JsonProperty("created_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @NotNull(message = "Updated at timestamp is required")
    @JsonProperty("updated_at")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @Valid
    private AssignmentRationale rationale;

    @Min(value = 1, message = "Workload points must be at least 1")
    @JsonProperty("workload_points")
    @Builder.Default
    private int workloadPoints = 1;

    /**
     * Enum for assignment status.
     */
    public enum AssignmentStatus {
        PENDING("pending"),
        CONFIRMED("confirmed"),
        REJECTED("rejected"),
        RESCHEDULED("rescheduled");

        private final String value;

        AssignmentStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Enum for relaxation strategies.
     */
    public enum RelaxationStrategy {
        NONE("none"),
        SKILL_SUBSTITUTION("skill_substitution"),
        EXTENDED_SERVICE_RADIUS("extended_service_radius"),
        OVERTIME_CAPACITY("overtime_capacity"),
        VISIT_RESCHEDULING("visit_rescheduling");

        private final String value;

        RelaxationStrategy(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Rationale for an assignment decision.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AssignmentRationale {

        @JsonProperty("hard_rules_satisfied")
        @Builder.Default
        private List<String> hardRulesSatisfied = List.of();

        @JsonProperty("soft_rules_satisfied")
        @Builder.Default
        private List<String> softRulesSatisfied = List.of();

        @JsonProperty("soft_rules_not_satisfied")
        @Builder.Default
        private List<String> softRulesNotSatisfied = List.of();

        @JsonProperty("total_score")
        @Builder.Default
        private double totalScore = 0.0;

        @JsonProperty("relaxation_applied")
        @Builder.Default
        private boolean relaxationApplied = false;

        @JsonProperty("relaxation_strategy")
        private RelaxationStrategy relaxationStrategy;

        @JsonProperty("relaxation_details")
        private Map<String, Object> relaxationDetails;
    }
}
