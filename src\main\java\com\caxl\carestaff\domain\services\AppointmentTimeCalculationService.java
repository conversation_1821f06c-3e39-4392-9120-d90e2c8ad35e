package com.caxl.carestaff.domain.services;

import com.caxl.carestaff.domain.entities.ServiceRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Domain service for calculating optimal appointment times based on various factors.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentTimeCalculationService {

    private final JdbcTemplate jdbcTemplate;

    /**
     * Calculate optimal appointment time for a service request and care staff.
     */
    public AppointmentTimeResult calculateOptimalAppointmentTime(
            ServiceRequest serviceRequest,
            UUID careStaffId,
            LocalDateTime suggestedDateTime) {

        log.debug("Calculating optimal appointment time for request {} and staff {}", 
                 serviceRequest.getRequestId(), careStaffId);

        ServiceRequest.TimeWindow timeWindow = serviceRequest.getTimeWindow();
        if (timeWindow == null) {
            return new AppointmentTimeResult(
                    suggestedDateTime,
                    suggestedDateTime.plusMinutes(60), // Default 1 hour
                    "No time window specified, using suggested time"
            );
        }

        // 1. Start with the suggested time or preferred time
        LocalDateTime optimalStart = determineOptimalStartTime(serviceRequest, careStaffId, suggestedDateTime);

        // 2. Calculate duration based on visit type and patient needs
        int optimalDuration = calculateOptimalDuration(serviceRequest);

        // 3. Adjust for staff schedule optimization
        AppointmentTimeAdjustment adjustment = adjustForStaffSchedule(careStaffId, optimalStart, optimalDuration);
        optimalStart = adjustment.adjustedStartTime();
        optimalDuration = adjustment.adjustedDuration();

        // 4. Validate against time window constraints
        TimeWindowValidation validation = validateAgainstTimeWindow(optimalStart, optimalDuration, timeWindow);
        if (!validation.isValid()) {
            optimalStart = validation.correctedStartTime();
            optimalDuration = validation.correctedDuration();
        }

        LocalDateTime optimalEnd = optimalStart.plusMinutes(optimalDuration);

        String rationale = buildRationale(serviceRequest, optimalStart, optimalDuration, adjustment, validation);

        log.info("Calculated optimal appointment time for request {}: {} - {} ({}min)", 
                serviceRequest.getRequestId(), optimalStart, optimalEnd, optimalDuration);

        return new AppointmentTimeResult(optimalStart, optimalEnd, rationale);
    }

    /**
     * Determine optimal start time considering preferences and constraints.
     */
    private LocalDateTime determineOptimalStartTime(ServiceRequest serviceRequest, 
                                                   UUID careStaffId, 
                                                   LocalDateTime suggestedDateTime) {
        
        ServiceRequest.TimeWindow timeWindow = serviceRequest.getTimeWindow();
        
        // Priority order:
        // 1. Preferred start time (if within window)
        if (timeWindow.getPreferredStartTime() != null &&
            isWithinTimeWindow(timeWindow.getPreferredStartTime(), timeWindow)) {
            return timeWindow.getPreferredStartTime();
        }

        // 2. Suggested time (if within window)
        if (suggestedDateTime != null && isWithinTimeWindow(suggestedDateTime, timeWindow)) {
            return suggestedDateTime;
        }

        // 3. Optimal time based on staff schedule
        LocalDateTime staffOptimalTime = findStaffOptimalTime(careStaffId, timeWindow);
        if (staffOptimalTime != null) {
            return staffOptimalTime;
        }

        // 4. Default to arrival window start
        return timeWindow.getArrivalWindowStart();
    }

    /**
     * Calculate optimal duration based on visit type and complexity.
     */
    private int calculateOptimalDuration(ServiceRequest serviceRequest) {
        int baseDuration = serviceRequest.getTimeWindow().getVisitDurationMinutes();
        
        // Adjust based on visit type
        int adjustedDuration = switch (serviceRequest.getVisitType().toLowerCase()) {
            case "routine_checkup" -> Math.max(baseDuration, 45);
            case "wound_care" -> Math.max(baseDuration, 60);
            case "medication_management" -> Math.max(baseDuration, 30);
            case "post_surgical" -> Math.max(baseDuration, 90);
            case "physical_therapy" -> Math.max(baseDuration, 60);
            case "mental_health" -> Math.max(baseDuration, 50);
            default -> baseDuration;
        };

        // Adjust based on priority (high priority gets more time)
        if (serviceRequest.getPriority() <= 2) {
            adjustedDuration += 15; // Extra 15 minutes for high priority
        }

        // Adjust based on workload points (complex cases get more time)
        if (serviceRequest.getWorkloadPoints() >= 4) {
            adjustedDuration += 20; // Extra 20 minutes for complex cases
        }

        // Add buffer for special instructions
        if (serviceRequest.getSpecialInstructions() != null && 
            !serviceRequest.getSpecialInstructions().trim().isEmpty()) {
            adjustedDuration += 10; // Extra 10 minutes for special instructions
        }

        return Math.min(adjustedDuration, 180); // Cap at 3 hours
    }

    /**
     * Adjust appointment time for staff schedule optimization.
     */
    private AppointmentTimeAdjustment adjustForStaffSchedule(UUID careStaffId, 
                                                           LocalDateTime startTime, 
                                                           int duration) {
        
        // Get staff's appointments for the day
        List<Map<String, Object>> dayAppointments = getStaffDayAppointments(careStaffId, startTime.toLocalDate());
        
        LocalDateTime adjustedStart = startTime;
        int adjustedDuration = duration;
        StringBuilder adjustmentReason = new StringBuilder();

        // 1. Check for travel time optimization
        TravelTimeOptimization travelOpt = optimizeForTravelTime(careStaffId, startTime, dayAppointments);
        if (travelOpt.hasAdjustment()) {
            adjustedStart = travelOpt.adjustedTime();
            adjustmentReason.append("Travel optimization: ").append(travelOpt.reason()).append("; ");
        }

        // 2. Check for workload balancing
        WorkloadBalancing workloadOpt = balanceWorkload(careStaffId, adjustedStart, duration, dayAppointments);
        if (workloadOpt.hasAdjustment()) {
            adjustedStart = workloadOpt.adjustedTime();
            adjustedDuration = workloadOpt.adjustedDuration();
            adjustmentReason.append("Workload balancing: ").append(workloadOpt.reason()).append("; ");
        }

        // 3. Check for break time optimization
        BreakTimeOptimization breakOpt = optimizeForBreaks(careStaffId, adjustedStart, adjustedDuration, dayAppointments);
        if (breakOpt.hasAdjustment()) {
            adjustedStart = breakOpt.adjustedTime();
            adjustmentReason.append("Break optimization: ").append(breakOpt.reason()).append("; ");
        }

        return new AppointmentTimeAdjustment(
                adjustedStart,
                adjustedDuration,
                !adjustedStart.equals(startTime) || adjustedDuration != duration,
                adjustmentReason.toString().trim()
        );
    }

    /**
     * Validate appointment time against service request time window.
     */
    private TimeWindowValidation validateAgainstTimeWindow(LocalDateTime startTime, 
                                                          int duration, 
                                                          ServiceRequest.TimeWindow timeWindow) {
        
        LocalDateTime endTime = startTime.plusMinutes(duration);
        boolean isValid = true;
        LocalDateTime correctedStart = startTime;
        int correctedDuration = duration;
        StringBuilder validationIssues = new StringBuilder();

        // Check if start time is before arrival window
        if (startTime.isBefore(timeWindow.getArrivalWindowStart())) {
            correctedStart = timeWindow.getArrivalWindowStart();
            isValid = false;
            validationIssues.append("Start time moved to arrival window start; ");
        }

        // Check if start time is after arrival window
        if (startTime.isAfter(timeWindow.getArrivalWindowEnd())) {
            correctedStart = timeWindow.getArrivalWindowEnd().minusMinutes(duration);
            isValid = false;
            validationIssues.append("Start time moved to fit within arrival window; ");
        }

        // Check if end time exceeds latest end time
        LocalDateTime correctedEnd = correctedStart.plusMinutes(correctedDuration);
        if (timeWindow.getLatestEndTime() != null && correctedEnd.isAfter(timeWindow.getLatestEndTime())) {
            correctedEnd = timeWindow.getLatestEndTime();
            correctedDuration = (int) java.time.Duration.between(correctedStart, correctedEnd).toMinutes();
            isValid = false;
            validationIssues.append("Duration reduced to fit latest end time; ");
        }

        return new TimeWindowValidation(
                isValid,
                correctedStart,
                correctedDuration,
                validationIssues.toString().trim()
        );
    }

    /**
     * Build rationale string explaining the appointment time calculation.
     */
    private String buildRationale(ServiceRequest serviceRequest,
                                 LocalDateTime optimalStart,
                                 int optimalDuration,
                                 AppointmentTimeAdjustment adjustment,
                                 TimeWindowValidation validation) {
        
        StringBuilder rationale = new StringBuilder();
        
        rationale.append(String.format("Appointment: %s (%dmin) - ", 
                optimalStart.toLocalTime(), optimalDuration));
        
        rationale.append(String.format("Visit type: %s; ", serviceRequest.getVisitType()));
        
        if (serviceRequest.getPriority() <= 2) {
            rationale.append("High priority (+15min); ");
        }
        
        if (serviceRequest.getWorkloadPoints() >= 4) {
            rationale.append("Complex case (+20min); ");
        }
        
        if (adjustment.hasAdjustment()) {
            rationale.append("Schedule adjustments: ").append(adjustment.reason()).append("; ");
        }
        
        if (!validation.isValid()) {
            rationale.append("Time window corrections: ").append(validation.issues()).append("; ");
        }
        
        return rationale.toString().trim();
    }

    // Helper methods
    private boolean isWithinTimeWindow(LocalDateTime time, ServiceRequest.TimeWindow timeWindow) {
        return !time.isBefore(timeWindow.getArrivalWindowStart()) && 
               !time.isAfter(timeWindow.getArrivalWindowEnd());
    }

    private LocalDateTime findStaffOptimalTime(UUID careStaffId, ServiceRequest.TimeWindow timeWindow) {
        // Find gaps in staff schedule that fit the time window
        List<Map<String, Object>> appointments = getStaffDayAppointments(careStaffId, timeWindow.getArrivalWindowStart().toLocalDate());
        
        // Simple implementation: find first available slot
        LocalDateTime searchStart = timeWindow.getArrivalWindowStart();
        LocalDateTime searchEnd = timeWindow.getArrivalWindowEnd();
        
        for (LocalDateTime current = searchStart; current.isBefore(searchEnd); current = current.plusMinutes(30)) {
            if (isTimeSlotAvailable(current, timeWindow.getVisitDurationMinutes(), appointments)) {
                return current;
            }
        }
        
        return null;
    }

    private List<Map<String, Object>> getStaffDayAppointments(UUID careStaffId, java.time.LocalDate date) {
        String sql = """
            SELECT appointment_id, scheduled_start_time, scheduled_end_time
            FROM appointment
            WHERE carestaff_id = ?
            AND DATE(scheduled_start_time) = ?
            AND status IN ('scheduled', 'in_progress')
            ORDER BY scheduled_start_time
            """;
        
        return jdbcTemplate.queryForList(sql, careStaffId, date);
    }

    private boolean isTimeSlotAvailable(LocalDateTime startTime, int durationMinutes, List<Map<String, Object>> appointments) {
        LocalDateTime endTime = startTime.plusMinutes(durationMinutes);
        
        return appointments.stream().noneMatch(apt -> {
            LocalDateTime aptStart = ((java.sql.Timestamp) apt.get("scheduled_start_time")).toLocalDateTime();
            LocalDateTime aptEnd = ((java.sql.Timestamp) apt.get("scheduled_end_time")).toLocalDateTime();
            
            return startTime.isBefore(aptEnd) && endTime.isAfter(aptStart);
        });
    }

    private TravelTimeOptimization optimizeForTravelTime(UUID careStaffId, LocalDateTime startTime, List<Map<String, Object>> appointments) {
        // Simplified travel time optimization
        return new TravelTimeOptimization(false, startTime, "No travel optimization needed");
    }

    private WorkloadBalancing balanceWorkload(UUID careStaffId, LocalDateTime startTime, int duration, List<Map<String, Object>> appointments) {
        // Simplified workload balancing
        return new WorkloadBalancing(false, startTime, duration, "Workload is balanced");
    }

    private BreakTimeOptimization optimizeForBreaks(UUID careStaffId, LocalDateTime startTime, int duration, List<Map<String, Object>> appointments) {
        // Check if appointment conflicts with lunch break (12:00-13:00)
        LocalTime appointmentTime = startTime.toLocalTime();
        LocalTime appointmentEnd = startTime.plusMinutes(duration).toLocalTime();
        
        if ((appointmentTime.isBefore(LocalTime.of(13, 0)) && appointmentEnd.isAfter(LocalTime.of(12, 0)))) {
            // Move appointment to after lunch
            LocalDateTime adjustedTime = startTime.withHour(13).withMinute(0);
            return new BreakTimeOptimization(true, adjustedTime, "Moved to avoid lunch break");
        }
        
        return new BreakTimeOptimization(false, startTime, "No break conflicts");
    }

    // Record classes
    public record AppointmentTimeResult(LocalDateTime startTime, LocalDateTime endTime, String rationale) {}
    
    private record AppointmentTimeAdjustment(LocalDateTime adjustedStartTime, int adjustedDuration, boolean hasAdjustment, String reason) {}
    private record TimeWindowValidation(boolean isValid, LocalDateTime correctedStartTime, int correctedDuration, String issues) {}
    private record TravelTimeOptimization(boolean hasAdjustment, LocalDateTime adjustedTime, String reason) {}
    private record WorkloadBalancing(boolean hasAdjustment, LocalDateTime adjustedTime, int adjustedDuration, String reason) {}
    private record BreakTimeOptimization(boolean hasAdjustment, LocalDateTime adjustedTime, String reason) {}
}
