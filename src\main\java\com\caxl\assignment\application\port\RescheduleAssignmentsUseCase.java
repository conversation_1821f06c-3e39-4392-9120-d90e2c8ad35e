package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Assignment;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Use case for handling rescheduling due to changes, cancellations, or new requests.
 * Maintains optimal scheduling when disruptions occur.
 */
public interface RescheduleAssignmentsUseCase {

    /**
     * Reschedule assignments due to carestaff unavailability.
     * 
     * @param unavailableClinicianId Clinician who became unavailable
     * @param affectedDate Date of unavailability
     * @param reason Reason for unavailability
     * @return Rescheduling result
     */
    ReschedulingResult rescheduleForUnavailableClinician(
        UUID unavailableClinicianId,
        LocalDate affectedDate,
        String reason
    );

    /**
     * Reschedule assignments due to patient request changes.
     * 
     * @param changedPatientId Patient with changed requirements
     * @param affectedDate Date of change
     * @param changeDetails Details of the change
     * @return Rescheduling result
     */
    ReschedulingResult rescheduleForPatientChange(
        UUID changedPatientId,
        LocalDate affectedDate,
        PatientChangeDetails changeDetails
    );

    /**
     * Add new urgent patient to existing schedule.
     * 
     * @param urgentPatientId New urgent patient
     * @param requestedDate Requested service date
     * @param urgencyLevel Urgency level (1-5, 5 being highest)
     * @return Rescheduling result
     */
    ReschedulingResult addUrgentPatientToSchedule(
        UUID urgentPatientId,
        LocalDate requestedDate,
        int urgencyLevel
    );

    /**
     * Optimize existing schedule without major disruptions.
     * 
     * @param schedulingDate Date to optimize
     * @param maxDisruptions Maximum number of assignment changes allowed
     * @return Optimization result
     */
    ReschedulingResult optimizeExistingSchedule(
        LocalDate schedulingDate,
        int maxDisruptions
    );

    /**
     * Details of patient requirement changes.
     */
    record PatientChangeDetails(
        List<String> newRequiredSkills,
        String newLocation,
        String newTimePreference,
        String changeReason
    ) {}

    /**
     * Result of rescheduling operation.
     */
    record ReschedulingResult(
        List<Assignment> newAssignments,
        List<Assignment> modifiedAssignments,
        List<Assignment> cancelledAssignments,
        ReschedulingMetrics metrics,
        boolean successful,
        String status,
        List<String> warnings
    ) {}

    /**
     * Rescheduling performance metrics.
     */
    record ReschedulingMetrics(
        long reschedulingTimeMs,
        int totalAffectedAssignments,
        int successfulReschedules,
        int failedReschedules,
        double impactScore,
        String strategy
    ) {}
}
