package com.caxl.assignment.application.services.healthcare;

import com.caxl.assignment.domain.models.healthcare.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * GeofencingService handles geographic zone management and validation
 * for care staff assignments within specific operating areas.
 */
@Service
public class GeofencingService {

    /**
     * Check if a care staff member can serve a patient based on geographic zones.
     */
    public boolean canServePatient(CareStaff careStaff, HomePatient patient) {
        if (careStaff.getOperatingZones() == null || careStaff.getOperatingZones().isEmpty()) {
            return false; // No operating zones defined
        }

        return careStaff.getOperatingZones().stream()
                .anyMatch(zone -> zone.contains(patient.getHomeAddress()));
    }    /**
     * Find the best geographic zone for a care staff member to serve a patient.
     */
    public Optional<CareStaff.OperatingZone> findBestZoneForPatient(CareStaff careStaff, HomePatient patient) {
        if (careStaff.getOperatingZones() == null) {
            return Optional.empty();
        }

        return careStaff.getOperatingZones().stream()
                .filter(zone -> zone.contains(patient.getHomeAddress()))
                .min((zone1, zone2) -> Double.compare(
                        calculateDistanceToZoneCenter(patient.getHomeAddress(), zone1),
                        calculateDistanceToZoneCenter(patient.getHomeAddress(), zone2)
                ));
    }

    /**
     * Get all care staff members who can serve a specific patient.
     */
    public List<CareStaff> findEligibleStaffForPatient(List<CareStaff> allStaff, HomePatient patient) {
        return allStaff.stream()
                .filter(staff -> canServePatient(staff, patient))
                .collect(Collectors.toList());
    }

    /**
     * Calculate travel distance between two geographic points.
     */
    public double calculateTravelDistance(GeoZone.GeoPoint from, GeoZone.GeoPoint to) {
        return from.distanceTo(to);
    }

    /**
     * Calculate estimated travel time between two points (assumes 30 mph average).
     */
    public int calculateTravelTimeMinutes(GeoZone.GeoPoint from, GeoZone.GeoPoint to) {
        double distanceMiles = calculateTravelDistance(from, to);
        double averageSpeedMph = 30.0; // Average speed in urban areas
        double timeHours = distanceMiles / averageSpeedMph;
        return (int) Math.ceil(timeHours * 60); // Convert to minutes
    }

    /**
     * Check if a proposed visit is within the maximum travel distance for care staff.
     */
    public boolean isWithinTravelLimit(CareStaff careStaff, HomePatient patient) {
        double distance = calculateTravelDistance(careStaff.getHomeLocation(), patient.getHomeAddress());
        return distance <= careStaff.getMaxTravelDistanceMiles();
    }

    /**
     * Find the optimal care staff for a patient based on proximity and zone coverage.
     */
    public Optional<CareStaff> findOptimalStaffForPatient(List<CareStaff> eligibleStaff, HomePatient patient) {
        return eligibleStaff.stream()
                .filter(staff -> canServePatient(staff, patient))
                .min((staff1, staff2) -> {
                    double distance1 = calculateTravelDistance(staff1.getHomeLocation(), patient.getHomeAddress());
                    double distance2 = calculateTravelDistance(staff2.getHomeLocation(), patient.getHomeAddress());
                    return Double.compare(distance1, distance2);
                });
    }

    /**
     * Validate that all visits for a care staff member are within their operating zones.
     */
    public GeofencingValidationResult validateStaffAssignments(CareStaff careStaff, List<HomecareVisit> visits) {
        List<String> violations = new java.util.ArrayList<>();
        
        for (HomecareVisit visit : visits) {
            if (!Objects.equals(visit.getAssignedCareStaffId(), careStaff.getId())) {
                continue; // Skip visits not assigned to this staff member
            }
            
            // This would require patient lookup - simplified for example
            boolean isWithinZone = careStaff.getOperatingZones().stream()
                    .anyMatch(zone -> isVisitWithinZone(visit, zone));
            
            if (!isWithinZone) {
                violations.add("Visit " + visit.getVisitId() + " is outside operating zones");
            }
        }
        
        return new GeofencingValidationResult(violations.isEmpty(), violations);
    }

    /**
     * Create a geographic zone with boundary points.
     */
    public GeoZone createGeoZone(String zoneId, String zoneName, String state, 
                                List<GeoZone.GeoPoint> boundaryPoints) {
        return GeoZone.builder()
                .zoneId(zoneId)
                .zoneName(zoneName)
                .state(state)
                .boundaryPoints(boundaryPoints)
                .zoneType(determineZoneType(boundaryPoints))
                .coverageRadiusMiles(calculateCoverageRadius(boundaryPoints))
                .build();
    }

    /**
     * Check if two zones overlap.
     */
    public boolean doZonesOverlap(GeoZone zone1, GeoZone zone2) {
        // Simplified overlap detection - check if any boundary points of one zone are within the other
        return zone1.getBoundaryPoints().stream()
                .anyMatch(point -> zone2.contains(point)) ||
               zone2.getBoundaryPoints().stream()
                .anyMatch(point -> zone1.contains(point));
    }

    /**
     * Get zone utilization statistics.
     */
    public ZoneUtilizationStats getZoneUtilization(GeoZone zone, List<CareStaff> allStaff, 
                                                  List<HomecareVisit> allVisits) {
        int staffCount = (int) allStaff.stream()
                .filter(staff -> staff.getOperatingZones().contains(zone))
                .count();
        
        int visitCount = (int) allVisits.stream()
                .filter(visit -> isVisitInZone(visit, zone))
                .count();
        
        double utilizationRate = staffCount > 0 ? (double) visitCount / staffCount : 0.0;
        
        return new ZoneUtilizationStats(zone.getZoneId(), staffCount, visitCount, utilizationRate);
    }

    // === HELPER METHODS ===

    private double calculateDistanceToZoneCenter(GeoZone.GeoPoint point, CareStaff.OperatingZone zone) {
        GeoZone.GeoPoint center = zone.getCenterPoint();
        return center != null ? point.distanceTo(center) : Double.MAX_VALUE;
    }

    private boolean isVisitWithinZone(HomecareVisit visit, CareStaff.OperatingZone zone) {
        // Simplified - would require patient address lookup
        return true; // Placeholder
    }

    private boolean isVisitInZone(HomecareVisit visit, GeoZone zone) {
        // Simplified - would require patient address lookup
        return true; // Placeholder
    }

    private GeoZone.ZoneType determineZoneType(List<GeoZone.GeoPoint> boundaryPoints) {
        // Simplified zone type determination based on area size
        double area = calculatePolygonArea(boundaryPoints);
        if (area < 10) return GeoZone.ZoneType.URBAN;
        if (area < 50) return GeoZone.ZoneType.SUBURBAN;
        return GeoZone.ZoneType.RURAL;
    }

    private double calculateCoverageRadius(List<GeoZone.GeoPoint> boundaryPoints) {
        if (boundaryPoints.size() < 2) return 0.0;
        
        // Find the maximum distance between any two points
        double maxDistance = 0.0;
        for (int i = 0; i < boundaryPoints.size(); i++) {
            for (int j = i + 1; j < boundaryPoints.size(); j++) {
                double distance = boundaryPoints.get(i).distanceTo(boundaryPoints.get(j));
                maxDistance = Math.max(maxDistance, distance);
            }
        }
        return maxDistance / 2; // Radius is half the diameter
    }

    private double calculatePolygonArea(List<GeoZone.GeoPoint> points) {
        // Simplified area calculation using shoelace formula
        if (points.size() < 3) return 0.0;
        
        double area = 0.0;
        int n = points.size();
        
        for (int i = 0; i < n; i++) {
            int j = (i + 1) % n;
            area += points.get(i).getLongitude() * points.get(j).getLatitude();
            area -= points.get(j).getLongitude() * points.get(i).getLatitude();
        }
        
        return Math.abs(area) / 2.0;
    }

    // === RESULT CLASSES ===

    public static class GeofencingValidationResult {
        private final boolean isValid;
        private final List<String> violations;

        public GeofencingValidationResult(boolean isValid, List<String> violations) {
            this.isValid = isValid;
            this.violations = violations;
        }

        public boolean isValid() { return isValid; }
        public List<String> getViolations() { return violations; }
    }

    public static class ZoneUtilizationStats {
        private final String zoneId;
        private final int staffCount;
        private final int visitCount;
        private final double utilizationRate;

        public ZoneUtilizationStats(String zoneId, int staffCount, int visitCount, double utilizationRate) {
            this.zoneId = zoneId;
            this.staffCount = staffCount;
            this.visitCount = visitCount;
            this.utilizationRate = utilizationRate;
        }

        public String getZoneId() { return zoneId; }
        public int getStaffCount() { return staffCount; }
        public int getVisitCount() { return visitCount; }
        public double getUtilizationRate() { return utilizationRate; }
    }
}
