# CareStaff Matching Service

## Overview

The CareStaff Matching Service is a configurable, geo-filtered & constraint-aware carestaff matching system built using hexagonal architecture with programmed heuristics. It intelligently matches available carestaff to incoming service requests for home-based healthcare based on configurable hard and soft constraints.

## Architecture

### Hexagonal Architecture (Ports and Adapters)

The system follows strict hexagonal architecture principles:

```
src/main/java/com/caxl/carestaff/
├── domain/                     # Domain Layer (Framework-agnostic)
│   ├── entities/              # Domain Entities
│   ├── valueobjects/          # Value Objects (MatchingCriteria)
│   └── exceptions/            # Custom Domain Exceptions
├── application/               # Application Layer
│   ├── port/
│   │   ├── in/               # Driven Ports (Use Case Interfaces)
│   │   └── out/              # Driving Ports
│   │       └── persistence/  # Persistence Ports
│   └── service/              # Use Case Implementations
└── infrastructure/           # Infrastructure Layer
    ├── api/                  # Spring Web Adapter
    │   ├── dto/             # Request/Response DTOs
    │   └── exceptionhandler/ # Global Exception Handler
    └── persistence/         # JPA/Hibernate Adapter
        ├── entity/          # JPA Entities
        ├── repository/      # Spring Data JPA Repositories
        └── adapter/         # Port Implementations
```

## Core Features

### 1. Configurable Constraint System

The matching algorithm uses JSON-based configuration stored in the database:

- **Hard Constraints (Filters)**: Must be satisfied for a match to be considered
  - Skill matching requirements
  - Geographic service area compliance
  - Availability checking
  - Barred staff filtering
  - Minimum score thresholds

- **Soft Constraints (Scoring Weights)**: Used for optimization and ranking
  - Skill match bonuses
  - Proximity penalties
  - Preferred staff bonuses
  - Continuity of care bonuses
  - Language matching bonuses
  - Experience level bonuses

### 2. Spatial/Geographic Filtering

- Uses PostGIS and Hibernate Spatial for geographic operations
- ST_DWithin for proximity searches
- ST_Contains/ST_Intersects for geofence validation
- Configurable search radius and service area types

### 3. Heuristic Matching Algorithm

The service implements a sophisticated heuristic algorithm that:
1. Finds initial candidates using proximity and skill filters
2. Applies hard constraint filters iteratively
3. Calculates scores based on soft constraint weights
4. Returns ranked suggestions above minimum threshold

## API Endpoints

### 1. Suggest Matches
```http
POST /api/v1/service-requests/{request_id}/suggest-matches
```
Generates match suggestions for a service request using configurable constraints.

### 2. Override Match
```http
POST /api/v1/service-requests/{request_id}/override-match
Content-Type: application/json

{
  "selected_carestaff_id": "uuid",
  "reason": "Manual selection",
  "explanation": "Patient specifically requested this caregiver"
}
```

### 3. Bulk Assignment
```http
POST /api/v1/matches/bulk-assign
Content-Type: application/json

{
  "suggestion_ids": ["uuid1", "uuid2", "uuid3"],
  "scheduler_id": "uuid"
}
```

### 4. Retrieve Suggestions
```http
GET /api/v1/service-requests/{request_id}/suggestions
```

## Database Schema

### Core Tables

- `matching_configuration` - Stores JSON-based constraint configurations
- `service_request` - Service requests requiring carestaff assignment
- `match_suggestion` - Generated match suggestions with scores
- `match_override` - Manual override records
- `appointment` - Scheduled appointments
- `carestaff_has_skill` - Many-to-many skill mappings

### Spatial Tables (Existing)

- `location` - Geographic locations with PostGIS Point/Polygon support
- `geofences` - Service area boundaries
- `caresstaff_pe` - Care staff with location references
- `patient` - Patients with location references

## Configuration

### Application Properties

```yaml
spring:
  datasource:
    url: ****************************************************
    username: caxl_user
    password: caxl_password
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.spatial.dialect.postgis.PostgisPG10Dialect

carestaff:
  matching:
    default-search-radius-km: 50.0
    min-score-threshold: 0.0
    overlap-threshold-minutes: 1
    geofence-strict-containment: true
```

### Sample Matching Configuration JSON

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 50.0,
    "mustRespectGeoServiceArea": true,
    "mustRespectAvailability": true,
    "mustNotBeBarred": true,
    "minScoreThreshold": 0.0
  },
  "scoring_weights": {
    "skillMatchBonusPerRequiredSkill": 10.0,
    "skillMatchAllBonus": 50.0,
    "proximityKmPenalty": -1.0,
    "geoServiceAreaBonus": 20.0,
    "preferredCareStaffBonus": 15.0,
    "continuityBonusPerRecentVisit": 5.0,
    "languageMatchBonus": 8.0,
    "experienceLevelBonusPerYear": 2.0
  },
  "geography": {
    "staffServiceGeofenceTypes": ["service_area", "county_area"],
    "geofenceStrictContainmentOnly": true
  },
  "availability": {
    "overlapThresholdMinutes": 1,
    "minTimeBeforeVisitMinutes": 30,
    "minTimeAfterVisitMinutes": 30
  }
}
```

## Running the Application

### Prerequisites

- Java 17+
- PostgreSQL with PostGIS extension
- Maven 3.6+

### Setup

1. Start PostgreSQL with PostGIS:
```bash
docker-compose up postgres
```

2. Build and run the application:
```bash
mvn clean install
mvn spring-boot:run
```

3. The application will start on port 8080 with sample data initialized.

## Testing Scenarios

The implementation supports comprehensive testing scenarios including:

- **Success Cases**: Valid matches with various constraint combinations
- **Hard Constraint Failures**: Missing skills, geographic restrictions, availability conflicts
- **Override Operations**: Manual caregiver selection with reason tracking
- **Bulk Operations**: Multiple assignment processing with conflict detection

## Future Enhancements

### TODO Items (Marked in Code)

- Refine skill extraction from patient/service request business rules
- Implement preferred carestaff checking logic
- Add continuity of care scoring based on recent visit history
- Implement language matching validation
- Add experience level scoring refinements
- Enhance availability window fit scoring
- Implement robust conflict detection for bulk assignments
- Add authentication and authorization
- Integrate with Timefold for optimal multi-request assignments

## Integration Notes

This service extends the existing CAXL assignment system and reuses:
- Existing domain models (Patient, Clinician, CareStaff)
- Database schema and spatial infrastructure
- Spring Boot configuration and dependencies
- Timefold integration patterns (for future enhancement)

The hexagonal architecture ensures clean separation of concerns and easy testability while avoiding code duplication with the existing system.
