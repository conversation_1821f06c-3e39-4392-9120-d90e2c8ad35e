package com.caxl.assignment.api.controllers;

import com.caxl.assignment.application.services.realtime.RealTimeSchedulingService;
import com.caxl.assignment.domain.events.SchedulingEvent;
import com.caxl.assignment.api.dto.EventResponse;
import com.caxl.assignment.api.dto.WhatIfScenarioRequest;
import com.caxl.assignment.api.dto.WhatIfScenarioResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * REST API controller for real-time scheduling events and what-if scenario planning.
 * Provides endpoints for dynamic schedule management and optimization.
 */
@RestController
@RequestMapping("/api/v1/scheduling/realtime")
@RequiredArgsConstructor
@Slf4j
public class RealTimeSchedulingController {

    private final RealTimeSchedulingService realTimeSchedulingService;

    /**
     * Submit a real-time scheduling event for immediate processing.
     */
    @PostMapping("/events")
    public ResponseEntity<EventResponse> submitEvent(@Valid @RequestBody SchedulingEvent event) {
        log.info("Received real-time scheduling event: {} for entity: {}", 
                event.getEventType(), event.getAffectedEntityId());

        try {
            CompletableFuture<Void> processingFuture = realTimeSchedulingService.handleEvent(event);
            
            EventResponse response = EventResponse.builder()
                    .eventId(event.getEventId())
                    .status("ACCEPTED")
                    .message("Event submitted for processing")
                    .processingStarted(true)
                    .requiresReoptimization(event.isRequiresImmediateReoptimization())
                    .build();

            return ResponseEntity.accepted().body(response);
        } catch (Exception e) {
            log.error("Failed to process scheduling event: {}", e.getMessage(), e);
            
            EventResponse errorResponse = EventResponse.builder()
                    .eventId(event.getEventId())
                    .status("ERROR")
                    .message("Failed to process event: " + e.getMessage())
                    .processingStarted(false)
                    .build();
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * Submit multiple events for batch processing.
     */
    @PostMapping("/events/batch")
    public ResponseEntity<List<EventResponse>> submitBatchEvents(
            @Valid @RequestBody List<SchedulingEvent> events) {
        log.info("Received batch of {} scheduling events", events.size());

        try {
            List<EventResponse> responses = events.stream()
                    .map(event -> {
                        try {
                            realTimeSchedulingService.handleEvent(event);
                            return EventResponse.builder()
                                    .eventId(event.getEventId())
                                    .status("ACCEPTED")
                                    .message("Event submitted for processing")
                                    .processingStarted(true)
                                    .build();
                        } catch (Exception e) {
                            return EventResponse.builder()
                                    .eventId(event.getEventId())
                                    .status("ERROR")
                                    .message("Failed to process event: " + e.getMessage())
                                    .processingStarted(false)
                                    .build();
                        }
                    })
                    .toList();

            return ResponseEntity.accepted().body(responses);
        } catch (Exception e) {
            log.error("Failed to process batch events: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Execute what-if scenario analysis for schedule planning.
     */
    @PostMapping("/what-if")
    public ResponseEntity<WhatIfScenarioResponse> executeWhatIfScenario(
            @Valid @RequestBody WhatIfScenarioRequest request) {
        log.info("Executing what-if scenario: {} with {} changes", 
                request.getScenarioName(), request.getChanges().size());

        try {
            WhatIfScenarioResponse response = realTimeSchedulingService.executeWhatIfScenario(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to execute what-if scenario: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get current optimization status and progress.
     */
    @GetMapping("/optimization/status")
    public ResponseEntity<OptimizationStatus> getOptimizationStatus() {
        try {
            OptimizationStatus status = realTimeSchedulingService.getCurrentOptimizationStatus();
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("Failed to get optimization status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Cancel current optimization process.
     */
    @PostMapping("/optimization/cancel")
    public ResponseEntity<Void> cancelOptimization() {
        try {
            realTimeSchedulingService.cancelCurrentOptimization();
            log.info("Optimization cancellation requested");
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Failed to cancel optimization: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Force immediate re-optimization with custom time limit.
     */
    @PostMapping("/optimization/force")
    public ResponseEntity<EventResponse> forceReoptimization(
            @RequestParam(defaultValue = "30") int timeLimitSeconds,
            @RequestParam(required = false) String reason) {
        try {
            log.info("Force re-optimization requested with {} seconds limit. Reason: {}", 
                    timeLimitSeconds, reason);
            
            CompletableFuture<Void> optimizationFuture = 
                realTimeSchedulingService.forceReoptimization(timeLimitSeconds, reason);
            
            EventResponse response = EventResponse.builder()
                    .eventId("FORCE_REOPT_" + System.currentTimeMillis())
                    .status("ACCEPTED")
                    .message("Force re-optimization started")
                    .processingStarted(true)
                    .requiresReoptimization(true)
                    .build();

            return ResponseEntity.accepted().body(response);
        } catch (Exception e) {
            log.error("Failed to force re-optimization: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get performance metrics for the scheduling system.
     */
    @GetMapping("/metrics")
    public ResponseEntity<SchedulingMetrics> getSchedulingMetrics() {
        try {
            SchedulingMetrics metrics = realTimeSchedulingService.getPerformanceMetrics();
            return ResponseEntity.ok(metrics);
        } catch (Exception e) {
            log.error("Failed to get scheduling metrics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check endpoint for real-time scheduling system.
     */
    @GetMapping("/health")
    public ResponseEntity<HealthStatus> getHealthStatus() {
        try {
            HealthStatus health = realTimeSchedulingService.getHealthStatus();
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            log.error("Health check failed: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // =========================================================================
    // RESPONSE DATA CLASSES
    // =========================================================================

    public static class OptimizationStatus {
        private boolean isRunning;
        private String currentPhase;
        private int progressPercentage;
        private long elapsedTimeMs;
        private String bestScoreSoFar;
        private int iterationsCompleted;

        // Constructors, getters, setters
        public OptimizationStatus() {}

        public OptimizationStatus(boolean isRunning, String currentPhase, int progressPercentage, 
                                long elapsedTimeMs, String bestScoreSoFar, int iterationsCompleted) {
            this.isRunning = isRunning;
            this.currentPhase = currentPhase;
            this.progressPercentage = progressPercentage;
            this.elapsedTimeMs = elapsedTimeMs;
            this.bestScoreSoFar = bestScoreSoFar;
            this.iterationsCompleted = iterationsCompleted;
        }

        // Getters and setters
        public boolean isRunning() { return isRunning; }
        public void setRunning(boolean running) { isRunning = running; }
        public String getCurrentPhase() { return currentPhase; }
        public void setCurrentPhase(String currentPhase) { this.currentPhase = currentPhase; }
        public int getProgressPercentage() { return progressPercentage; }
        public void setProgressPercentage(int progressPercentage) { this.progressPercentage = progressPercentage; }
        public long getElapsedTimeMs() { return elapsedTimeMs; }
        public void setElapsedTimeMs(long elapsedTimeMs) { this.elapsedTimeMs = elapsedTimeMs; }
        public String getBestScoreSoFar() { return bestScoreSoFar; }
        public void setBestScoreSoFar(String bestScoreSoFar) { this.bestScoreSoFar = bestScoreSoFar; }
        public int getIterationsCompleted() { return iterationsCompleted; }
        public void setIterationsCompleted(int iterationsCompleted) { this.iterationsCompleted = iterationsCompleted; }
    }

    public static class SchedulingMetrics {
        private int totalActiveAssignments;
        private int totalCareStaff;
        private int totalPatients;
        private double averageUtilization;
        private double averageScore;
        private int optimizationCount24h;
        private long averageOptimizationTimeMs;
        private int eventProcessingCount24h;

        // Constructors, getters, setters
        public SchedulingMetrics() {}

        // Getters and setters
        public int getTotalActiveAssignments() { return totalActiveAssignments; }
        public void setTotalActiveAssignments(int totalActiveAssignments) { this.totalActiveAssignments = totalActiveAssignments; }
        public int getTotalCareStaff() { return totalCareStaff; }
        public void setTotalCareStaff(int totalCareStaff) { this.totalCareStaff = totalCareStaff; }
        public int getTotalPatients() { return totalPatients; }
        public void setTotalPatients(int totalPatients) { this.totalPatients = totalPatients; }
        public double getAverageUtilization() { return averageUtilization; }
        public void setAverageUtilization(double averageUtilization) { this.averageUtilization = averageUtilization; }
        public double getAverageScore() { return averageScore; }
        public void setAverageScore(double averageScore) { this.averageScore = averageScore; }
        public int getOptimizationCount24h() { return optimizationCount24h; }
        public void setOptimizationCount24h(int optimizationCount24h) { this.optimizationCount24h = optimizationCount24h; }
        public long getAverageOptimizationTimeMs() { return averageOptimizationTimeMs; }
        public void setAverageOptimizationTimeMs(long averageOptimizationTimeMs) { this.averageOptimizationTimeMs = averageOptimizationTimeMs; }
        public int getEventProcessingCount24h() { return eventProcessingCount24h; }
        public void setEventProcessingCount24h(int eventProcessingCount24h) { this.eventProcessingCount24h = eventProcessingCount24h; }
    }

    public static class HealthStatus {
        private String status;
        private boolean solverHealthy;
        private boolean eventProcessingHealthy;
        private boolean trafficServiceHealthy;
        private long lastOptimizationMs;
        private String systemLoad;

        // Constructors, getters, setters
        public HealthStatus() {}

        public HealthStatus(String status, boolean solverHealthy, boolean eventProcessingHealthy, 
                          boolean trafficServiceHealthy, long lastOptimizationMs, String systemLoad) {
            this.status = status;
            this.solverHealthy = solverHealthy;
            this.eventProcessingHealthy = eventProcessingHealthy;
            this.trafficServiceHealthy = trafficServiceHealthy;
            this.lastOptimizationMs = lastOptimizationMs;
            this.systemLoad = systemLoad;
        }

        // Getters and setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public boolean isSolverHealthy() { return solverHealthy; }
        public void setSolverHealthy(boolean solverHealthy) { this.solverHealthy = solverHealthy; }
        public boolean isEventProcessingHealthy() { return eventProcessingHealthy; }
        public void setEventProcessingHealthy(boolean eventProcessingHealthy) { this.eventProcessingHealthy = eventProcessingHealthy; }
        public boolean isTrafficServiceHealthy() { return trafficServiceHealthy; }
        public void setTrafficServiceHealthy(boolean trafficServiceHealthy) { this.trafficServiceHealthy = trafficServiceHealthy; }
        public long getLastOptimizationMs() { return lastOptimizationMs; }
        public void setLastOptimizationMs(long lastOptimizationMs) { this.lastOptimizationMs = lastOptimizationMs; }
        public String getSystemLoad() { return systemLoad; }
        public void setSystemLoad(String systemLoad) { this.systemLoad = systemLoad; }
    }
}
