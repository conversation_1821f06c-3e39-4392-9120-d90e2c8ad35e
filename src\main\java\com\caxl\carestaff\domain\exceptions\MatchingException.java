package com.caxl.carestaff.domain.exceptions;

/**
 * Exception thrown when there are matching-related issues.
 */
public class MatchingException extends RuntimeException {

    public MatchingException(String message) {
        super(message);
    }

    public MatchingException(String message, Throwable cause) {
        super(message, cause);
    }

    public static MatchingException requestAlreadyScheduled(String requestId) {
        return new MatchingException("Service request is already scheduled: " + requestId);
    }

    public static MatchingException invalidRequestStatus(String requestId, String status) {
        return new MatchingException("Invalid request status for matching: " + status + " (request: " + requestId + ")");
    }
}
