package com.caxl.assignment.domain.constraints.scheduling;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.scheduling.PatientAssignment;
import com.caxl.assignment.domain.scheduling.SchedulingSolution;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test suite for SchedulingConstraintProvider.
 * Tests both hard and soft constraints with various scenarios.
 */
class SchedulingConstraintProviderTest {

    private ConstraintVerifier<SchedulingConstraintProvider, SchedulingSolution> constraintVerifier;
    
    private Clinician clinician1;
    private Clinician clinician2;
    private Patient patient1;
    private Patient patient2;
    private PatientAssignment.TimeSlot timeSlot1;
    private PatientAssignment.TimeSlot timeSlot2;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
            new SchedulingConstraintProvider(),
            SchedulingSolution.class,
            PatientAssignment.class
        );
        
        setupTestData();
    }

    private void setupTestData() {
        // Create test clinicians
        clinician1 = Clinician.builder()
            .id("CLIN001")
            .name("Dr. Smith")
            .skills(Arrays.asList("NURSING", "WOUND_CARE"))
            .maxDailyAppointments(8)
            .availableSlots(Arrays.asList(
                Clinician.AvailabilitySlot.builder()
                    .startTime(LocalTime.of(9, 0))
                    .endTime(LocalTime.of(17, 0))
                    .build()
            ))
            .build();

        clinician2 = Clinician.builder()
            .id("CLIN002")
            .name("Nurse Johnson")
            .skills(Arrays.asList("NURSING", "MEDICATION"))
            .maxDailyAppointments(6)
            .availableSlots(Arrays.asList(
                Clinician.AvailabilitySlot.builder()
                    .startTime(LocalTime.of(8, 0))
                    .endTime(LocalTime.of(16, 0))
                    .build()
            ))
            .build();

        // Create test patients
        patient1 = Patient.builder()
            .id("PAT001")
            .name("John Doe")
            .requiredSkills(Arrays.asList("NURSING"))
            .visitPriority(1)
            .preferences(Patient.PatientPreferences.builder()
                .preferredClinicians(Arrays.asList("CLIN001"))
                .build())
            .build();

        patient2 = Patient.builder()
            .id("PAT002")
            .name("Jane Smith")
            .requiredSkills(Arrays.asList("NURSING", "WOUND_CARE"))
            .visitPriority(3)
            .build();

        // Create test time slots
        timeSlot1 = PatientAssignment.TimeSlot.builder()
            .id("SLOT001")
            .startTime(LocalTime.of(10, 0))
            .endTime(LocalTime.of(11, 0))
            .capacity(2)
            .build();

        timeSlot2 = PatientAssignment.TimeSlot.builder()
            .id("SLOT002")
            .startTime(LocalTime.of(14, 0))
            .endTime(LocalTime.of(15, 0))
            .capacity(1)
            .build();
    }

    // =========================================================================
    // HARD CONSTRAINT TESTS
    // =========================================================================

    @Test
    void testClinicianAvailabilityConstraint_Available() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::clinicianAvailabilityConstraint)
            .given(assignment)
            .penalizesBy(0);
    }

    @Test
    void testClinicianAvailabilityConstraint_Unavailable() {
        // Time slot outside clinician availability
        PatientAssignment.TimeSlot unavailableSlot = PatientAssignment.TimeSlot.builder()
            .id("SLOT_UNAVAILABLE")
            .startTime(LocalTime.of(18, 0)) // After clinician availability
            .endTime(LocalTime.of(19, 0))
            .capacity(1)
            .build();
            
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, unavailableSlot);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::clinicianAvailabilityConstraint)
            .given(assignment)
            .penalizesBy(1);
    }

    @Test
    void testSkillRequirementConstraint_HasSkills() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::skillRequirementConstraint)
            .given(assignment)
            .penalizesBy(0);
    }

    @Test
    void testSkillRequirementConstraint_MissingSkills() {
        // Patient requires skills clinician doesn't have
        Patient patientWithSpecialSkills = Patient.builder()
            .id("PAT_SPECIAL")
            .name("Special Patient")
            .requiredSkills(Arrays.asList("NURSING", "PHYSICAL_THERAPY"))
            .visitPriority(1)
            .build();
            
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patientWithSpecialSkills, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::skillRequirementConstraint)
            .given(assignment)
            .penalizesBy(1);
    }

    @Test
    void testTimeSlotCapacityConstraint_WithinCapacity() {
        PatientAssignment assignment1 = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        PatientAssignment assignment2 = createAssignment("ASSIGN002", clinician2, patient2, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::timeSlotCapacityConstraint)
            .given(assignment1, assignment2)
            .penalizesBy(0);
    }

    @Test
    void testTimeSlotCapacityConstraint_ExceedsCapacity() {
        Patient patient3 = Patient.builder()
            .id("PAT003")
            .name("Third Patient")
            .requiredSkills(Arrays.asList("NURSING"))
            .visitPriority(2)
            .build();
            
        PatientAssignment assignment1 = createAssignment("ASSIGN001", clinician1, patient1, timeSlot2);
        PatientAssignment assignment2 = createAssignment("ASSIGN002", clinician2, patient2, timeSlot2);
        PatientAssignment assignment3 = createAssignment("ASSIGN003", clinician1, patient3, timeSlot2);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::timeSlotCapacityConstraint)
            .given(assignment1, assignment2, assignment3)
            .penalizesBy(2); // 3 assignments - 1 capacity = 2 violations
    }

    @Test
    void testClinicianWorkloadConstraint_WithinLimit() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::clinicianWorkloadConstraint)
            .given(assignment)
            .penalizesBy(0);
    }

    @Test
    void testOneAssignmentPerPatientConstraint_SingleAssignment() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::oneAssignmentPerPatientConstraint)
            .given(assignment)
            .penalizesBy(0);
    }

    @Test
    void testOneAssignmentPerPatientConstraint_MultipleAssignments() {
        PatientAssignment assignment1 = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        PatientAssignment assignment2 = createAssignment("ASSIGN002", clinician2, patient1, timeSlot2);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::oneAssignmentPerPatientConstraint)
            .given(assignment1, assignment2)
            .penalizesBy(1); // 2 assignments - 1 allowed = 1 violation
    }

    // =========================================================================
    // SOFT CONSTRAINT TESTS
    // =========================================================================

    @Test
    void testContinuityOfCareConstraint_PreferredClinician() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::continuityOfCareConstraint)
            .given(assignment)
            .rewardsWith(100);
    }

    @Test
    void testContinuityOfCareConstraint_NonPreferredClinician() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician2, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::continuityOfCareConstraint)
            .given(assignment)
            .rewardsWith(0);
    }

    @Test
    void testPriorityOptimizationConstraint_HighPriority() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::priorityOptimizationConstraint)
            .given(assignment)
            .rewardsWith(1000); // Priority 1 = 1000 points
    }

    @Test
    void testPriorityOptimizationConstraint_MediumPriority() {
        PatientAssignment assignment = createAssignment("ASSIGN001", clinician1, patient2, timeSlot1);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::priorityOptimizationConstraint)
            .given(assignment)
            .rewardsWith(200); // Priority 3 = 200 points
    }

    @Test
    void testUtilizationOptimizationConstraint() {
        PatientAssignment assignment1 = createAssignment("ASSIGN001", clinician1, patient1, timeSlot1);
        PatientAssignment assignment2 = createAssignment("ASSIGN002", clinician1, patient2, timeSlot2);
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::utilizationOptimizationConstraint)
            .given(assignment1, assignment2)
            .rewardsWith(20); // 2 assignments * 10 points each
    }

    @Test
    void testGeographicCohesionConstraint_SameZone() {
        PatientAssignment assignment1 = createAssignmentWithZone("ASSIGN001", clinician1, patient1, timeSlot1, "ZONE_A");
        PatientAssignment assignment2 = createAssignmentWithZone("ASSIGN002", clinician1, patient2, timeSlot2, "ZONE_A");
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::geographicCohesionConstraint)
            .given(assignment1, assignment2)
            .rewardsWith(50);
    }

    @Test
    void testGeographicCohesionConstraint_DifferentZones() {
        PatientAssignment assignment1 = createAssignmentWithZone("ASSIGN001", clinician1, patient1, timeSlot1, "ZONE_A");
        PatientAssignment assignment2 = createAssignmentWithZone("ASSIGN002", clinician1, patient2, timeSlot2, "ZONE_B");
        
        constraintVerifier.verifyThat(SchedulingConstraintProvider::geographicCohesionConstraint)
            .given(assignment1, assignment2)
            .rewardsWith(0);
    }

    // =========================================================================
    // HELPER METHODS
    // =========================================================================

    private PatientAssignment createAssignment(String id, Clinician clinician, Patient patient, 
                                             PatientAssignment.TimeSlot timeSlot) {
        return PatientAssignment.builder()
            .id(id)
            .assignedClinician(clinician)
            .patient(patient)
            .assignedTimeSlot(timeSlot)
            .assignmentDate(LocalDate.now())
            .priority(patient.getVisitPriority())
            .build();
    }

    private PatientAssignment createAssignmentWithZone(String id, Clinician clinician, Patient patient, 
                                                     PatientAssignment.TimeSlot timeSlot, String zone) {
        return PatientAssignment.builder()
            .id(id)
            .assignedClinician(clinician)
            .patient(patient)
            .assignedTimeSlot(timeSlot)
            .assignmentDate(LocalDate.now())
            .priority(patient.getVisitPriority())
            .geographicZone(zone)
            .build();
    }
}
