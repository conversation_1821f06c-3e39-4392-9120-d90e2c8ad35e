{"name": "Rescheduling Rules Demonstration", "description": "Tests the system's ability to suggest rescheduling options when no immediate matches are found for the original request. For example, when a patient requires a Respiratory Therapist on 2025-06-01 but no matching carestaff is available on that date, the system suggests rescheduling to 2025-06-02 when a suitable carestaff becomes available.", "patients": [{"id": "patient-001", "name": "<PERSON>", "mrn": "MRN12345", "location": {"address": "123 Main St, Anytown, CA 90210", "postal_code": "90210", "coordinates": {"latitude": 34.0522, "longitude": -118.2437}}, "required_skills": ["Respiratory Therapist"], "required_certifications": ["Respiratory"], "required_slots": [{"date": "2025-06-01", "start_time": "09:00", "end_time": "11:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 2}}, {"id": "patient-002", "name": "<PERSON>", "mrn": "MRN67890", "location": {"address": "456 Oak St, Anytown, CA 90299", "postal_code": "90299", "coordinates": {"latitude": 34.0548, "longitude": -118.25}}, "required_skills": ["Physical Therapist"], "required_certifications": ["Therapy"], "required_slots": [{"date": "2025-06-01", "start_time": "13:00", "end_time": "15:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": [], "preferred_carestaff_gender": "any", "preferred_languages": ["english", "spanish"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 1}}, {"id": "patient-003", "name": "<PERSON>", "mrn": "MRN54321", "location": {"address": "789 Pine St, Anytown, CA 90212", "postal_code": "90212", "coordinates": {"latitude": 34.053, "longitude": -118.248}}, "required_skills": ["RN"], "required_certifications": ["Nursing"], "required_slots": [{"date": "2025-06-01", "start_time": "16:00", "end_time": "18:00"}], "preferences": {"preferred_carestaff_ids": [], "excluded_carestaff_ids": ["carestaff-001", "carestaff-005"], "preferred_carestaff_gender": "any", "preferred_languages": ["english"], "previous_carestaff_ids": []}, "profile": {"min_carestaff_experience": 1}}], "carestaff": [{"id": "carestaff-001", "name": "<PERSON>", "service_type": "home_care", "skills": ["RN"], "certifications": ["Nursing", "CPR"], "languages": ["english"], "location": {"address": "101 Elm St, Anytown, CA 90210", "service_areas": ["90210", "90211", "90212"], "coordinates": {"latitude": 34.0535, "longitude": -118.249}}, "available_slots": [{"date": "2025-06-02", "start_time": "09:00", "end_time": "11:00"}, {"date": "2025-06-03", "start_time": "09:00", "end_time": "11:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 5}, "current_appointment_count": 2, "max_daily_appointments": 5}, {"id": "carestaff-002", "name": "<PERSON>", "service_type": "home_care", "skills": ["Physical Therapist"], "certifications": ["Therapy", "CPR"], "languages": ["english", "spanish"], "location": {"address": "202 Maple St, Anytown, CA 90211", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.054, "longitude": -118.2495}}, "available_slots": [{"date": "2025-06-02", "start_time": "13:00", "end_time": "15:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 8}, "current_appointment_count": 1, "max_daily_appointments": 4}, {"id": "carestaff-003", "name": "<PERSON>", "service_type": "home_care", "skills": ["Respiratory Therapist"], "certifications": ["Respiratory", "CPR"], "languages": ["english", "spanish"], "location": {"address": "303 Cedar St, Anytown, CA 90211", "service_areas": ["90211", "90212"], "coordinates": {"latitude": 34.0545, "longitude": -118.2505}}, "available_slots": [{"date": "2025-06-02", "start_time": "09:00", "end_time": "11:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 3}, "current_appointment_count": 3, "max_daily_appointments": 5}, {"id": "carestaff-004", "name": "<PERSON>", "service_type": "home_care", "skills": ["Physical Therapist"], "certifications": ["Therapy", "CPR"], "languages": ["english", "mandarin"], "location": {"address": "404 Birch St, Anytown, CA 90212", "service_areas": ["90212", "90299"], "coordinates": {"latitude": 34.055, "longitude": -118.251}}, "available_slots": [{"date": "2025-06-03", "start_time": "13:00", "end_time": "15:00"}], "pii": {"gender": "male"}, "profile": {"experience_years": 6}, "current_appointment_count": 1, "max_daily_appointments": 3}, {"id": "carestaff-005", "name": "<PERSON>", "service_type": "home_care", "skills": ["RN"], "certifications": ["Nursing", "CPR"], "languages": ["english", "mandarin"], "location": {"address": "505 Pine St, Anytown, CA 90210", "service_areas": ["90210", "90211"], "coordinates": {"latitude": 34.0555, "longitude": -118.2515}}, "available_slots": [{"date": "2025-06-02", "start_time": "16:00", "end_time": "18:00"}], "pii": {"gender": "female"}, "profile": {"experience_years": 7}, "current_appointment_count": 0, "max_daily_appointments": 5}], "skill_hierarchy": {"Nursing": {"RN": 1, "LPN": 2, "CNA": 3}, "Therapy": {"Physical Therapist": 1, "Occupational Therapist": 2, "Therapy Aide": 3}, "Respiratory": {"Respiratory Therapist": 1, "Respiratory Aide": 2}}, "rules": {"skill-match": {"check_hierarchy": true}, "carestaff-workload-priority": {"section": "WORKLOAD_PRIORITIZATION", "type": "prioritization", "rule_id": "carestaff-workload-priority", "description": "Prioritize carestaffs with the least workload", "priority_attribute": "current_appointment_count", "priority_order": "asc", "enabled": true}, "reschedule-options": {"section": "RESCHEDULING_RULES", "type": "rescheduling", "rule_id": "reschedule-options", "description": "Rules for rescheduling appointments, considering skill hierarchy", "options": [{"time_shift_days": 1, "weight": 0.8, "consider_hierarchy": true}, {"time_shift_days": 2, "weight": 0.5, "consider_hierarchy": true}, {"alternate_geofence": true, "weight": 0.6, "consider_hierarchy": false}, {"skill_level_preference": "exact", "weight": 0.9}, {"skill_level_preference": "higher", "weight": 0.1}], "enabled": true}}, "expected_rescheduling_options": {"patient-001": [{"option_type": "time_shift", "days": 1, "carestaff_id": "carestaff-003", "reason": "Respiratory Therapist available on 2025-06-02"}], "patient-002": [{"option_type": "time_shift", "days": 1, "carestaff_id": "carestaff-002", "reason": "Physical Therapist available on 2025-06-02"}, {"option_type": "time_shift", "days": 2, "carestaff_id": "carestaff-004", "reason": "Physical Therapist available on 2025-06-03"}], "patient-003": [{"option_type": "time_shift", "days": 1, "carestaff_id": "carestaff-005", "reason": "RN available on 2025-06-02, patient excluded carestaff-001"}]}}