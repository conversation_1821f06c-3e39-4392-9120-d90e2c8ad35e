package com.caxl.carestaff.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * JPA entity for care staff skill mapping.
 * Maps to CARESTAFF_HAS_SKILL table (many-to-many relationship).
 */
@Entity
@Table(name = "carestaff_has_skill")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CareStaffHasSkillEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "mapping_id")
    private UUID mappingId;

    @Column(name = "carestaff_id", nullable = false)
    private UUID careStaffId;

    @Column(name = "skill_id", nullable = false)
    private UUID skillId;

    @Column(name = "proficiency_level")
    private String proficiencyLevel; // BEGINNER, INTERMEDIATE, ADVANCED, EXPERT

    @Column(name = "years_experience")
    private Integer yearsExperience;

    @Column(name = "is_certified")
    @Builder.Default
    private Boolean isCertified = false;

    @Column(name = "certification_date")
    private LocalDateTime certificationDate;

    @Column(name = "certification_expiry")
    private LocalDateTime certificationExpiry;

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        if (mappingId == null) {
            mappingId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
