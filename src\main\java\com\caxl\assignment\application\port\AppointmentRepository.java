package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Assignment;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for appointment/assignment data access.
 * Manages scheduled appointments and their lifecycle.
 */
public interface AppointmentRepository {

    /**
     * Save a new assignment/appointment.
     * 
     * @param assignment Assignment to save
     * @return Saved assignment
     */
    Assignment save(Assignment assignment);

    /**
     * Save multiple assignments in batch.
     * 
     * @param assignments List of assignments to save
     * @return List of saved assignments
     */
    List<Assignment> saveAll(List<Assignment> assignments);

    /**
     * Find assignment by ID.
     * 
     * @param assignmentId Assignment ID
     * @return Assignment if found
     */
    Optional<Assignment> findById(UUID assignmentId);

    /**
     * Find all assignments for a specific date.
     * 
     * @param schedulingDate Date to query
     * @return List of assignments for the date
     */
    List<Assignment> findByDate(LocalDate schedulingDate);

    /**
     * Find assignments for a specific carestaff on a date.
     * 
     * @param careStaffId Carestaff ID
     * @param schedulingDate Date to query
     * @return List of assignments for the carestaff
     */
    List<Assignment> findByCareStaffAndDate(UUID careStaffId, LocalDate schedulingDate);

    /**
     * Find assignments for a specific patient on a date.
     * 
     * @param patientId Patient ID
     * @param schedulingDate Date to query
     * @return List of assignments for the patient
     */
    List<Assignment> findByPatientAndDate(UUID patientId, LocalDate schedulingDate);

    /**
     * Find conflicting assignments for a carestaff in a time window.
     * 
     * @param careStaffId Carestaff ID
     * @param startTime Start of time window
     * @param endTime End of time window
     * @return List of conflicting assignments
     */
    List<Assignment> findConflictingAssignments(UUID careStaffId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Update assignment status.
     * 
     * @param assignmentId Assignment ID
     * @param status New status
     */
    void updateStatus(UUID assignmentId, String status);

    /**
     * Cancel assignment.
     * 
     * @param assignmentId Assignment ID
     * @param reason Cancellation reason
     */
    void cancelAssignment(UUID assignmentId, String reason);

    /**
     * Delete assignment.
     * 
     * @param assignmentId Assignment ID
     */
    void deleteById(UUID assignmentId);

    /**
     * Find assignments that need rescheduling due to conflicts.
     * 
     * @param schedulingDate Date to check
     * @return List of assignments needing rescheduling
     */
    List<Assignment> findAssignmentsNeedingRescheduling(LocalDate schedulingDate);

    /**
     * Get assignment statistics for a date range.
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return Assignment statistics
     */
    AssignmentStatistics getAssignmentStatistics(LocalDate startDate, LocalDate endDate);

    /**
     * Assignment statistics.
     */
    record AssignmentStatistics(
        int totalAssignments,
        int completedAssignments,
        int cancelledAssignments,
        int rescheduledAssignments,
        double completionRate,
        double averageScore
    ) {}
}
