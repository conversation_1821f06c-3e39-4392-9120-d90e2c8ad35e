package com.caxl.assignment.domain.models.healthcare;

import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty;
import ai.timefold.solver.core.api.domain.solution.PlanningScore;
import ai.timefold.solver.core.api.domain.solution.PlanningSolution;
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * HomecareSchedule represents the complete solution for homecare visit scheduling.
 * This is the main planning solution that Timefold will optimize.
 */
@PlanningSolution
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomecareSchedule {

    /**
     * List of homecare visits to be scheduled (planning entities).
     * These are the variables that Timefold will optimize by assigning care staff and time slots.
     */
    @PlanningEntityCollectionProperty
    @Valid
    @NotNull(message = "Homecare visits list is required")
    @JsonProperty("homecare_visits")
    private List<HomecareVisit> homecareVisits;

    /**
     * Available care staff for assignment (value range for planning variables).
     */
    @ValueRangeProvider(id = "careStaffRange")
    @ProblemFactCollectionProperty
    @Valid
    @NotNull(message = "Care staff list is required")
    @JsonProperty("care_staff")
    private List<CareStaff> careStaff;

    /**
     * Patients requiring care (problem facts).
     */
    @ProblemFactCollectionProperty
    @Valid
    @JsonProperty("home_patients")
    private List<HomePatient> homePatients;

    /**
     * Care teams for organizational structure (problem facts).
     */
    @ProblemFactCollectionProperty
    @Valid
    @JsonProperty("care_teams")
    private List<CareTeam> careTeams;

    /**
     * Available time slots for scheduling (value range for planning variables).
     */
    @ValueRangeProvider(id = "timeSlotRange")
    @ProblemFactCollectionProperty
    @Valid
    @JsonProperty("time_slots")
    private List<java.time.LocalDateTime> timeSlots;

    /**
     * Organizations managing the care delivery (problem facts).
     */
    @ProblemFactCollectionProperty
    @Valid
    @JsonProperty("organizations")
    private List<Organization> organizations;

    /**
     * State-specific compliance rules (problem facts).
     */
    @ProblemFactCollectionProperty
    @Valid
    @JsonProperty("state_compliance_rules")
    private List<StateComplianceRules> stateComplianceRules;

    /**
     * The planning score calculated by Timefold.
     * Hard constraints (must be satisfied) vs Soft constraints (optimization goals).
     */
    @PlanningScore
    @JsonProperty("score")
    private HardSoftScore score;

    /**
     * Scheduling metadata and configuration.
     */
    @Valid
    @JsonProperty("schedule_metadata")
    private ScheduleMetadata scheduleMetadata;

    // === NESTED CLASSES ===

    /**
     * Metadata about the scheduling run and configuration.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ScheduleMetadata {
        
        @JsonProperty("schedule_id")
        private String scheduleId;

        @JsonProperty("organization_id")
        private String organizationId;

        @JsonProperty("primary_state")
        private String primaryState;

        @JsonProperty("scheduling_period_start")
        private String schedulingPeriodStart;

        @JsonProperty("scheduling_period_end")
        private String schedulingPeriodEnd;

        @JsonProperty("optimization_objectives")
        private List<String> optimizationObjectives;

        @JsonProperty("constraint_weights")
        private ConstraintWeights constraintWeights;

        @JsonProperty("solver_config")
        private SolverConfig solverConfig;
    }

    /**
     * Weights for different constraint types in scoring.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ConstraintWeights {
        
        // Hard constraint weights (violations prevent feasible solution)
        @JsonProperty("license_compliance_weight")
        @Builder.Default
        private Integer licenseComplianceWeight = 1000;

        @JsonProperty("skill_match_weight")
        @Builder.Default
        private Integer skillMatchWeight = 1000;

        @JsonProperty("working_hours_weight")
        @Builder.Default
        private Integer workingHoursWeight = 1000;

        @JsonProperty("geographic_zone_weight")
        @Builder.Default
        private Integer geographicZoneWeight = 1000;

        // Soft constraint weights (optimization objectives)
        @JsonProperty("travel_time_weight")
        @Builder.Default
        private Integer travelTimeWeight = 100;

        @JsonProperty("staff_preference_weight")
        @Builder.Default
        private Integer staffPreferenceWeight = 50;

        @JsonProperty("patient_preference_weight")
        @Builder.Default
        private Integer patientPreferenceWeight = 75;

        @JsonProperty("workload_balance_weight")
        @Builder.Default
        private Integer workloadBalanceWeight = 60;

        @JsonProperty("continuity_of_care_weight")
        @Builder.Default
        private Integer continuityOfCareWeight = 80;
    }

    /**
     * Solver configuration parameters.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SolverConfig {
        
        @JsonProperty("solving_time_limit_seconds")
        @Builder.Default
        private Long solvingTimeLimitSeconds = 300L; // 5 minutes

        @JsonProperty("unimproved_seconds_spent_limit")
        @Builder.Default
        private Long unimprovedSecondsSpentLimit = 60L;

        @JsonProperty("enable_multithreading")
        @Builder.Default
        private Boolean enableMultithreading = true;

        @JsonProperty("random_seed")
        private Long randomSeed;

        @JsonProperty("optimization_strategy")
        @Builder.Default
        private String optimizationStrategy = "BALANCED";

        @JsonProperty("custom_phases")
        private List<String> customPhases;
    }

    // === HELPER METHODS ===

    /**
     * Get care staff by ID.
     */
    public CareStaff getCareStaffById(String careStaffId) {
        return careStaff.stream()
                .filter(staff -> staff.getId().equals(careStaffId))
                .findFirst()
                .orElse(null);
    }

    /**
     * Get home patient by ID.
     */
    public HomePatient getHomePatientById(String patientId) {
        return homePatients.stream()
                .filter(patient -> patient.getId().equals(patientId))
                .findFirst()
                .orElse(null);
    }

    /**
     * Get state compliance rules by state code.
     */
    public StateComplianceRules getStateComplianceRules(String stateCode) {
        return stateComplianceRules.stream()
                .filter(rules -> rules.getStateCode().equals(stateCode))
                .findFirst()
                .orElse(null);
    }

    /**
     * Get total number of unassigned visits.
     */
    public long getUnassignedVisitsCount() {
        return homecareVisits.stream()
                .filter(visit -> visit.getAssignedCareStaffId() == null)
                .count();
    }

    /**
     * Get total number of assigned visits.
     */
    public long getAssignedVisitsCount() {
        return homecareVisits.stream()
                .filter(visit -> visit.getAssignedCareStaffId() != null)
                .count();
    }

    /**
     * Check if solution is feasible (no hard constraint violations).
     */
    public boolean isFeasible() {
        return score != null && score.hardScore() >= 0;
    }
}
