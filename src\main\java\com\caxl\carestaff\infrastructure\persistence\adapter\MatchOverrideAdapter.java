package com.caxl.carestaff.infrastructure.persistence.adapter;

import com.caxl.carestaff.application.port.out.persistence.MatchOverridePort;
import com.caxl.carestaff.domain.entities.MatchOverride;
import com.caxl.carestaff.infrastructure.persistence.entity.MatchOverrideEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * Spring Data JPA repository for match override entities.
 */
interface MatchOverrideRepository extends JpaRepository<MatchOverrideEntity, UUID> {
}

/**
 * Persistence adapter for match override operations.
 * Implements the MatchOverridePort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class MatchOverrideAdapter implements MatchOverridePort {

    private final MatchOverrideRepository repository;

    @Override
    public MatchOverride save(MatchOverride matchOverride) {
        log.debug("Saving match override: {}", matchOverride.getOverrideId());
        
        MatchOverrideEntity entity = mapToEntity(matchOverride);
        MatchOverrideEntity saved = repository.save(entity);
        return mapToDomain(saved);
    }

    /**
     * Map domain entity to JPA entity.
     */
    private MatchOverrideEntity mapToEntity(MatchOverride domain) {
        return MatchOverrideEntity.builder()
                .overrideId(domain.getOverrideId())
                .serviceRequestId(domain.getServiceRequestId())
                .selectedCareStaffId(domain.getSelectedCareStaffId())
                .schedulerId(domain.getSchedulerId())
                .reason(domain.getReason())
                .explanation(domain.getExplanation())
                .overrideDateTime(domain.getOverrideDateTime())
                .createdAt(domain.getCreatedAt())
                .build();
    }

    /**
     * Map JPA entity to domain entity.
     */
    private MatchOverride mapToDomain(MatchOverrideEntity entity) {
        return MatchOverride.builder()
                .overrideId(entity.getOverrideId())
                .serviceRequestId(entity.getServiceRequestId())
                .selectedCareStaffId(entity.getSelectedCareStaffId())
                .schedulerId(entity.getSchedulerId())
                .reason(entity.getReason())
                .explanation(entity.getExplanation())
                .overrideDateTime(entity.getOverrideDateTime())
                .createdAt(entity.getCreatedAt())
                .build();
    }
}
