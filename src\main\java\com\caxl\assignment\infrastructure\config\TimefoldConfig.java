package com.caxl.assignment.infrastructure.config;

import ai.timefold.solver.core.api.solver.SolverFactory;
import com.caxl.assignment.domain.scheduling.SchedulingSolution;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Optimal Timefold configuration for shift-based scheduling.
 * Provides enterprise-grade solver configuration using community APIs.
 */
@Configuration
public class TimefoldConfig {

    /**
     * Create solver factory for optimal shift-based scheduling.
     */
    @Bean
    public SolverFactory<SchedulingSolution> solverFactory() {
        return SolverFactory.createFromXmlResource(
            "com/caxl/assignment/scheduling/schedulingSolverConfig.xml");
    }
}
