package com.caxl.assignment.interfaces.rest;

import com.caxl.assignment.application.services.assignment.DynamicMultiLevelAssignmentService;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.assignment.*;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import com.caxl.assignment.infrastructure.config.DynamicRuleConfigurationLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * REST controller for dynamic multi-level clinician assignment system.
 * Provides endpoints for assignment processing, rule management, and system monitoring.
 */
@RestController
@RequestMapping("/api/v1/dynamic-assignment")
@RequiredArgsConstructor
@Slf4j
public class DynamicAssignmentController {

    private final DynamicMultiLevelAssignmentService assignmentService;
    private final DynamicRuleConfigurationLoader ruleLoader;

    /**
     * Process assignment requests through multi-level filtering system.
     */
    @PostMapping("/process")
    public ResponseEntity<AssignmentResponse> processAssignments(
            @Valid @RequestBody AssignmentRequest request) {
        
        log.info("Processing assignment request with {} requests", 
                request.getAssignmentRequests().size());
        
        try {
            // Load dynamic rules
            List<DynamicRule> dynamicRules = ruleLoader.getCachedRules();
            
            // Process assignments
            MultiLevelAssignmentSolution solution = assignmentService.processAssignments(
                request.getAssignmentRequests(),
                request.getClinicians(),
                request.getCareStaff(),
                dynamicRules
            );
            
            // Create response
            AssignmentResponse response = createAssignmentResponse(solution);
            
            log.info("Assignment processing completed. Completion rate: {:.1f}%", 
                    solution.getCompletionRate() * 100);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error processing assignments: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(AssignmentResponse.builder()
                            .success(false)
                            .errorMessage(e.getMessage())
                            .build());
        }
    }

    /**
     * Get current dynamic rules configuration.
     */
    @GetMapping("/rules")
    public ResponseEntity<RulesResponse> getRules(
            @RequestParam(required = false) String level,
            @RequestParam(required = false) String type) {
        
        try {
            List<DynamicRule> rules;
            
            if (level != null && type != null) {
                DynamicRule.RuleLevel ruleLevel = DynamicRule.RuleLevel.valueOf(level.toUpperCase());
                DynamicRule.RuleType ruleType = DynamicRule.RuleType.valueOf(type.toUpperCase());
                rules = ruleLoader.loadRules(ruleLevel, ruleType);
            } else if (level != null) {
                DynamicRule.RuleLevel ruleLevel = DynamicRule.RuleLevel.valueOf(level.toUpperCase());
                rules = ruleLoader.loadRulesByLevel(ruleLevel);
            } else if (type != null) {
                DynamicRule.RuleType ruleType = DynamicRule.RuleType.valueOf(type.toUpperCase());
                rules = ruleLoader.loadRulesByType(ruleType);
            } else {
                rules = ruleLoader.getCachedRules();
            }
            
            DynamicRuleConfigurationLoader.RuleStatistics statistics = ruleLoader.getRuleStatistics();
            
            return ResponseEntity.ok(RulesResponse.builder()
                    .success(true)
                    .rules(rules)
                    .statistics(statistics)
                    .build());
            
        } catch (Exception e) {
            log.error("Error retrieving rules: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(RulesResponse.builder()
                            .success(false)
                            .errorMessage(e.getMessage())
                            .build());
        }
    }

    /**
     * Reload dynamic rules from configuration.
     */
    @PostMapping("/rules/reload")
    public ResponseEntity<RulesResponse> reloadRules() {
        try {
            List<DynamicRule> rules = ruleLoader.reloadRules();
            DynamicRuleConfigurationLoader.RuleStatistics statistics = ruleLoader.getRuleStatistics();
            
            log.info("Rules reloaded successfully. Total rules: {}", rules.size());
            
            return ResponseEntity.ok(RulesResponse.builder()
                    .success(true)
                    .rules(rules)
                    .statistics(statistics)
                    .message("Rules reloaded successfully")
                    .build());
            
        } catch (Exception e) {
            log.error("Error reloading rules: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(RulesResponse.builder()
                            .success(false)
                            .errorMessage(e.getMessage())
                            .build());
        }
    }

    /**
     * Validate rules configuration.
     */
    @PostMapping("/rules/validate")
    public ResponseEntity<ValidationResponse> validateRules(
            @RequestParam(defaultValue = "dynamic-assignment-rules.json") String configPath) {
        
        try {
            boolean isValid = ruleLoader.validateRuleConfiguration(configPath);
            
            return ResponseEntity.ok(ValidationResponse.builder()
                    .valid(isValid)
                    .configPath(configPath)
                    .message(isValid ? "Configuration is valid" : "Configuration has errors")
                    .build());
            
        } catch (Exception e) {
            log.error("Error validating rules configuration: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ValidationResponse.builder()
                            .valid(false)
                            .configPath(configPath)
                            .errorMessage(e.getMessage())
                            .build());
        }
    }

    /**
     * Get system statistics and monitoring information.
     */
    @GetMapping("/statistics")
    public ResponseEntity<SystemStatistics> getSystemStatistics() {
        try {
            DynamicRuleConfigurationLoader.RuleStatistics ruleStats = ruleLoader.getRuleStatistics();
            
            SystemStatistics stats = SystemStatistics.builder()
                    .ruleStatistics(ruleStats)
                    .systemStatus("OPERATIONAL")
                    .lastRuleReload(ruleStats.getLastLoadTime())
                    .build();
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("Error retrieving system statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(SystemStatistics.builder()
                            .systemStatus("ERROR")
                            .errorMessage(e.getMessage())
                            .build());
        }
    }

    /**
     * Create assignment response from solution.
     */
    private AssignmentResponse createAssignmentResponse(MultiLevelAssignmentSolution solution) {
        return AssignmentResponse.builder()
                .success(true)
                .solution(solution)
                .completionRate(solution.getCompletionRate())
                .totalRequests(solution.getAssignmentRequests().size())
                .assignedRequests(solution.getAssignedRequests().size())
                .unassignedRequests(solution.getUnassignedRequests().size())
                .level1Assignments(solution.getAssignmentsByLevel(1).size())
                .level2Assignments(solution.getAssignmentsByLevel(2).size())
                .level3Assignments(solution.getAssignmentsByLevel(3).size())
                .statistics(solution.getStatistics())
                .build();
    }

    // =========================================================================
    // REQUEST/RESPONSE DTOs
    // =========================================================================

    public static class AssignmentRequest {
        private List<ClinicianAssignmentRequest> assignmentRequests;
        private List<Clinician> clinicians;
        private List<CareStaff> careStaff;
        private Map<String, Object> configuration;

        // Getters and setters
        public List<ClinicianAssignmentRequest> getAssignmentRequests() { return assignmentRequests; }
        public void setAssignmentRequests(List<ClinicianAssignmentRequest> assignmentRequests) { this.assignmentRequests = assignmentRequests; }
        
        public List<Clinician> getClinicians() { return clinicians; }
        public void setClinicians(List<Clinician> clinicians) { this.clinicians = clinicians; }
        
        public List<CareStaff> getCareStaff() { return careStaff; }
        public void setCareStaff(List<CareStaff> careStaff) { this.careStaff = careStaff; }
        
        public Map<String, Object> getConfiguration() { return configuration; }
        public void setConfiguration(Map<String, Object> configuration) { this.configuration = configuration; }
    }

    public static class AssignmentResponse {
        private boolean success;
        private String errorMessage;
        private MultiLevelAssignmentSolution solution;
        private double completionRate;
        private int totalRequests;
        private int assignedRequests;
        private int unassignedRequests;
        private int level1Assignments;
        private int level2Assignments;
        private int level3Assignments;
        private MultiLevelAssignmentSolution.AssignmentStatistics statistics;

        public static Builder builder() { return new Builder(); }

        // Getters
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public MultiLevelAssignmentSolution getSolution() { return solution; }
        public double getCompletionRate() { return completionRate; }
        public int getTotalRequests() { return totalRequests; }
        public int getAssignedRequests() { return assignedRequests; }
        public int getUnassignedRequests() { return unassignedRequests; }
        public int getLevel1Assignments() { return level1Assignments; }
        public int getLevel2Assignments() { return level2Assignments; }
        public int getLevel3Assignments() { return level3Assignments; }
        public MultiLevelAssignmentSolution.AssignmentStatistics getStatistics() { return statistics; }

        public static class Builder {
            private AssignmentResponse response = new AssignmentResponse();
            
            public Builder success(boolean success) { response.success = success; return this; }
            public Builder errorMessage(String errorMessage) { response.errorMessage = errorMessage; return this; }
            public Builder solution(MultiLevelAssignmentSolution solution) { response.solution = solution; return this; }
            public Builder completionRate(double completionRate) { response.completionRate = completionRate; return this; }
            public Builder totalRequests(int totalRequests) { response.totalRequests = totalRequests; return this; }
            public Builder assignedRequests(int assignedRequests) { response.assignedRequests = assignedRequests; return this; }
            public Builder unassignedRequests(int unassignedRequests) { response.unassignedRequests = unassignedRequests; return this; }
            public Builder level1Assignments(int level1Assignments) { response.level1Assignments = level1Assignments; return this; }
            public Builder level2Assignments(int level2Assignments) { response.level2Assignments = level2Assignments; return this; }
            public Builder level3Assignments(int level3Assignments) { response.level3Assignments = level3Assignments; return this; }
            public Builder statistics(MultiLevelAssignmentSolution.AssignmentStatistics statistics) { response.statistics = statistics; return this; }
            
            public AssignmentResponse build() { return response; }
        }
    }

    public static class RulesResponse {
        private boolean success;
        private String errorMessage;
        private String message;
        private List<DynamicRule> rules;
        private DynamicRuleConfigurationLoader.RuleStatistics statistics;

        public static Builder builder() { return new Builder(); }

        // Getters
        public boolean isSuccess() { return success; }
        public String getErrorMessage() { return errorMessage; }
        public String getMessage() { return message; }
        public List<DynamicRule> getRules() { return rules; }
        public DynamicRuleConfigurationLoader.RuleStatistics getStatistics() { return statistics; }

        public static class Builder {
            private RulesResponse response = new RulesResponse();
            
            public Builder success(boolean success) { response.success = success; return this; }
            public Builder errorMessage(String errorMessage) { response.errorMessage = errorMessage; return this; }
            public Builder message(String message) { response.message = message; return this; }
            public Builder rules(List<DynamicRule> rules) { response.rules = rules; return this; }
            public Builder statistics(DynamicRuleConfigurationLoader.RuleStatistics statistics) { response.statistics = statistics; return this; }
            
            public RulesResponse build() { return response; }
        }
    }

    public static class ValidationResponse {
        private boolean valid;
        private String configPath;
        private String message;
        private String errorMessage;

        public static Builder builder() { return new Builder(); }

        // Getters
        public boolean isValid() { return valid; }
        public String getConfigPath() { return configPath; }
        public String getMessage() { return message; }
        public String getErrorMessage() { return errorMessage; }

        public static class Builder {
            private ValidationResponse response = new ValidationResponse();
            
            public Builder valid(boolean valid) { response.valid = valid; return this; }
            public Builder configPath(String configPath) { response.configPath = configPath; return this; }
            public Builder message(String message) { response.message = message; return this; }
            public Builder errorMessage(String errorMessage) { response.errorMessage = errorMessage; return this; }
            
            public ValidationResponse build() { return response; }
        }
    }

    public static class SystemStatistics {
        private DynamicRuleConfigurationLoader.RuleStatistics ruleStatistics;
        private String systemStatus;
        private long lastRuleReload;
        private String errorMessage;

        public static Builder builder() { return new Builder(); }

        // Getters
        public DynamicRuleConfigurationLoader.RuleStatistics getRuleStatistics() { return ruleStatistics; }
        public String getSystemStatus() { return systemStatus; }
        public long getLastRuleReload() { return lastRuleReload; }
        public String getErrorMessage() { return errorMessage; }

        public static class Builder {
            private SystemStatistics stats = new SystemStatistics();
            
            public Builder ruleStatistics(DynamicRuleConfigurationLoader.RuleStatistics ruleStatistics) { stats.ruleStatistics = ruleStatistics; return this; }
            public Builder systemStatus(String systemStatus) { stats.systemStatus = systemStatus; return this; }
            public Builder lastRuleReload(long lastRuleReload) { stats.lastRuleReload = lastRuleReload; return this; }
            public Builder errorMessage(String errorMessage) { stats.errorMessage = errorMessage; return this; }
            
            public SystemStatistics build() { return stats; }
        }
    }
}
