package com.caxl.carestaff.domain.services;

import com.caxl.carestaff.domain.entities.ServiceRequest;
import com.caxl.carestaff.domain.valueobjects.MatchingCriteria;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Domain service for calculating availability window fit scores.
 * Evaluates how well a care staff's availability aligns with service request time windows.
 */
@Service
@Slf4j
public class AvailabilityWindowScoringService {

    private final JdbcTemplate jdbcTemplate;

    public AvailabilityWindowScoringService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Calculate availability window fit score for a care staff member.
     */
    public AvailabilityWindowScore calculateAvailabilityScore(
            UUID careStaffId,
            ServiceRequest serviceRequest,
            MatchingCriteria.Availability availabilityConfig,
            MatchingCriteria.ScoringWeights weights) {

        log.debug("Calculating availability score for staff {} and request {}", 
                 careStaffId, serviceRequest.getRequestId());

        ServiceRequest.TimeWindow timeWindow = serviceRequest.getTimeWindow();
        if (timeWindow == null) {
            return new AvailabilityWindowScore(0.0, "No time window specified");
        }

        double totalScore = 0.0;
        StringBuilder rationale = new StringBuilder();

        // 1. Calculate buffer time scoring
        BufferTimeScore bufferScore = calculateBufferTimeScore(
                careStaffId, timeWindow, availabilityConfig, weights);
        totalScore += bufferScore.score();
        rationale.append(bufferScore.rationale()).append(" ");

        // 2. Calculate preferred time alignment
        PreferredTimeScore preferredScore = calculatePreferredTimeAlignment(
                careStaffId, timeWindow, weights);
        totalScore += preferredScore.score();
        rationale.append(preferredScore.rationale()).append(" ");

        // 3. Calculate workload distribution score
        WorkloadDistributionScore workloadScore = calculateWorkloadDistribution(
                careStaffId, timeWindow, weights);
        totalScore += workloadScore.score();
        rationale.append(workloadScore.rationale()).append(" ");

        // 4. Calculate travel time optimization
        TravelTimeScore travelScore = calculateTravelTimeOptimization(
                careStaffId, timeWindow, weights);
        totalScore += travelScore.score();
        rationale.append(travelScore.rationale()).append(" ");

        // 5. Calculate shift boundary alignment
        ShiftBoundaryScore shiftScore = calculateShiftBoundaryAlignment(
                careStaffId, timeWindow, weights);
        totalScore += shiftScore.score();
        rationale.append(shiftScore.rationale()).append(" ");

        log.debug("Total availability score for staff {}: {}", careStaffId, totalScore);

        return new AvailabilityWindowScore(totalScore, rationale.toString().trim());
    }

    /**
     * Calculate buffer time score based on time before/after visit.
     */
    private BufferTimeScore calculateBufferTimeScore(
            UUID careStaffId,
            ServiceRequest.TimeWindow timeWindow,
            MatchingCriteria.Availability config,
            MatchingCriteria.ScoringWeights weights) {

        // Get surrounding appointments
        List<Map<String, Object>> surroundingAppointments = getSurroundingAppointments(
                careStaffId, timeWindow.getArrivalWindowStart(), timeWindow.getArrivalWindowEnd());

        double score = 0.0;
        StringBuilder rationale = new StringBuilder();

        // Calculate buffer before visit
        long bufferBefore = calculateBufferBefore(surroundingAppointments, timeWindow.getArrivalWindowStart());
        if (bufferBefore >= config.getMinTimeBeforeVisitMinutes()) {
            double bufferBonus = Math.min((bufferBefore - config.getMinTimeBeforeVisitMinutes()) * 0.1, 5.0);
            score += bufferBonus;
            rationale.append(String.format("BufferBefore(+%.1f)", bufferBonus));
        } else {
            double bufferPenalty = (config.getMinTimeBeforeVisitMinutes() - bufferBefore) * 
                                  weights.getAvailabilityWindowFitPenaltyPerMinuteDeviation();
            score += bufferPenalty; // This will be negative
            rationale.append(String.format("BufferBefore(%.1f)", bufferPenalty));
        }

        // Calculate buffer after visit
        LocalDateTime visitEnd = timeWindow.getArrivalWindowStart().plusMinutes(timeWindow.getVisitDurationMinutes());
        long bufferAfter = calculateBufferAfter(surroundingAppointments, visitEnd);
        if (bufferAfter >= config.getMinTimeAfterVisitMinutes()) {
            double bufferBonus = Math.min((bufferAfter - config.getMinTimeAfterVisitMinutes()) * 0.1, 5.0);
            score += bufferBonus;
            rationale.append(String.format(" BufferAfter(+%.1f)", bufferBonus));
        } else {
            double bufferPenalty = (config.getMinTimeAfterVisitMinutes() - bufferAfter) * 
                                  weights.getAvailabilityWindowFitPenaltyPerMinuteDeviation();
            score += bufferPenalty; // This will be negative
            rationale.append(String.format(" BufferAfter(%.1f)", bufferPenalty));
        }

        return new BufferTimeScore(score, rationale.toString());
    }

    /**
     * Calculate preferred time alignment score.
     */
    private PreferredTimeScore calculatePreferredTimeAlignment(
            UUID careStaffId,
            ServiceRequest.TimeWindow timeWindow,
            MatchingCriteria.ScoringWeights weights) {

        // Get staff's preferred working hours (this would come from staff profile)
        StaffPreferredHours preferredHours = getStaffPreferredHours(careStaffId);
        
        LocalTime requestTime = timeWindow.getArrivalWindowStart().toLocalTime();
        double score = 0.0;
        String rationale = "";

        if (preferredHours != null) {
            if (isWithinPreferredHours(requestTime, preferredHours)) {
                score += 10.0; // Bonus for preferred time
                rationale = "PreferredTime(+10.0)";
            } else {
                // Calculate penalty based on distance from preferred hours
                long minutesFromPreferred = calculateMinutesFromPreferredHours(requestTime, preferredHours);
                double penalty = Math.min(minutesFromPreferred * -0.1, -5.0);
                score += penalty;
                rationale = String.format("PreferredTime(%.1f)", penalty);
            }
        } else {
            rationale = "PreferredTime(N/A)";
        }

        return new PreferredTimeScore(score, rationale);
    }

    /**
     * Calculate workload distribution score.
     */
    private WorkloadDistributionScore calculateWorkloadDistribution(
            UUID careStaffId,
            ServiceRequest.TimeWindow timeWindow,
            MatchingCriteria.ScoringWeights weights) {

        // Count appointments on the same day
        int appointmentsOnDay = countAppointmentsOnDay(careStaffId, timeWindow.getArrivalWindowStart().toLocalDate());
        
        double score = 0.0;
        String rationale = "";

        // Optimal workload is 4-6 appointments per day
        if (appointmentsOnDay >= 4 && appointmentsOnDay <= 6) {
            score += 5.0; // Bonus for optimal workload
            rationale = String.format("OptimalWorkload(%d,+5.0)", appointmentsOnDay);
        } else if (appointmentsOnDay < 4) {
            score += 2.0; // Small bonus for light day (allows flexibility)
            rationale = String.format("LightWorkload(%d,+2.0)", appointmentsOnDay);
        } else {
            double penalty = (appointmentsOnDay - 6) * -1.0; // Penalty for overload
            score += penalty;
            rationale = String.format("Overload(%d,%.1f)", appointmentsOnDay, penalty);
        }

        return new WorkloadDistributionScore(score, rationale);
    }

    /**
     * Calculate travel time optimization score.
     */
    private TravelTimeScore calculateTravelTimeOptimization(
            UUID careStaffId,
            ServiceRequest.TimeWindow timeWindow,
            MatchingCriteria.ScoringWeights weights) {

        // Get adjacent appointments for travel time calculation
        Map<String, Object> previousAppointment = getPreviousAppointment(careStaffId, timeWindow.getArrivalWindowStart());
        Map<String, Object> nextAppointment = getNextAppointment(careStaffId, timeWindow.getArrivalWindowEnd());

        double score = 0.0;
        StringBuilder rationale = new StringBuilder();

        // Calculate travel time efficiency
        if (previousAppointment != null) {
            double travelTimeBefore = estimateTravelTime(previousAppointment, timeWindow);
            if (travelTimeBefore <= 30) { // 30 minutes or less is good
                score += 3.0;
                rationale.append("EfficientTravel(+3.0) ");
            } else {
                double penalty = (travelTimeBefore - 30) * -0.1;
                score += penalty;
                rationale.append(String.format("LongTravel(%.1f) ", penalty));
            }
        }

        if (nextAppointment != null) {
            double travelTimeAfter = estimateTravelTime(timeWindow, nextAppointment);
            if (travelTimeAfter <= 30) {
                score += 3.0;
                rationale.append("EfficientTravel(+3.0)");
            } else {
                double penalty = (travelTimeAfter - 30) * -0.1;
                score += penalty;
                rationale.append(String.format("LongTravel(%.1f)", penalty));
            }
        }

        return new TravelTimeScore(score, rationale.toString().trim());
    }

    /**
     * Calculate shift boundary alignment score.
     */
    private ShiftBoundaryScore calculateShiftBoundaryAlignment(
            UUID careStaffId,
            ServiceRequest.TimeWindow timeWindow,
            MatchingCriteria.ScoringWeights weights) {

        // Check if appointment aligns well with shift boundaries
        LocalTime appointmentTime = timeWindow.getArrivalWindowStart().toLocalTime();
        
        double score = 0.0;
        String rationale = "";

        // Standard shift times
        if (isWithinShift(appointmentTime, LocalTime.of(7, 0), LocalTime.of(15, 0))) {
            score += 5.0; // Day shift bonus
            rationale = "DayShift(+5.0)";
        } else if (isWithinShift(appointmentTime, LocalTime.of(15, 0), LocalTime.of(23, 0))) {
            score += 3.0; // Evening shift bonus
            rationale = "EveningShift(+3.0)";
        } else {
            score -= 5.0; // Night shift penalty
            rationale = "NightShift(-5.0)";
        }

        return new ShiftBoundaryScore(score, rationale);
    }

    // Helper methods
    private List<Map<String, Object>> getSurroundingAppointments(UUID careStaffId, LocalDateTime start, LocalDateTime end) {
        String sql = """
            SELECT appointment_id, scheduled_start_time, scheduled_end_time
            FROM appointment
            WHERE carestaff_id = ?
            AND status IN ('scheduled', 'in_progress')
            AND (scheduled_start_time BETWEEN ? AND ? 
                 OR scheduled_end_time BETWEEN ? AND ?
                 OR (scheduled_start_time <= ? AND scheduled_end_time >= ?))
            ORDER BY scheduled_start_time
            """;
        
        LocalDateTime dayStart = start.toLocalDate().atStartOfDay();
        LocalDateTime dayEnd = end.toLocalDate().atTime(23, 59, 59);
        
        return jdbcTemplate.queryForList(sql, careStaffId, dayStart, dayEnd, dayStart, dayEnd, start, end);
    }

    private long calculateBufferBefore(List<Map<String, Object>> appointments, LocalDateTime requestStart) {
        return appointments.stream()
                .filter(apt -> ((LocalDateTime) apt.get("scheduled_end_time")).isBefore(requestStart))
                .mapToLong(apt -> Duration.between((LocalDateTime) apt.get("scheduled_end_time"), requestStart).toMinutes())
                .min()
                .orElse(120); // Default 2 hours if no previous appointment
    }

    private long calculateBufferAfter(List<Map<String, Object>> appointments, LocalDateTime requestEnd) {
        return appointments.stream()
                .filter(apt -> ((LocalDateTime) apt.get("scheduled_start_time")).isAfter(requestEnd))
                .mapToLong(apt -> Duration.between(requestEnd, (LocalDateTime) apt.get("scheduled_start_time")).toMinutes())
                .min()
                .orElse(120); // Default 2 hours if no next appointment
    }

    private StaffPreferredHours getStaffPreferredHours(UUID careStaffId) {
        // This would typically come from staff profile
        // For now, return default business hours
        return new StaffPreferredHours(LocalTime.of(8, 0), LocalTime.of(17, 0));
    }

    private boolean isWithinPreferredHours(LocalTime time, StaffPreferredHours preferred) {
        return !time.isBefore(preferred.startTime()) && !time.isAfter(preferred.endTime());
    }

    private long calculateMinutesFromPreferredHours(LocalTime time, StaffPreferredHours preferred) {
        if (time.isBefore(preferred.startTime())) {
            return Duration.between(time, preferred.startTime()).toMinutes();
        } else if (time.isAfter(preferred.endTime())) {
            return Duration.between(preferred.endTime(), time).toMinutes();
        }
        return 0;
    }

    private int countAppointmentsOnDay(UUID careStaffId, java.time.LocalDate date) {
        String sql = """
            SELECT COUNT(*)
            FROM appointment
            WHERE carestaff_id = ?
            AND DATE(scheduled_start_time) = ?
            AND status IN ('scheduled', 'in_progress', 'completed')
            """;
        
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, careStaffId, date);
        return count != null ? count : 0;
    }

    private Map<String, Object> getPreviousAppointment(UUID careStaffId, LocalDateTime before) {
        String sql = """
            SELECT appointment_id, scheduled_start_time, scheduled_end_time
            FROM appointment
            WHERE carestaff_id = ?
            AND scheduled_end_time <= ?
            AND status IN ('scheduled', 'in_progress')
            ORDER BY scheduled_end_time DESC
            LIMIT 1
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, careStaffId, before);
        return results.isEmpty() ? null : results.get(0);
    }

    private Map<String, Object> getNextAppointment(UUID careStaffId, LocalDateTime after) {
        String sql = """
            SELECT appointment_id, scheduled_start_time, scheduled_end_time
            FROM appointment
            WHERE carestaff_id = ?
            AND scheduled_start_time >= ?
            AND status IN ('scheduled', 'in_progress')
            ORDER BY scheduled_start_time ASC
            LIMIT 1
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, careStaffId, after);
        return results.isEmpty() ? null : results.get(0);
    }

    private double estimateTravelTime(Object from, Object to) {
        // Simplified travel time estimation
        // In a real system, this would use geographic coordinates and routing services
        return 20.0; // Default 20 minutes
    }

    private double estimateTravelTime(ServiceRequest.TimeWindow timeWindow, Map<String, Object> appointment) {
        return 20.0; // Simplified
    }

    private boolean isWithinShift(LocalTime time, LocalTime shiftStart, LocalTime shiftEnd) {
        return !time.isBefore(shiftStart) && time.isBefore(shiftEnd);
    }

    // Record classes for results
    public record AvailabilityWindowScore(double score, String rationale) {}
    private record BufferTimeScore(double score, String rationale) {}
    private record PreferredTimeScore(double score, String rationale) {}
    private record WorkloadDistributionScore(double score, String rationale) {}
    private record TravelTimeScore(double score, String rationale) {}
    private record ShiftBoundaryScore(double score, String rationale) {}
    private record StaffPreferredHours(LocalTime startTime, LocalTime endTime) {}
}
