package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.Set;

/**
 * Comprehensive test suite for state compliance constraints in homecare scheduling.
 * Tests state licensing requirements for care staff visiting patients across state lines.
 */
@DisplayName("State Compliance Constraint Tests")
class StateComplianceConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic State Compliance Validation")
    class BasicStateComplianceValidation {

        @Test
        @DisplayName("testStateComplianceConstraint_whenStaffLicensedInPatientState_shouldPass")
        void testStateComplianceConstraint_whenStaffLicensedInPatientState_shouldPass() {
            // Given: Care staff licensed in California
            CareStaff caStaff = testDataFactory.createCareStaffWithStateLicenses("caStaff", Set.of("CA"));
            
            // And: Patient located in California
            HomecareVisit caVisit = testDataFactory.createVisitInState("caVisit", "CA");
            caVisit.setAssignedCareStaffId("caStaff");
            
            // When: Verifying the constraint
            // Then: Should pass (staff is licensed in patient's state)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(caStaff, caVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenStaffNotLicensedInPatientState_shouldFail")
        void testStateComplianceConstraint_whenStaffNotLicensedInPatientState_shouldFail() {
            // Given: Care staff licensed only in Texas
            CareStaff txStaff = testDataFactory.createCareStaffWithStateLicenses("txStaff", Set.of("TX"));
            
            // And: Patient located in California
            HomecareVisit caVisit = testDataFactory.createVisitInState("caVisit", "CA");
            caVisit.setAssignedCareStaffId("txStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff not licensed in patient's state)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(txStaff, caVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenVisitUnassigned_shouldPass")
        void testStateComplianceConstraint_whenVisitUnassigned_shouldPass() {
            // Given: Care staff with any license
            CareStaff anyStaff = testDataFactory.createCareStaffWithStateLicenses("anyStaff", Set.of("NY"));
            
            // And: Unassigned visit
            HomecareVisit unassignedVisit = testDataFactory.createVisitInState("unassignedVisit", "CA");
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (constraint only applies to assigned visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(anyStaff, unassignedVisit)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Multi-State License Scenarios")
    class MultiStateLicenseScenarios {

        @Test
        @DisplayName("testStateComplianceConstraint_whenStaffHasMultipleLicenses_shouldMatchCorrectly")
        void testStateComplianceConstraint_whenStaffHasMultipleLicenses_shouldMatchCorrectly() {
            // Given: Care staff licensed in multiple states
            CareStaff multiStateStaff = testDataFactory.createCareStaffWithStateLicenses("multiStateStaff", 
                    Set.of("CA", "NV", "AZ"));
            
            // And: Visits in different states where staff is licensed
            HomecareVisit caVisit = testDataFactory.createVisitInState("caVisit", "CA");
            caVisit.setAssignedCareStaffId("multiStateStaff");
            
            HomecareVisit nvVisit = testDataFactory.createVisitInState("nvVisit", "NV");
            nvVisit.setAssignedCareStaffId("multiStateStaff");
            
            HomecareVisit azVisit = testDataFactory.createVisitInState("azVisit", "AZ");
            azVisit.setAssignedCareStaffId("multiStateStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (staff licensed in all relevant states)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(multiStateStaff, caVisit, nvVisit, azVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenStaffPartiallyLicensed_shouldFailOnlyUnlicensedStates")
        void testStateComplianceConstraint_whenStaffPartiallyLicensed_shouldFailOnlyUnlicensedStates() {
            // Given: Care staff licensed in some but not all required states
            CareStaff partialStaff = testDataFactory.createCareStaffWithStateLicenses("partialStaff", 
                    Set.of("CA", "NV"));
            
            // And: Visits in licensed and unlicensed states
            HomecareVisit licensedVisit = testDataFactory.createVisitInState("licensedVisit", "CA");
            licensedVisit.setAssignedCareStaffId("partialStaff");
            
            HomecareVisit unlicensedVisit = testDataFactory.createVisitInState("unlicensedVisit", "TX");
            unlicensedVisit.setAssignedCareStaffId("partialStaff");
            
            // When: Verifying the constraint
            // Then: Should penalize only the unlicensed state visit
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(partialStaff, licensedVisit, unlicensedVisit)
                    .penalizesBy(1);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Special Scenarios")
    class EdgeCasesAndSpecialScenarios {

        @Test
        @DisplayName("testStateComplianceConstraint_whenStaffHasNoLicenses_shouldFailAllAssignments")
        void testStateComplianceConstraint_whenStaffHasNoLicenses_shouldFailAllAssignments() {
            // Given: Care staff with no state licenses
            CareStaff unlicensedStaff = testDataFactory.createCareStaffWithStateLicenses("unlicensedStaff", Set.of());
            
            // And: Visit in any state
            HomecareVisit anyVisit = testDataFactory.createVisitInState("anyVisit", "CA");
            anyVisit.setAssignedCareStaffId("unlicensedStaff");
            
            // When: Verifying the constraint
            // Then: Should fail (staff has no licenses)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(unlicensedStaff, anyVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenPatientLocationUnknown_shouldPass")
        void testStateComplianceConstraint_whenPatientLocationUnknown_shouldPass() {
            // Given: Care staff with any license
            CareStaff anyStaff = testDataFactory.createCareStaffWithStateLicenses("anyStaff", Set.of("CA"));
            
            // And: Visit with unknown/null state
            HomecareVisit unknownStateVisit = testDataFactory.createBasicVisit("unknownStateVisit");
            unknownStateVisit.setAssignedCareStaffId("anyStaff");
            // Note: State will be null/unknown in this case
            
            // When: Verifying the constraint
            // Then: Should pass (constraint may not apply without state info)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(anyStaff, unknownStateVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenCaseSensitiveStateComparison_shouldHandleCorrectly")
        void testStateComplianceConstraint_whenCaseSensitiveStateComparison_shouldHandleCorrectly() {
            // Given: Care staff licensed with lowercase state code
            CareStaff staff = testDataFactory.createCareStaffWithStateLicenses("staff", Set.of("ca"));
            
            // And: Patient location with uppercase state code
            HomecareVisit visit = testDataFactory.createVisitInState("visit", "CA");
            visit.setAssignedCareStaffId("staff");
            
            // When: Verifying the constraint
            // Then: Should handle case sensitivity appropriately
            // Note: This test verifies the constraint implementation handles case sensitivity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(staff, visit)
                    .penalizesBy(0); // Assuming case-insensitive matching
        }
    }

    @Nested
    @DisplayName("Multiple Staff and State Combinations")
    class MultipleStaffAndStateCombinations {

        @Test
        @DisplayName("testStateComplianceConstraint_whenMultipleStaffDifferentStates_shouldEvaluateIndependently")
        void testStateComplianceConstraint_whenMultipleStaffDifferentStates_shouldEvaluateIndependently() {
            // Given: Staff licensed in different states
            CareStaff caStaff = testDataFactory.createCareStaffWithStateLicenses("caStaff", Set.of("CA"));
            CareStaff txStaff = testDataFactory.createCareStaffWithStateLicenses("txStaff", Set.of("TX"));
            CareStaff nyStaff = testDataFactory.createCareStaffWithStateLicenses("nyStaff", Set.of("NY"));
            
            // And: Visits properly assigned to staff licensed in respective states
            HomecareVisit caVisit = testDataFactory.createVisitInState("caVisit", "CA");
            caVisit.setAssignedCareStaffId("caStaff");
            
            HomecareVisit txVisit = testDataFactory.createVisitInState("txVisit", "TX");
            txVisit.setAssignedCareStaffId("txStaff");
            
            HomecareVisit nyVisit = testDataFactory.createVisitInState("nyVisit", "NY");
            nyVisit.setAssignedCareStaffId("nyStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (each staff assigned to appropriate state)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(caStaff, txStaff, nyStaff, caVisit, txVisit, nyVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenCrossStateViolations_shouldPenalizeCorrectly")
        void testStateComplianceConstraint_whenCrossStateViolations_shouldPenalizeCorrectly() {
            // Given: Staff with limited state licenses
            CareStaff caOnlyStaff = testDataFactory.createCareStaffWithStateLicenses("caOnlyStaff", Set.of("CA"));
            CareStaff txOnlyStaff = testDataFactory.createCareStaffWithStateLicenses("txOnlyStaff", Set.of("TX"));
            
            // And: Cross-state violations
            HomecareVisit caToTxViolation = testDataFactory.createVisitInState("caToTxViolation", "TX");
            caToTxViolation.setAssignedCareStaffId("caOnlyStaff"); // CA staff assigned to TX visit
            
            HomecareVisit txToCaViolation = testDataFactory.createVisitInState("txToCaViolation", "CA");
            txToCaViolation.setAssignedCareStaffId("txOnlyStaff"); // TX staff assigned to CA visit
            
            // And: One correct assignment
            HomecareVisit correctAssignment = testDataFactory.createVisitInState("correctAssignment", "CA");
            correctAssignment.setAssignedCareStaffId("caOnlyStaff");
            
            // When: Verifying the constraint
            // Then: Should penalize exactly 2 violations
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(caOnlyStaff, txOnlyStaff, caToTxViolation, txToCaViolation, correctAssignment)
                    .penalizesBy(2);
        }

        @Test
        @DisplayName("testStateComplianceConstraint_whenNationalLicenseStaff_shouldPassAllStates")
        void testStateComplianceConstraint_whenNationalLicenseStaff_shouldPassAllStates() {
            // Given: Staff with licenses in many states (simulating national coverage)
            CareStaff nationalStaff = testDataFactory.createCareStaffWithStateLicenses("nationalStaff", 
                    Set.of("CA", "TX", "NY", "FL", "IL", "PA", "OH", "GA", "NC", "MI"));
            
            // And: Visits across multiple states
            HomecareVisit visit1 = testDataFactory.createVisitInState("visit1", "CA");
            visit1.setAssignedCareStaffId("nationalStaff");
            
            HomecareVisit visit2 = testDataFactory.createVisitInState("visit2", "TX");
            visit2.setAssignedCareStaffId("nationalStaff");
            
            HomecareVisit visit3 = testDataFactory.createVisitInState("visit3", "NY");
            visit3.setAssignedCareStaffId("nationalStaff");
            
            HomecareVisit visit4 = testDataFactory.createVisitInState("visit4", "FL");
            visit4.setAssignedCareStaffId("nationalStaff");
            
            // When: Verifying the constraint
            // Then: All should pass (staff licensed in all states)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceValidation)
                    .given(nationalStaff, visit1, visit2, visit3, visit4)
                    .penalizesBy(0);
        }
    }
}
