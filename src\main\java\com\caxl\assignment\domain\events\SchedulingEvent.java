package com.caxl.assignment.domain.events;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Real-time scheduling event for dynamic re-optimization triggers.
 * Supports all types of scheduling disruptions and changes.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchedulingEvent {

    @JsonProperty("event_id")
    private String eventId;

    @JsonProperty("event_type")
    private EventType eventType;

    @JsonProperty("timestamp")
    private LocalDateTime timestamp;

    @JsonProperty("priority")
    private EventPriority priority;

    @JsonProperty("affected_entity_id")
    private String affectedEntityId;

    @JsonProperty("affected_entity_type")
    private EntityType affectedEntityType;

    @JsonProperty("event_data")
    private Map<String, Object> eventData;

    @JsonProperty("requires_immediate_reoptimization")
    private boolean requiresImmediateReoptimization;

    @JsonProperty("affected_schedule_region")
    private String affectedScheduleRegion;

    /**
     * Event types for real-time scheduling system.
     */
    public enum EventType {
        // Caregiver events
        CAREGIVER_UNAVAILABLE("caregiver_unavailable"),
        CAREGIVER_AVAILABLE("caregiver_available"),
        CAREGIVER_RUNNING_LATE("caregiver_running_late"),
        CAREGIVER_OVERTIME_REQUEST("caregiver_overtime_request"),
        
        // Patient events
        URGENT_VISIT_REQUEST("urgent_visit_request"),
        VISIT_CANCELLATION("visit_cancellation"),
        VISIT_RESCHEDULING_REQUEST("visit_rescheduling_request"),
        PATIENT_TIME_WINDOW_CHANGE("patient_time_window_change"),
        
        // External events
        TRAFFIC_DELAY("traffic_delay"),
        WEATHER_ALERT("weather_alert"),
        EMERGENCY_OVERRIDE("emergency_override"),
        
        // System events
        BULK_SCHEDULE_UPDATE("bulk_schedule_update"),
        CONSTRAINT_CONFIGURATION_CHANGE("constraint_configuration_change");

        private final String value;

        EventType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Event priority for processing order.
     */
    public enum EventPriority {
        CRITICAL(1),    // Immediate reoptimization required
        HIGH(2),        // Reoptimization within 1 minute
        MEDIUM(3),      // Reoptimization within 5 minutes
        LOW(4);         // Batch reoptimization

        private final int level;

        EventPriority(int level) {
            this.level = level;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * Type of entity affected by the event.
     */
    public enum EntityType {
        CAREGIVER("caregiver"),
        PATIENT("patient"),
        VISIT("visit"),
        ROUTE("route"),
        SCHEDULE("schedule");

        private final String value;

        EntityType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
