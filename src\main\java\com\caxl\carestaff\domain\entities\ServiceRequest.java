package com.caxl.carestaff.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Domain entity representing a service request for carestaff assignment.
 * Framework-agnostic domain model extending existing Patient model concepts.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceRequest {

    private UUID requestId;
    private UUID patientId;
    private String status; // pending, scheduled, completed, cancelled
    private List<UUID> requiredSkillIds;
    private List<UUID> requiredCertificationIds;
    private TimeWindow timeWindow;
    private String visitType;
    private int priority;
    private int workloadPoints;
    private String specialInstructions;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * Time window for the service request.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeWindow {
        private LocalDateTime arrivalWindowStart;
        private LocalDateTime arrivalWindowEnd;
        private int visitDurationMinutes;
        private LocalDateTime preferredStartTime;
        private LocalDateTime latestEndTime;
    }

    /**
     * Check if the request is in pending status.
     */
    public boolean isPending() {
        return "pending".equalsIgnoreCase(status);
    }

    /**
     * Check if the request is scheduled.
     */
    public boolean isScheduled() {
        return "scheduled".equalsIgnoreCase(status);
    }

    /**
     * Update the status of the request.
     */
    public void updateStatus(String newStatus) {
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }
}
