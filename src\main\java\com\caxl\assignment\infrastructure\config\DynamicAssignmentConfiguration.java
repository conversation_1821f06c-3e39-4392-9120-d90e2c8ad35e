package com.caxl.assignment.infrastructure.config;

import ai.timefold.solver.core.api.solver.SolverFactory;
import ai.timefold.solver.core.config.solver.SolverConfig;
import ai.timefold.solver.core.config.solver.termination.TerminationConfig;
import com.caxl.assignment.domain.constraints.assignment.DynamicMultiLevelConstraintProvider;
import com.caxl.assignment.domain.models.assignment.DynamicClinicianAssignment;
import com.caxl.assignment.domain.models.assignment.MultiLevelAssignmentSolution;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * Configuration class for Dynamic Multi-Level Clinician Assignment System.
 * Configures Timefold Solver, Jackson ObjectMapper, and system properties.
 */
@Configuration
@EnableConfigurationProperties(DynamicAssignmentConfiguration.AssignmentProperties.class)
@Slf4j
public class DynamicAssignmentConfiguration {

    /**
     * Configure Timefold Solver factory for multi-level assignment optimization.
     */
    @Bean
    public SolverFactory<MultiLevelAssignmentSolution> multiLevelSolverFactory(
            AssignmentProperties properties) {
        
        log.info("Configuring Timefold Solver for dynamic multi-level assignment");
        
        SolverConfig solverConfig = new SolverConfig()
                .withSolutionClass(MultiLevelAssignmentSolution.class)
                .withEntityClasses(DynamicClinicianAssignment.class)
                .withConstraintProviderClass(DynamicMultiLevelConstraintProvider.class);
        
        // Configure termination
        TerminationConfig terminationConfig = new TerminationConfig()
                .withSpentLimit(Duration.ofSeconds(properties.getSolver().getTerminationSeconds()))
                .withUnimprovedSpentLimit(Duration.ofSeconds(properties.getSolver().getUnimprovedSeconds()));
        
        if (properties.getSolver().getBestScoreLimit() != null) {
            terminationConfig.withBestScoreLimit(properties.getSolver().getBestScoreLimit());
        }
        
        solverConfig.withTerminationConfig(terminationConfig);
        
        // Configure environment mode
        if (properties.getSolver().getEnvironmentMode() != null) {
            solverConfig.withEnvironmentMode(
                ai.timefold.solver.core.config.solver.EnvironmentMode.valueOf(
                    properties.getSolver().getEnvironmentMode().toUpperCase()));
        }
        
        log.info("Solver configured with termination limit: {}s, unimproved limit: {}s",
                properties.getSolver().getTerminationSeconds(),
                properties.getSolver().getUnimprovedSeconds());
        
        return SolverFactory.create(solverConfig);
    }

    /**
     * Configure ObjectMapper with Java Time support for JSON processing.
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.findAndRegisterModules();
        return mapper;
    }

    /**
     * Configuration properties for the assignment system.
     */
    @ConfigurationProperties(prefix = "assignment")
    public static class AssignmentProperties {
        
        private Rules rules = new Rules();
        private Solver solver = new Solver();
        private Relaxation relaxation = new Relaxation();
        private Monitoring monitoring = new Monitoring();

        // Getters and setters
        public Rules getRules() { return rules; }
        public void setRules(Rules rules) { this.rules = rules; }
        
        public Solver getSolver() { return solver; }
        public void setSolver(Solver solver) { this.solver = solver; }
        
        public Relaxation getRelaxation() { return relaxation; }
        public void setRelaxation(Relaxation relaxation) { this.relaxation = relaxation; }
        
        public Monitoring getMonitoring() { return monitoring; }
        public void setMonitoring(Monitoring monitoring) { this.monitoring = monitoring; }

        /**
         * Rules configuration properties.
         */
        public static class Rules {
            private String configPath = "config/dynamic-assignment-rules.json";
            private long cacheDurationMinutes = 5;
            private boolean validationEnabled = true;
            private boolean hotReloadEnabled = false;

            // Getters and setters
            public String getConfigPath() { return configPath; }
            public void setConfigPath(String configPath) { this.configPath = configPath; }
            
            public long getCacheDurationMinutes() { return cacheDurationMinutes; }
            public void setCacheDurationMinutes(long cacheDurationMinutes) { this.cacheDurationMinutes = cacheDurationMinutes; }
            
            public boolean isValidationEnabled() { return validationEnabled; }
            public void setValidationEnabled(boolean validationEnabled) { this.validationEnabled = validationEnabled; }
            
            public boolean isHotReloadEnabled() { return hotReloadEnabled; }
            public void setHotReloadEnabled(boolean hotReloadEnabled) { this.hotReloadEnabled = hotReloadEnabled; }
        }

        /**
         * Solver configuration properties.
         */
        public static class Solver {
            private int terminationSeconds = 30;
            private int unimprovedSeconds = 10;
            private String bestScoreLimit;
            private String environmentMode = "REPRODUCIBLE";
            private boolean moveThreadCountAuto = true;
            private int moveThreadCount = 1;

            // Getters and setters
            public int getTerminationSeconds() { return terminationSeconds; }
            public void setTerminationSeconds(int terminationSeconds) { this.terminationSeconds = terminationSeconds; }
            
            public int getUnimprovedSeconds() { return unimprovedSeconds; }
            public void setUnimprovedSeconds(int unimprovedSeconds) { this.unimprovedSeconds = unimprovedSeconds; }
            
            public String getBestScoreLimit() { return bestScoreLimit; }
            public void setBestScoreLimit(String bestScoreLimit) { this.bestScoreLimit = bestScoreLimit; }
            
            public String getEnvironmentMode() { return environmentMode; }
            public void setEnvironmentMode(String environmentMode) { this.environmentMode = environmentMode; }
            
            public boolean isMoveThreadCountAuto() { return moveThreadCountAuto; }
            public void setMoveThreadCountAuto(boolean moveThreadCountAuto) { this.moveThreadCountAuto = moveThreadCountAuto; }
            
            public int getMoveThreadCount() { return moveThreadCount; }
            public void setMoveThreadCount(int moveThreadCount) { this.moveThreadCount = moveThreadCount; }
        }

        /**
         * Relaxation strategy configuration properties.
         */
        public static class Relaxation {
            private SkillSubstitution skillSubstitution = new SkillSubstitution();
            private ExtendedRadius extendedRadius = new ExtendedRadius();
            private Overtime overtime = new Overtime();
            private Rescheduling rescheduling = new Rescheduling();

            // Getters and setters
            public SkillSubstitution getSkillSubstitution() { return skillSubstitution; }
            public void setSkillSubstitution(SkillSubstitution skillSubstitution) { this.skillSubstitution = skillSubstitution; }
            
            public ExtendedRadius getExtendedRadius() { return extendedRadius; }
            public void setExtendedRadius(ExtendedRadius extendedRadius) { this.extendedRadius = extendedRadius; }
            
            public Overtime getOvertime() { return overtime; }
            public void setOvertime(Overtime overtime) { this.overtime = overtime; }
            
            public Rescheduling getRescheduling() { return rescheduling; }
            public void setRescheduling(Rescheduling rescheduling) { this.rescheduling = rescheduling; }

            public static class SkillSubstitution {
                private boolean enabled = true;
                private double penalty = 0.5;

                public boolean isEnabled() { return enabled; }
                public void setEnabled(boolean enabled) { this.enabled = enabled; }
                public double getPenalty() { return penalty; }
                public void setPenalty(double penalty) { this.penalty = penalty; }
            }

            public static class ExtendedRadius {
                private boolean enabled = true;
                private double maxDistanceKm = 50.0;
                private double penalty = 0.3;

                public boolean isEnabled() { return enabled; }
                public void setEnabled(boolean enabled) { this.enabled = enabled; }
                public double getMaxDistanceKm() { return maxDistanceKm; }
                public void setMaxDistanceKm(double maxDistanceKm) { this.maxDistanceKm = maxDistanceKm; }
                public double getPenalty() { return penalty; }
                public void setPenalty(double penalty) { this.penalty = penalty; }
            }

            public static class Overtime {
                private boolean enabled = false;
                private int maxHours = 2;
                private double penalty = 0.8;

                public boolean isEnabled() { return enabled; }
                public void setEnabled(boolean enabled) { this.enabled = enabled; }
                public int getMaxHours() { return maxHours; }
                public void setMaxHours(int maxHours) { this.maxHours = maxHours; }
                public double getPenalty() { return penalty; }
                public void setPenalty(double penalty) { this.penalty = penalty; }
            }

            public static class Rescheduling {
                private boolean enabled = true;
                private int windowHours = 24;
                private double penalty = 0.2;

                public boolean isEnabled() { return enabled; }
                public void setEnabled(boolean enabled) { this.enabled = enabled; }
                public int getWindowHours() { return windowHours; }
                public void setWindowHours(int windowHours) { this.windowHours = windowHours; }
                public double getPenalty() { return penalty; }
                public void setPenalty(double penalty) { this.penalty = penalty; }
            }
        }

        /**
         * Monitoring and metrics configuration properties.
         */
        public static class Monitoring {
            private boolean enabled = true;
            private boolean detailedLogging = false;
            private int metricsRetentionDays = 30;
            private boolean performanceTracking = true;

            // Getters and setters
            public boolean isEnabled() { return enabled; }
            public void setEnabled(boolean enabled) { this.enabled = enabled; }
            
            public boolean isDetailedLogging() { return detailedLogging; }
            public void setDetailedLogging(boolean detailedLogging) { this.detailedLogging = detailedLogging; }
            
            public int getMetricsRetentionDays() { return metricsRetentionDays; }
            public void setMetricsRetentionDays(int metricsRetentionDays) { this.metricsRetentionDays = metricsRetentionDays; }
            
            public boolean isPerformanceTracking() { return performanceTracking; }
            public void setPerformanceTracking(boolean performanceTracking) { this.performanceTracking = performanceTracking; }
        }
    }
}
