package com.caxl.assignment.infrastructure.persistence.repository;

import com.caxl.assignment.infrastructure.persistence.entity.MatchingConfigurationEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for matching configuration entities.
 */
@Repository
public interface MatchingConfigurationRepository extends JpaRepository<MatchingConfigurationEntity, UUID> {

    /**
     * Find the active matching configuration.
     */
    @Query("SELECT mc FROM MatchingConfigurationEntity mc WHERE mc.isActive = true")
    Optional<MatchingConfigurationEntity> findActiveConfiguration();

    /**
     * Find configuration by name.
     */
    Optional<MatchingConfigurationEntity> findByConfigName(String configName);
}
