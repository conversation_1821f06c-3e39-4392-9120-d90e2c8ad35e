package com.caxl.assignment.infrastructure.carestaff.persistence.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * JPA entity for matching configuration storage.
 * Maps to MATCHING_CONFIGURATION table with JSONB support.
 */
@Entity
@Table(name = "matching_configuration")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchingConfigurationEntity {

    @Id
    @Column(name = "config_id")
    private UUID configId;

    @Column(name = "config_name", unique = true, nullable = false)
    private String configName;

    @Type(JsonType.class)
    @Column(name = "criteria_json", columnDefinition = "jsonb")
    private String criteriaJson;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = false;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "created_by")
    private String createdBy;

    @PrePersist
    protected void onCreate() {
        if (configId == null) {
            configId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
