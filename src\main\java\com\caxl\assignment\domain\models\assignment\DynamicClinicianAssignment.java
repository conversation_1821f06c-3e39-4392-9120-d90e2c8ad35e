package com.caxl.assignment.domain.models.assignment;

import ai.timefold.solver.core.api.domain.entity.PlanningEntity;
import ai.timefold.solver.core.api.domain.lookup.PlanningId;
import ai.timefold.solver.core.api.domain.variable.PlanningVariable;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Timefold planning entity for dynamic multi-level clinician assignment.
 * Supports flexible assignment with relaxation strategies and multi-level processing.
 */
@PlanningEntity
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicClinicianAssignment {

    @PlanningId
    private String id;

    /**
     * Planning variable: Assigned clinician (optimized by Timefold).
     * Can be null if no feasible assignment is found.
     */
    @PlanningVariable(valueRangeProviderRefs = "clinicianRange", nullable = true)
    private Clinician assignedClinician;

    /**
     * Planning variable: Assigned care staff (for homecare scenarios).
     * Alternative to assignedClinician for specialized care.
     */
    @PlanningVariable(valueRangeProviderRefs = "careStaffRange", nullable = true)
    private CareStaff assignedCareStaff;

    /**
     * The assignment request being processed.
     */
    private ClinicianAssignmentRequest assignmentRequest;

    /**
     * Processing level at which this assignment was made (1, 2, or 3).
     */
    @Builder.Default
    private int processingLevel = 1;

    /**
     * Assignment score based on rule evaluation.
     */
    @Builder.Default
    private double assignmentScore = 0.0;

    /**
     * Indicates if relaxation strategies were used.
     */
    @Builder.Default
    private boolean relaxationApplied = false;

    /**
     * Details of applied relaxation strategies.
     */
    private RelaxationDetails relaxationDetails;

    /**
     * Assignment timestamp.
     */
    @Builder.Default
    private LocalDateTime assignmentTimestamp = LocalDateTime.now();

    /**
     * Rule evaluation results for this assignment.
     */
    private Map<String, Boolean> ruleEvaluationResults;

    /**
     * Soft constraint scores for optimization.
     */
    private Map<String, Double> softConstraintScores;

    /**
     * Details of relaxation strategies applied to this assignment.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelaxationDetails {
        
        @Builder.Default
        private boolean skillSubstitutionUsed = false;
        
        @Builder.Default
        private boolean extendedRadiusUsed = false;
        
        @Builder.Default
        private boolean overtimeAssignmentUsed = false;
        
        @Builder.Default
        private boolean visitReschedulingUsed = false;
        
        private Map<String, String> skillSubstitutions;
        
        private Double originalDistance;
        
        private Double extendedDistance;
        
        private Integer overtimeHours;
        
        private LocalDateTime originalScheduledTime;
        
        private LocalDateTime rescheduledTime;
        
        private String relaxationReason;
        
        private double relaxationPenalty;
    }

    /**
     * Get the effective assigned clinician (either direct clinician or care staff).
     */
    public Clinician getEffectiveAssignedClinician() {
        if (assignedClinician != null) {
            return assignedClinician;
        }
        return assignedCareStaff; // CareStaff extends Clinician
    }

    /**
     * Check if this assignment is complete (has an assigned clinician).
     */
    public boolean isComplete() {
        return assignedClinician != null || assignedCareStaff != null;
    }

    /**
     * Check if this assignment is feasible based on hard constraints.
     */
    public boolean isFeasible() {
        if (!isComplete()) {
            return false;
        }
        
        // Check if all hard constraint rules are satisfied
        if (ruleEvaluationResults != null) {
            return ruleEvaluationResults.entrySet().stream()
                    .filter(entry -> entry.getKey().contains("hard") || entry.getKey().contains("level_1"))
                    .allMatch(Map.Entry::getValue);
        }
        
        return true;
    }

    /**
     * Calculate total assignment score including relaxation penalties.
     */
    public double getTotalScore() {
        double baseScore = assignmentScore;
        
        if (relaxationApplied && relaxationDetails != null) {
            baseScore -= relaxationDetails.getRelaxationPenalty();
        }
        
        // Add soft constraint scores
        if (softConstraintScores != null) {
            double softScore = softConstraintScores.values().stream()
                    .mapToDouble(Double::doubleValue)
                    .sum();
            baseScore += softScore;
        }
        
        return baseScore;
    }

    /**
     * Get assignment quality rating (0.0 to 1.0).
     */
    public double getQualityRating() {
        if (!isComplete()) {
            return 0.0;
        }
        
        double baseQuality = 1.0;
        
        // Reduce quality based on processing level
        switch (processingLevel) {
            case 2 -> baseQuality *= 0.8; // Level 2 assignments are good but not perfect
            case 3 -> baseQuality *= 0.6; // Level 3 assignments required relaxation
        }
        
        // Further reduce quality if relaxation was applied
        if (relaxationApplied && relaxationDetails != null) {
            baseQuality *= (1.0 - (relaxationDetails.getRelaxationPenalty() / 10.0));
        }
        
        return Math.max(0.0, Math.min(1.0, baseQuality));
    }

    /**
     * Check if this assignment required skill substitution.
     */
    public boolean usedSkillSubstitution() {
        return relaxationApplied && relaxationDetails != null && 
               relaxationDetails.isSkillSubstitutionUsed();
    }

    /**
     * Check if this assignment used extended service radius.
     */
    public boolean usedExtendedRadius() {
        return relaxationApplied && relaxationDetails != null && 
               relaxationDetails.isExtendedRadiusUsed();
    }

    /**
     * Check if this assignment required overtime.
     */
    public boolean usedOvertime() {
        return relaxationApplied && relaxationDetails != null && 
               relaxationDetails.isOvertimeAssignmentUsed();
    }

    /**
     * Check if this assignment required visit rescheduling.
     */
    public boolean usedRescheduling() {
        return relaxationApplied && relaxationDetails != null && 
               relaxationDetails.isVisitReschedulingUsed();
    }

    /**
     * Get the distance between patient and assigned clinician.
     */
    public Double getAssignmentDistance() {
        if (relaxationDetails != null && relaxationDetails.getExtendedDistance() != null) {
            return relaxationDetails.getExtendedDistance();
        }
        if (relaxationDetails != null && relaxationDetails.getOriginalDistance() != null) {
            return relaxationDetails.getOriginalDistance();
        }
        return null;
    }

    /**
     * Get assignment summary for reporting.
     */
    public String getAssignmentSummary() {
        if (!isComplete()) {
            return "Unassigned";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("Level ").append(processingLevel).append(" assignment");
        
        if (relaxationApplied) {
            summary.append(" (with relaxation)");
        }
        
        summary.append(" - Quality: ").append(String.format("%.1f%%", getQualityRating() * 100));
        
        return summary.toString();
    }

    /**
     * Create a copy of this assignment for Level 3 relaxation processing.
     */
    public DynamicClinicianAssignment createRelaxedCopy() {
        return DynamicClinicianAssignment.builder()
                .id(id + "_relaxed")
                .assignedClinician(null) // Reset for re-assignment
                .assignedCareStaff(null)
                .assignmentRequest(assignmentRequest)
                .processingLevel(3)
                .assignmentScore(0.0)
                .relaxationApplied(false)
                .relaxationDetails(null)
                .assignmentTimestamp(LocalDateTime.now())
                .ruleEvaluationResults(null)
                .softConstraintScores(null)
                .build();
    }
}
