-- CareStaff Matching Service Database Initialization
-- This script creates the necessary tables for the carestaff matching service

-- Enable PostGIS extension
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create matching_configuration table
CREATE TABLE IF NOT EXISTS matching_configuration (
    config_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_name VARCHAR(255) UNIQUE NOT NULL,
    criteria_json JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VA<PERSON><PERSON><PERSON>(255)
);

-- Create service_request table
CREATE TABLE IF NOT EXISTS service_request (
    request_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    required_skill_ids JSON<PERSON>,
    required_certification_ids J<PERSON>N<PERSON>,
    arrival_window_start TIMESTAMP,
    arrival_window_end TIMESTAMP,
    visit_duration_minutes INTEGER,
    preferred_start_time TIMESTAMP,
    latest_end_time TIMESTAMP,
    visit_type VARCHAR(100),
    priority INTEGER DEFAULT 3,
    workload_points INTEGER DEFAULT 1,
    special_instructions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create match_suggestion table
CREATE TABLE IF NOT EXISTS match_suggestion (
    suggestion_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_request_id UUID NOT NULL,
    suggested_carestaff_id UUID NOT NULL,
    score DOUBLE PRECISION NOT NULL,
    rationale TEXT,
    suggested_datetime TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Create match_override table
CREATE TABLE IF NOT EXISTS match_override (
    override_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_request_id UUID NOT NULL,
    selected_carestaff_id UUID NOT NULL,
    scheduler_id UUID,
    reason VARCHAR(255),
    explanation TEXT,
    override_datetime TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create appointment table
CREATE TABLE IF NOT EXISTS appointment (
    appointment_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    service_request_id UUID,
    carestaff_id UUID NOT NULL,
    patient_id UUID NOT NULL,
    scheduled_start_time TIMESTAMP,
    scheduled_end_time TIMESTAMP,
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    status VARCHAR(50) DEFAULT 'scheduled',
    notes TEXT,
    scheduler_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create carestaff_has_skill table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS carestaff_has_skill (
    mapping_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    carestaff_id UUID NOT NULL,
    skill_id UUID NOT NULL,
    proficiency_level VARCHAR(50),
    years_experience INTEGER,
    is_certified BOOLEAN DEFAULT FALSE,
    certification_date TIMESTAMP,
    certification_expiry TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(carestaff_id, skill_id)
);

-- Create sample tables for demonstration (simplified versions)
-- These would typically reference the existing schema

-- Location table with PostGIS support
CREATE TABLE IF NOT EXISTS location (
    location_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    location_type VARCHAR(100),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    country VARCHAR(50),
    postal_code VARCHAR(20),
    coordinates GEOMETRY(POINT, 4326),
    geofence_boundary GEOMETRY(POLYGON, 4326),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Geofences table
CREATE TABLE IF NOT EXISTS geofences (
    geofence_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    geofence_name VARCHAR(255),
    geofence_type VARCHAR(100),
    boundary GEOMETRY(POLYGON, 4326),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Simplified patient table
CREATE TABLE IF NOT EXISTS patient (
    patient_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enc_first_name VARCHAR(255),
    enc_last_name VARCHAR(255),
    location_id UUID REFERENCES location(location_id),
    preferred_language VARCHAR(50),
    preferred_carestaff_ids JSONB,
    barred_carestaff_ids JSONB,
    medical_conditions TEXT,
    special_instructions TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Simplified carestaff table
CREATE TABLE IF NOT EXISTS caresstaff_pe (
    carestaff_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enc_first_name VARCHAR(255),
    enc_last_name VARCHAR(255),
    location_id UUID REFERENCES location(location_id),
    experience_years INTEGER DEFAULT 0,
    languages JSONB,
    operating_zones JSONB,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_matching_config_active ON matching_configuration(is_active);
CREATE INDEX IF NOT EXISTS idx_service_request_status ON service_request(status);
CREATE INDEX IF NOT EXISTS idx_service_request_patient ON service_request(patient_id);
CREATE INDEX IF NOT EXISTS idx_match_suggestion_request ON match_suggestion(service_request_id);
CREATE INDEX IF NOT EXISTS idx_match_suggestion_active ON match_suggestion(is_active);
CREATE INDEX IF NOT EXISTS idx_appointment_carestaff ON appointment(carestaff_id);
CREATE INDEX IF NOT EXISTS idx_appointment_patient ON appointment(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointment_status ON appointment(status);
CREATE INDEX IF NOT EXISTS idx_carestaff_skill_mapping ON carestaff_has_skill(carestaff_id, skill_id);

-- Spatial indexes
CREATE INDEX IF NOT EXISTS idx_location_coordinates ON location USING GIST(coordinates);
CREATE INDEX IF NOT EXISTS idx_location_geofence ON location USING GIST(geofence_boundary);
CREATE INDEX IF NOT EXISTS idx_geofences_boundary ON geofences USING GIST(boundary);

-- Insert sample data for demonstration
INSERT INTO matching_configuration (config_id, config_name, criteria_json, is_active, created_by)
VALUES (
    uuid_generate_v4(),
    'default_demo_config',
    '{
      "filters": {
        "requiredSkillsMustMatchAll": false,
        "proximitySearchRadiusKm": 50.0,
        "mustRespectGeoServiceArea": true,
        "mustRespectAvailability": true,
        "mustNotBeBarred": true,
        "minScoreThreshold": 0.0
      },
      "scoring_weights": {
        "skillMatchBonusPerRequiredSkill": 10.0,
        "skillMatchAllBonus": 50.0,
        "proximityKmPenalty": -1.0,
        "geoServiceAreaBonus": 20.0,
        "availabilityWindowFitPenaltyPerMinuteDeviation": -0.5,
        "preferredCareStaffBonus": 15.0,
        "continuityBonusPerRecentVisit": 5.0,
        "languageMatchBonus": 8.0,
        "experienceLevelBonusPerYear": 2.0
      },
      "geography": {
        "staffServiceGeofenceTypes": ["service_area", "county_area"],
        "geofenceStrictContainmentOnly": true
      },
      "availability": {
        "overlapThresholdMinutes": 1,
        "minTimeBeforeVisitMinutes": 30,
        "minTimeAfterVisitMinutes": 30
      }
    }',
    TRUE,
    'system'
) ON CONFLICT (config_name) DO NOTHING;

-- Insert sample locations
INSERT INTO location (location_id, location_type, address, city, state, coordinates)
VALUES 
    (uuid_generate_v4(), 'patient_home', '123 Main St', 'Springfield', 'IL', ST_SetSRID(ST_Point(-89.6501, 39.7817), 4326)),
    (uuid_generate_v4(), 'carestaff_base', '456 Oak Ave', 'Springfield', 'IL', ST_SetSRID(ST_Point(-89.6401, 39.7917), 4326)),
    (uuid_generate_v4(), 'service_area', 'Springfield Service Area', 'Springfield', 'IL', ST_SetSRID(ST_Point(-89.6451, 39.7867), 4326))
ON CONFLICT DO NOTHING;

COMMIT;
