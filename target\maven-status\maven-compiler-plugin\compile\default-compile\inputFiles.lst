D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\controllers\AssignmentController.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\controllers\healthcare\EnhancedHomecareSchedulingController.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\controllers\RealTimeSchedulingController.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\dto\EventResponse.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\dto\WhatIfScenarioRequest.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\dto\WhatIfScenarioResponse.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\dtos\AssignmentResponse.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\api\dtos\CreateAssignmentRequest.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\ports\RuleRepository.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\common\BaseService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\common\OptimizationUtils.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\common\ServiceResults.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\common\ValidationUtils.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\enhanced\EnhancedOptimizationService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\healthcare\EnhancedHomecareSchedulingService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\healthcare\GeofencingService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\healthcare\StateComplianceService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\notifications\NotificationService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\realtime\ConstraintConfigurationService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\realtime\RealTimeSchedulingService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\RuleEvaluatorService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\SchedulingService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\application\services\traffic\TrafficAwareService.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\CaxlAssignmentServiceApplication.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\config\AssignmentProperties.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\config\RuleConfiguration.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\config\TimefoldSolverConfig.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\common\BaseBuilder.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\common\CommonDomainPatterns.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\constraints\enhanced\EnhancedSchedulingConstraints.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\constraints\healthcare\EnhancedHomecareConstraintProvider.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\constraints\healthcare\HomecareConstraintProvider_fixed.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\constraints\SchedulingConstraints.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\events\SchedulingEvent.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\exceptions\AssignmentException.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\exceptions\NoFeasibleAssignmentException.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\exceptions\RuleEvaluationException.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\Assignment.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\Clinician.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\Condition.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\enums\OperatorType.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\enums\RuleType.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\Address.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\AvailabilityWindow.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\CarePreferences.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\CareStaff.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\CareTeam.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\ComplianceRequirements.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\GeoZone.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\HomecareSchedule.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\HomecareVisit.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\HomePatient.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\Location.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\MedicalInformation.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\Organization.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\ServicePriority.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\ServiceType.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\StateComplianceRules.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\USState.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\healthcare\WorkingSchedule.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\Patient.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\models\Rule.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\scheduling\ClinicianShift.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\scheduling\PatientAssignment.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\domain\scheduling\SchedulingSolution.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\adapters\RuleRepositoryAdapter.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\common\RepositoryUtils.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\optaplanner\config\TimefoldConfig.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\optaplanner\constraints\enhanced\EnhancedConstraintProvider.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\optaplanner\domain\enhanced\EnhancedAssignmentSolution.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\optaplanner\domain\enhanced\Shift.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\infrastructure\optaplanner\moves\SkillCompatibleMoveFilter.java
D:\Work\Assignment\caxl-optaplanner-assignment-service\src\main\java\com\caxl\assignment\services\RelaxationService.java
