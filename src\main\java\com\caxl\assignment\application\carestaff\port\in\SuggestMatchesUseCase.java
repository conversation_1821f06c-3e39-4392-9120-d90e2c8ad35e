package com.caxl.assignment.application.port.in;

import com.caxl.assignment.domain.entities.MatchSuggestion;

import java.util.List;
import java.util.UUID;

/**
 * Use case interface for suggesting carestaff matches for service requests.
 * Driven port in hexagonal architecture.
 */
public interface SuggestMatchesUseCase {

    /**
     * Suggest carestaff matches for a service request using configurable constraints.
     * 
     * @param requestId The service request ID to find matches for
     * @return List of match suggestions ordered by score (highest first)
     */
    List<MatchSuggestion> suggestMatches(UUID requestId);
}
