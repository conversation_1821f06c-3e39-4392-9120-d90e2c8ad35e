{"dashboard": {"id": null, "title": "Homecare Scheduling Dashboard", "tags": ["homecare", "scheduling", "timefold"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Scheduling Optimization Performance", "type": "stat", "targets": [{"expr": "avg(homecare_optimization_duration_seconds)", "legendFormat": "Avg Optimization Time"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 60}, {"color": "red", "value": 300}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Visit Assignment Rate", "type": "stat", "targets": [{"expr": "rate(homecare_visits_assigned_total[5m])", "legendFormat": "Visits/min"}], "fieldConfig": {"defaults": {"unit": "per_min", "color": {"mode": "thresholds"}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "State Compliance Status", "type": "piechart", "targets": [{"expr": "sum by (compliance_status) (homecare_compliance_checks_total)", "legendFormat": "{{ compliance_status }}"}], "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "Care Staff Utilization", "type": "bargauge", "targets": [{"expr": "homecare_staff_utilization_ratio", "legendFormat": "{{ staff_id }}"}], "fieldConfig": {"defaults": {"unit": "percentunit", "max": 1, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.8}, {"color": "red", "value": 0.95}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}}, {"id": 5, "title": "Geofencing Violations Over Time", "type": "timeseries", "targets": [{"expr": "rate(homecare_geofencing_violations_total[5m])", "legendFormat": "Zone {{ zone_id }}"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "lineWidth": 2, "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}}, {"id": 6, "title": "Service Type Distribution", "type": "piechart", "targets": [{"expr": "sum by (service_type) (homecare_visits_by_service_type_total)", "legendFormat": "{{ service_type }}"}], "gridPos": {"h": 8, "w": 6, "x": 12, "y": 12}}, {"id": 7, "title": "Optimization Score Trends", "type": "timeseries", "targets": [{"expr": "homecare_optimization_hard_score", "legendFormat": "Hard Score"}, {"expr": "homecare_optimization_soft_score", "legendFormat": "Soft Score"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 18, "x": 0, "y": 20}}, {"id": 8, "title": "System Health", "type": "table", "targets": [{"expr": "up", "legendFormat": "{{ job }}"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {}}}], "gridPos": {"h": 6, "w": 24, "x": 0, "y": 28}}]}}