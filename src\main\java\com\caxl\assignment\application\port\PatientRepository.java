package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Patient;
import org.locationtech.jts.geom.Point;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for patient data access.
 * Supports scheduling-focused queries for optimization.
 */
public interface PatientRepository {

    /**
     * Find all patients requiring care on a specific date.
     * 
     * @param schedulingDate Date to schedule care
     * @return List of patients requiring care
     */
    List<Patient> findPatientsRequiringCare(LocalDate schedulingDate);

    /**
     * Find patients by priority level for a specific date.
     * 
     * @param priorityLevel Priority level (1-5, 5 being highest)
     * @param schedulingDate Date to check
     * @return List of patients with specified priority
     */
    List<Patient> findPatientsByPriority(int priorityLevel, LocalDate schedulingDate);

    /**
     * Find patients within a geographic area.
     * 
     * @param centerPoint Center of search area
     * @param radiusKm Search radius in kilometers
     * @param schedulingDate Date of service
     * @return List of patients in the area
     */
    List<Patient> findPatientsInArea(Point centerPoint, double radiusKm, LocalDate schedulingDate);

    /**
     * Find patients with specific care requirements.
     * 
     * @param requiredSkills Required carestaff skills
     * @param schedulingDate Date of service
     * @return List of patients with matching requirements
     */
    List<Patient> findPatientsByRequiredSkills(List<String> requiredSkills, LocalDate schedulingDate);

    /**
     * Find patient by ID.
     * 
     * @param patientId Patient ID
     * @return Patient if found
     */
    Optional<Patient> findById(UUID patientId);

    /**
     * Get patient care history with specific carestaff.
     * 
     * @param patientId Patient ID
     * @param careStaffId Carestaff ID
     * @return Care history information
     */
    CareHistory getCareHistory(UUID patientId, UUID careStaffId);

    /**
     * Update patient care requirements.
     * 
     * @param patientId Patient ID
     * @param newRequirements Updated care requirements
     */
    void updateCareRequirements(UUID patientId, CareRequirements newRequirements);

    /**
     * Get patient scheduling preferences.
     * 
     * @param patientId Patient ID
     * @return Scheduling preferences
     */
    SchedulingPreferences getSchedulingPreferences(UUID patientId);

    /**
     * Find urgent patients requiring immediate attention.
     * 
     * @param schedulingDate Date to check
     * @return List of urgent patients
     */
    List<Patient> findUrgentPatients(LocalDate schedulingDate);

    /**
     * Care history between patient and carestaff.
     */
    record CareHistory(
        UUID patientId,
        UUID careStaffId,
        int totalVisits,
        LocalDateTime lastVisit,
        double averageRating,
        boolean hasPreference,
        List<String> notes
    ) {}

    /**
     * Patient care requirements.
     */
    record CareRequirements(
        List<String> requiredSkills,
        List<String> preferredSkills,
        int estimatedDurationMinutes,
        String specialInstructions,
        boolean requiresContinuityOfCare
    ) {}

    /**
     * Patient scheduling preferences.
     */
    record SchedulingPreferences(
        List<String> preferredTimeSlots,
        List<UUID> preferredCareStaff,
        List<UUID> excludedCareStaff,
        boolean flexibleTiming,
        int maxWaitingDays
    ) {}
}
