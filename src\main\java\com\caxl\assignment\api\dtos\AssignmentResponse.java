package com.caxl.assignment.api.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.caxl.assignment.domain.models.Assignment;

import java.util.List;

/**
 * DTO for assignment response.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AssignmentResponse {

    private List<Assignment> assignments;

    @JsonProperty("optimization_details")
    private OptimizationDetails optimizationDetails;

    /**
     * Details about the optimization process.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimizationDetails {

        @JsonProperty("solving_time_ms")
        private long solvingTimeMs;

        @JsonProperty("assigned_patients")
        private int assignedPatients;

        @JsonProperty("total_patients")
        private int totalPatients;

        @JsonProperty("total_score")
        private double totalScore;

        @JsonProperty("solver_status")
        private String solverStatus;
    }
}
