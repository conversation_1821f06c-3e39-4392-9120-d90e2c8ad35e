package com.caxl.assignment.domain.models.assignment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Domain model representing a request for clinician assignment.
 * Contains all patient requirements and visit details needed for dynamic assignment.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClinicianAssignmentRequest {

    @NotBlank(message = "Patient ID is required")
    @JsonProperty("patient_id")
    private String patientId;

    @NotBlank(message = "Visit ID is required")
    @JsonProperty("visit_id")
    private String visitId;

    @NotNull(message = "Service date and time is required")
    @JsonProperty("service_date_time")
    private LocalDateTime serviceDateTime;

    @NotNull(message = "Patient location is required")
    @JsonProperty("patient_location")
    private PatientLocation patientLocation;

    @NotNull(message = "Required skills list is required")
    @JsonProperty("required_skills")
    private List<String> requiredSkills;

    @NotNull(message = "Required competencies list is required")
    @JsonProperty("required_competencies")
    private List<String> requiredCompetencies;

    @Min(value = 1, message = "Workload points must be at least 1")
    @JsonProperty("workload_points")
    @Builder.Default
    private int workloadPoints = 1;

    @Min(value = 1, message = "Priority level must be at least 1")
    @JsonProperty("priority_level")
    @Builder.Default
    private int priorityLevel = 3;

    @JsonProperty("visit_type")
    private String visitType;

    @JsonProperty("estimated_duration_minutes")
    @Builder.Default
    private int estimatedDurationMinutes = 60;

    @JsonProperty("special_requirements")
    private Map<String, Object> specialRequirements;

    @JsonProperty("preferred_clinician_ids")
    private List<String> preferredClinicianIds;

    @JsonProperty("excluded_clinician_ids")
    private List<String> excludedClinicianIds;

    @JsonProperty("flexibility_options")
    private FlexibilityOptions flexibilityOptions;

    /**
     * Patient location information for geographic matching.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatientLocation {
        
        @NotBlank(message = "Address is required")
        private String address;
        
        @NotNull(message = "Latitude is required")
        private Double latitude;
        
        @NotNull(message = "Longitude is required")
        private Double longitude;
        
        @JsonProperty("zip_code")
        private String zipCode;
        
        @JsonProperty("service_area_code")
        private String serviceAreaCode;
        
        @JsonProperty("geographic_zone")
        private String geographicZone;
    }

    /**
     * Flexibility options for alternative assignment pathways.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlexibilityOptions {
        
        @JsonProperty("allow_skill_substitution")
        @Builder.Default
        private boolean allowSkillSubstitution = false;
        
        @JsonProperty("allow_extended_service_radius")
        @Builder.Default
        private boolean allowExtendedServiceRadius = false;
        
        @JsonProperty("allow_overtime_assignment")
        @Builder.Default
        private boolean allowOvertimeAssignment = false;
        
        @JsonProperty("allow_visit_rescheduling")
        @Builder.Default
        private boolean allowVisitRescheduling = false;
        
        @JsonProperty("max_extended_radius_km")
        @Builder.Default
        private double maxExtendedRadiusKm = 50.0;
        
        @JsonProperty("reschedule_window_hours")
        @Builder.Default
        private int rescheduleWindowHours = 24;
        
        @JsonProperty("acceptable_skill_alternatives")
        private Map<String, List<String>> acceptableSkillAlternatives;
    }

    /**
     * Check if this request allows any flexibility options.
     */
    public boolean hasFlexibilityOptions() {
        if (flexibilityOptions == null) {
            return false;
        }
        
        return flexibilityOptions.isAllowSkillSubstitution() ||
               flexibilityOptions.isAllowExtendedServiceRadius() ||
               flexibilityOptions.isAllowOvertimeAssignment() ||
               flexibilityOptions.isAllowVisitRescheduling();
    }

    /**
     * Get the maximum acceptable service radius.
     */
    public double getMaxServiceRadius() {
        if (flexibilityOptions != null && flexibilityOptions.isAllowExtendedServiceRadius()) {
            return flexibilityOptions.getMaxExtendedRadiusKm();
        }
        return 25.0; // Default service radius
    }

    /**
     * Check if a specific skill can be substituted.
     */
    public boolean canSubstituteSkill(String originalSkill, String alternativeSkill) {
        if (flexibilityOptions == null || !flexibilityOptions.isAllowSkillSubstitution()) {
            return false;
        }
        
        Map<String, List<String>> alternatives = flexibilityOptions.getAcceptableSkillAlternatives();
        if (alternatives == null) {
            return false;
        }
        
        List<String> acceptableAlternatives = alternatives.get(originalSkill);
        return acceptableAlternatives != null && acceptableAlternatives.contains(alternativeSkill);
    }

    /**
     * Get all attributes as a map for dynamic rule evaluation.
     */
    public Map<String, Object> toAttributeMap() {
        return Map.of(
            "patient_id", patientId,
            "visit_id", visitId,
            "service_date_time", serviceDateTime,
            "patient_location", patientLocation,
            "required_skills", requiredSkills,
            "required_competencies", requiredCompetencies,
            "workload_points", workloadPoints,
            "priority_level", priorityLevel,
            "visit_type", visitType != null ? visitType : "",
            "estimated_duration_minutes", estimatedDurationMinutes,
            "special_requirements", specialRequirements != null ? specialRequirements : Map.of(),
            "preferred_clinician_ids", preferredClinicianIds != null ? preferredClinicianIds : List.of(),
            "excluded_clinician_ids", excludedClinicianIds != null ? excludedClinicianIds : List.of(),
            "flexibility_options", flexibilityOptions
        );
    }
}
