package com.caxl.carestaff.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * JPA entity for match override storage.
 * Maps to MATCH_OVERRIDE table.
 */
@Entity
@Table(name = "match_override")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchOverrideEntity {

    @Id
    @Column(name = "override_id")
    private UUID overrideId;

    @Column(name = "service_request_id", nullable = false)
    private UUID serviceRequestId;

    @Column(name = "selected_carestaff_id", nullable = false)
    private UUID selectedCareStaffId;

    @Column(name = "scheduler_id")
    private UUID schedulerId;

    @Column(name = "reason")
    private String reason;

    @Column(name = "explanation", columnDefinition = "TEXT")
    private String explanation;

    @Column(name = "override_datetime")
    private LocalDateTime overrideDateTime;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        if (overrideId == null) {
            overrideId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        if (overrideDateTime == null) {
            overrideDateTime = LocalDateTime.now();
        }
    }
}
