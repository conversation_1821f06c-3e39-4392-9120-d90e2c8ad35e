package com.caxl.assignment.infrastructure.optaplanner.moves;

import ai.timefold.solver.core.api.score.director.ScoreDirector;
import ai.timefold.solver.core.impl.heuristic.selector.common.decorator.SelectionFilter;
import ai.timefold.solver.core.impl.heuristic.move.Move;
import ai.timefold.solver.core.impl.heuristic.selector.move.generic.ChangeMove;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.EnhancedAssignmentSolution;
import com.caxl.assignment.infrastructure.optaplanner.domain.enhanced.Shift;

import java.util.Set;

/**
 * Custom move filter that only allows skill-compatible clinician assignments.
 * This improves solver efficiency by filtering out infeasible moves early.
 */
public class SkillCompatibleMoveFilter implements SelectionFilter<EnhancedAssignmentSolution, Move> {

    @Override
    public boolean accept(ScoreDirector<EnhancedAssignmentSolution> scoreDirector, Move move) {
        if (!(move instanceof ChangeMove)) {
            return true; // Allow non-change moves
        }

        ChangeMove changeMove = (ChangeMove) move;
        
        // Check if this is a clinician assignment change
        if (!(changeMove.getEntity() instanceof Shift)) {
            return true;
        }

        Shift shift = (Shift) changeMove.getEntity();
        Object toPlanningValue = changeMove.getToPlanningValue();
        
        if (!(toPlanningValue instanceof Clinician)) {
            return true; // Allow null assignments or other types
        }

        Clinician clinician = (Clinician) toPlanningValue;
        
        // Check if clinician has required skills for all patients in shift
        return hasRequiredSkillsForAllPatients(shift, clinician);
    }

    /**
     * Check if clinician has all required skills for patients in the shift.
     */
    private boolean hasRequiredSkillsForAllPatients(Shift shift, Clinician clinician) {
        if (shift.getPatientVisits().isEmpty()) {
            return true;
        }

        Set<String> clinicianSkills = Set.copyOf(clinician.getSkills());
        
        return shift.getPatientVisits().stream()
                .allMatch(patient -> {
                    Set<String> requiredSkills = Set.copyOf(patient.getRequiredSkills());
                    return clinicianSkills.containsAll(requiredSkills);
                });
    }
}
