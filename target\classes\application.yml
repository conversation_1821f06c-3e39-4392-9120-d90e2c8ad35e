server:
  port: 8080
  servlet:
    context-path: /
  compression:
    enabled: true

spring:
  application:
    name: caxl-assignment-service
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  mvc:
    throw-exception-if-no-handler-found: true

logging:
  level:
    root: INFO
    com.caxl.assignment: DEBUG
    ai.timefold.solver: INFO

timefold:
  solver:
    termination:
      spent-limit: 30s
      unimproved-spent-limit: 10s
      best-score-limit: 0hard/*soft
    move-thread-count: AUTO
    environment-mode: PRODUCTION

# Application specific configuration
assignment:
  config:
    rules-file: config/rules.json
    skill-hierarchies-file: config/skill_hierarchies.json
    base-score: 100.0
    max-assignments-per-batch: 100
    relaxation:
      strategies:
        - SKILL_SUBSTITUTION
        - VISIT_RESCHEDULING
        - EXTENDED_SERVICE_RADIUS
        - OVERTIME_CAPACITY
      max-date-shift-days: 3
      extended-radius-percent: 20
      overtime-capacity-percent: 20
      max-relaxations-per-patient: 3

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
