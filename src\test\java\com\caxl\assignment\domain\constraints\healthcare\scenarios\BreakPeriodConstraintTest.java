package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;

/**
 * Comprehensive test suite for break period constraints in homecare scheduling.
 * Tests mandatory break requirements for care staff during their working shifts.
 */
@DisplayName("Break Period Constraint Tests")
class BreakPeriodConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Break Period Validation")
    class BasicBreakPeriodValidation {

        @Test
        @DisplayName("testBreakPeriodConstraint_whenAdequateBreakProvided_shouldPass")
        void testBreakPeriodConstraint_whenAdequateBreakProvided_shouldPass() {
            // Given: Care staff working a long shift
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Two visits with adequate break between them (2 hours)
            HomecareVisit morningVisit = testDataFactory.createVisitWithTimeWindow("morningVisit", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            morningVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit afternoonVisit = testDataFactory.createVisitWithTimeWindow("afternoonVisit", 
                    LocalDateTime.of(2024, 1, 15, 13, 0), Duration.ofHours(2));
            afternoonVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (adequate break provided)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, morningVisit, afternoonVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenInsufficientBreak_shouldFail")
        void testBreakPeriodConstraint_whenInsufficientBreak_shouldFail() {
            // Given: Care staff working consecutive visits
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Two visits with insufficient break (30 minutes)
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 30), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (insufficient break period)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, visit1, visit2)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenBackToBackVisits_shouldFail")
        void testBreakPeriodConstraint_whenBackToBackVisits_shouldFail() {
            // Given: Care staff with back-to-back assignments
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Consecutive visits with no break
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (no break between visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, visit1, visit2)
                    .penalizesBy(1);
        }
    }

    @Nested
    @DisplayName("Multiple Visit Break Scenarios")
    class MultipleVisitBreakScenarios {

        @Test
        @DisplayName("testBreakPeriodConstraint_whenMultipleVisitsWithProperBreaks_shouldPass")
        void testBreakPeriodConstraint_whenMultipleVisitsWithProperBreaks_shouldPass() {
            // Given: Care staff working multiple visits
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Four visits throughout the day with proper breaks
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 8, 0), Duration.ofHours(1));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit3 = testDataFactory.createVisitWithTimeWindow("visit3", 
                    LocalDateTime.of(2024, 1, 15, 13, 0), Duration.ofHours(1));
            visit3.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit4 = testDataFactory.createVisitWithTimeWindow("visit4", 
                    LocalDateTime.of(2024, 1, 15, 15, 30), Duration.ofHours(1));
            visit4.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (adequate breaks between all visits)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, visit1, visit2, visit3, visit4)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenSomeBreaksInsufficient_shouldPenalizeViolations")
        void testBreakPeriodConstraint_whenSomeBreaksInsufficient_shouldPenalizeViolations() {
            // Given: Care staff working multiple visits
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Mixed break periods - some adequate, some insufficient
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 8, 0), Duration.ofHours(1));
            visit1.setAssignedCareStaffId("staff1");
            
            // Insufficient break (30 minutes)
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 9, 30), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("staff1");
            
            // Adequate break (2 hours)
            HomecareVisit visit3 = testDataFactory.createVisitWithTimeWindow("visit3", 
                    LocalDateTime.of(2024, 1, 15, 12, 30), Duration.ofHours(1));
            visit3.setAssignedCareStaffId("staff1");
            
            // Insufficient break (15 minutes)
            HomecareVisit visit4 = testDataFactory.createVisitWithTimeWindow("visit4", 
                    LocalDateTime.of(2024, 1, 15, 13, 45), Duration.ofHours(1));
            visit4.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for insufficient breaks (2 violations)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, visit1, visit2, visit3, visit4)
                    .penalizesBy(2);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenLongWorkingDay_shouldRequireExtendedBreaks")
        void testBreakPeriodConstraint_whenLongWorkingDay_shouldRequireExtendedBreaks() {
            // Given: Care staff working a very long day (12+ hours)
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Extended schedule with lunch break but insufficient rest periods
            HomecareVisit earlyMorning = testDataFactory.createVisitWithTimeWindow("earlyMorning", 
                    LocalDateTime.of(2024, 1, 15, 6, 0), Duration.ofHours(3));
            earlyMorning.setAssignedCareStaffId("staff1");
            
            // Short break before lunch
            HomecareVisit preLunch = testDataFactory.createVisitWithTimeWindow("preLunch", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            preLunch.setAssignedCareStaffId("staff1");
            
            // Long lunch break (1 hour) - might be adequate
            HomecareVisit postLunch = testDataFactory.createVisitWithTimeWindow("postLunch", 
                    LocalDateTime.of(2024, 1, 15, 13, 0), Duration.ofHours(3));
            postLunch.setAssignedCareStaffId("staff1");
            
            // Insufficient evening break
            HomecareVisit evening = testDataFactory.createVisitWithTimeWindow("evening", 
                    LocalDateTime.of(2024, 1, 15, 16, 30), Duration.ofHours(3));
            evening.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for insufficient breaks during extended workday
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, earlyMorning, preLunch, postLunch, evening)
                    .penalizesBy(2); // Two insufficient break periods
        }
    }

    @Nested
    @DisplayName("Break Period Edge Cases")
    class BreakPeriodEdgeCases {

        @Test
        @DisplayName("testBreakPeriodConstraint_whenSingleVisit_shouldNotRequireBreak")
        void testBreakPeriodConstraint_whenSingleVisit_shouldNotRequireBreak() {
            // Given: Care staff with only one visit
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Single visit assignment
            HomecareVisit singleVisit = testDataFactory.createVisitWithTimeWindow("singleVisit", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            singleVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (no break required for single visit)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, singleVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenVisitsOnDifferentDays_shouldNotApply")
        void testBreakPeriodConstraint_whenVisitsOnDifferentDays_shouldNotApply() {
            // Given: Care staff working on consecutive days
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Visits on different days
            HomecareVisit todayVisit = testDataFactory.createVisitWithTimeWindow("todayVisit", 
                    LocalDateTime.of(2024, 1, 15, 16, 0), Duration.ofHours(3));
            todayVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit tomorrowVisit = testDataFactory.createVisitWithTimeWindow("tomorrowVisit", 
                    LocalDateTime.of(2024, 1, 16, 8, 0), Duration.ofHours(3));
            tomorrowVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (break requirements don't span across days)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, todayVisit, tomorrowVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenOverlappingVisits_shouldHandleGracefully")
        void testBreakPeriodConstraint_whenOverlappingVisits_shouldHandleGracefully() {
            // Given: Care staff with problematic schedule
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Overlapping visits (should be caught by other constraints)
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should handle overlapping visits appropriately
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, visit1, visit2)
                    .penalizesBy(1); // Penalty for overlapping/insufficient break
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenZeroDurationVisits_shouldHandleCorrectly")
        void testBreakPeriodConstraint_whenZeroDurationVisits_shouldHandleCorrectly() {
            // Given: Care staff with instant consultations
            CareStaff staff = testDataFactory.createCareStaffWithFullAvailability("staff1");
            
            // And: Zero-duration visits (phone consultations)
            HomecareVisit consultation1 = testDataFactory.createVisitWithTimeWindow("consultation1", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ZERO);
            consultation1.setAssignedCareStaffId("staff1");
            
            HomecareVisit consultation2 = testDataFactory.createVisitWithTimeWindow("consultation2", 
                    LocalDateTime.of(2024, 1, 15, 10, 30), Duration.ZERO);
            consultation2.setAssignedCareStaffId("staff1");
            
            HomecareVisit consultation3 = testDataFactory.createVisitWithTimeWindow("consultation3", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ZERO);
            consultation3.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should handle zero-duration visits appropriately
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff, consultation1, consultation2, consultation3)
                    .penalizesBy(0); // May not require breaks for instant consultations
        }
    }

    @Nested
    @DisplayName("Multiple Staff Break Management")
    class MultipleStaffBreakManagement {

        @Test
        @DisplayName("testBreakPeriodConstraint_whenMultipleStaffDifferentSchedules_shouldEvaluateIndependently")
        void testBreakPeriodConstraint_whenMultipleStaffDifferentSchedules_shouldEvaluateIndependently() {
            // Given: Multiple staff with different schedules
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            
            // And: Staff1 with adequate breaks
            HomecareVisit staff1Visit1 = testDataFactory.createVisitWithTimeWindow("staff1Visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            staff1Visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit staff1Visit2 = testDataFactory.createVisitWithTimeWindow("staff1Visit2", 
                    LocalDateTime.of(2024, 1, 15, 13, 0), Duration.ofHours(2));
            staff1Visit2.setAssignedCareStaffId("staff1");
            
            // And: Staff2 with insufficient breaks
            HomecareVisit staff2Visit1 = testDataFactory.createVisitWithTimeWindow("staff2Visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            staff2Visit1.setAssignedCareStaffId("staff2");
            
            HomecareVisit staff2Visit2 = testDataFactory.createVisitWithTimeWindow("staff2Visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 30), Duration.ofHours(2));
            staff2Visit2.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Should penalize only staff2's insufficient break
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(staff1, staff2, staff1Visit1, staff1Visit2, staff2Visit1, staff2Visit2)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testBreakPeriodConstraint_whenStaffWorkingStaggeredShifts_shouldAllowFlexibility")
        void testBreakPeriodConstraint_whenStaffWorkingStaggeredShifts_shouldAllowFlexibility() {
            // Given: Staff working staggered shifts for coverage
            CareStaff morningStaff = testDataFactory.createCareStaffWithFullAvailability("morningStaff");
            CareStaff afternoonStaff = testDataFactory.createCareStaffWithFullAvailability("afternoonStaff");
            CareStaff eveningStaff = testDataFactory.createCareStaffWithFullAvailability("eveningStaff");
            
            // And: Each staff working their respective shifts with proper breaks
            HomecareVisit morningVisit1 = testDataFactory.createVisitWithTimeWindow("morningVisit1", 
                    LocalDateTime.of(2024, 1, 15, 6, 0), Duration.ofHours(2));
            morningVisit1.setAssignedCareStaffId("morningStaff");
            
            HomecareVisit morningVisit2 = testDataFactory.createVisitWithTimeWindow("morningVisit2", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            morningVisit2.setAssignedCareStaffId("morningStaff");
            
            HomecareVisit afternoonVisit1 = testDataFactory.createVisitWithTimeWindow("afternoonVisit1", 
                    LocalDateTime.of(2024, 1, 15, 12, 0), Duration.ofHours(2));
            afternoonVisit1.setAssignedCareStaffId("afternoonStaff");
            
            HomecareVisit afternoonVisit2 = testDataFactory.createVisitWithTimeWindow("afternoonVisit2", 
                    LocalDateTime.of(2024, 1, 15, 15, 0), Duration.ofHours(2));
            afternoonVisit2.setAssignedCareStaffId("afternoonStaff");
            
            HomecareVisit eveningVisit = testDataFactory.createVisitWithTimeWindow("eveningVisit", 
                    LocalDateTime.of(2024, 1, 15, 18, 0), Duration.ofHours(3));
            eveningVisit.setAssignedCareStaffId("eveningStaff");
            
            // When: Verifying the constraint
            // Then: Should pass for all staff (adequate breaks within shifts)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::mandatoryBreakPeriods)
                    .given(morningStaff, afternoonStaff, eveningStaff, 
                           morningVisit1, morningVisit2, afternoonVisit1, afternoonVisit2, eveningVisit)
                    .penalizesBy(0);
        }
    }
}
