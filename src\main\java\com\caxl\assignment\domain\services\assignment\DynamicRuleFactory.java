package com.caxl.assignment.domain.services.assignment;

import com.caxl.assignment.domain.models.assignment.DynamicRule;
import com.caxl.assignment.domain.models.assignment.DynamicRule.RuleOperator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiFunction;

/**
 * Factory service for creating dynamic rule objects from JSON configuration.
 * Automatically generates evaluation functions based on rule operators and field types.
 */
@Service
@Slf4j
public class DynamicRuleFactory {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    /**
     * Create a dynamic rule with auto-generated evaluation function.
     * 
     * @param ruleConfig Rule configuration from JSON
     * @return Configured DynamicRule with evaluation function
     */
    public DynamicRule createRule(DynamicRule ruleConfig) {
        log.debug("Creating dynamic rule: {}", ruleConfig.getRuleId());
        
        // Create evaluation function based on operator
        BiFunction<Map<String, Object>, Map<String, Object>, Boolean> evaluationFunction = 
            createEvaluationFunction(ruleConfig);
        
        // Set the evaluation function
        ruleConfig.setEvaluationFunction(evaluationFunction);
        
        log.debug("Successfully created rule: {} with operator: {}", 
                 ruleConfig.getRuleId(), ruleConfig.getOperator());
        
        return ruleConfig;
    }

    /**
     * Create evaluation function based on rule configuration.
     */
    private BiFunction<Map<String, Object>, Map<String, Object>, Boolean> createEvaluationFunction(
            DynamicRule rule) {
        
        return (patientData, clinicianData) -> {
            try {
                // Determine which entity to evaluate
                Map<String, Object> targetData = "clinician".equals(rule.getTargetEntity()) 
                    ? clinicianData : patientData;
                
                // Extract value from target entity
                Object fieldValue = extractValue(targetData, rule.getField());
                Object ruleValue = rule.getValue();
                
                // Apply operator-specific evaluation
                return evaluateWithOperator(fieldValue, ruleValue, rule.getOperator(), 
                                          patientData, clinicianData);
                
            } catch (Exception e) {
                log.warn("Error evaluating rule {}: {}", rule.getRuleId(), e.getMessage());
                return false;
            }
        };
    }

    /**
     * Extract value from data map using dot notation for nested fields.
     */
    @SuppressWarnings("unchecked")
    private Object extractValue(Map<String, Object> data, String fieldPath) {
        if (data == null || fieldPath == null) {
            return null;
        }
        
        String[] pathParts = fieldPath.split("\\.");
        Object current = data;
        
        for (String part : pathParts) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current;
    }

    /**
     * Evaluate field value against rule value using specified operator.
     */
    @SuppressWarnings("unchecked")
    private boolean evaluateWithOperator(Object fieldValue, Object ruleValue, RuleOperator operator,
                                       Map<String, Object> patientData, Map<String, Object> clinicianData) {
        
        return switch (operator) {
            case CONTAINS_ALL -> evaluateContainsAll(fieldValue, ruleValue);
            case CONTAINS_ANY -> evaluateContainsAny(fieldValue, ruleValue);
            case IN -> evaluateIn(fieldValue, ruleValue);
            case NOT_IN -> !evaluateIn(fieldValue, ruleValue);
            case EQUALS -> evaluateEquals(fieldValue, ruleValue);
            case NOT_EQUALS -> !evaluateEquals(fieldValue, ruleValue);
            case GREATER_THAN -> evaluateGreaterThan(fieldValue, ruleValue);
            case GREATER_THAN_OR_EQUAL -> evaluateGreaterThanOrEqual(fieldValue, ruleValue);
            case LESS_THAN -> evaluateLessThan(fieldValue, ruleValue);
            case LESS_THAN_OR_EQUAL -> evaluateLessThanOrEqual(fieldValue, ruleValue);
            case WITHIN_DISTANCE -> evaluateWithinDistance(fieldValue, ruleValue, patientData, clinicianData);
            case OVERLAPS_TIME -> evaluateOverlapsTime(fieldValue, ruleValue);
            case IS_AVAILABLE_AT -> evaluateIsAvailableAt(fieldValue, ruleValue, patientData);
            case HAS_CAPACITY_FOR -> evaluateHasCapacityFor(fieldValue, ruleValue, patientData);
        };
    }

    private boolean evaluateContainsAll(Object fieldValue, Object ruleValue) {
        if (!(fieldValue instanceof Collection) || !(ruleValue instanceof Collection)) {
            return false;
        }
        Collection<?> fieldCollection = (Collection<?>) fieldValue;
        Collection<?> ruleCollection = (Collection<?>) ruleValue;
        return fieldCollection.containsAll(ruleCollection);
    }

    private boolean evaluateContainsAny(Object fieldValue, Object ruleValue) {
        if (!(fieldValue instanceof Collection) || !(ruleValue instanceof Collection)) {
            return false;
        }
        Collection<?> fieldCollection = (Collection<?>) fieldValue;
        Collection<?> ruleCollection = (Collection<?>) ruleValue;
        return fieldCollection.stream().anyMatch(ruleCollection::contains);
    }

    private boolean evaluateIn(Object fieldValue, Object ruleValue) {
        if (!(ruleValue instanceof Collection)) {
            return Objects.equals(fieldValue, ruleValue);
        }
        Collection<?> ruleCollection = (Collection<?>) ruleValue;
        return ruleCollection.contains(fieldValue);
    }

    private boolean evaluateEquals(Object fieldValue, Object ruleValue) {
        return Objects.equals(fieldValue, ruleValue);
    }

    private boolean evaluateGreaterThan(Object fieldValue, Object ruleValue) {
        return compareNumbers(fieldValue, ruleValue) > 0;
    }

    private boolean evaluateGreaterThanOrEqual(Object fieldValue, Object ruleValue) {
        return compareNumbers(fieldValue, ruleValue) >= 0;
    }

    private boolean evaluateLessThan(Object fieldValue, Object ruleValue) {
        return compareNumbers(fieldValue, ruleValue) < 0;
    }

    private boolean evaluateLessThanOrEqual(Object fieldValue, Object ruleValue) {
        return compareNumbers(fieldValue, ruleValue) <= 0;
    }

    private boolean evaluateWithinDistance(Object fieldValue, Object ruleValue, 
                                         Map<String, Object> patientData, Map<String, Object> clinicianData) {
        try {
            // Extract coordinates from patient and clinician data
            Double patientLat = (Double) extractValue(patientData, "patient_location.latitude");
            Double patientLon = (Double) extractValue(patientData, "patient_location.longitude");
            Double clinicianLat = (Double) extractValue(clinicianData, "location.latitude");
            Double clinicianLon = (Double) extractValue(clinicianData, "location.longitude");
            
            if (patientLat == null || patientLon == null || clinicianLat == null || clinicianLon == null) {
                return false;
            }
            
            double distance = calculateDistance(patientLat, patientLon, clinicianLat, clinicianLon);
            double maxDistance = ((Number) ruleValue).doubleValue();
            
            return distance <= maxDistance;
        } catch (Exception e) {
            log.warn("Error evaluating distance: {}", e.getMessage());
            return false;
        }
    }

    private boolean evaluateOverlapsTime(Object fieldValue, Object ruleValue) {
        try {
            // Implement time overlap logic for scheduling
            if (fieldValue instanceof LocalDateTime && ruleValue instanceof Map) {
                LocalDateTime requestTime = (LocalDateTime) fieldValue;
                Map<String, Object> timeRange = (Map<String, Object>) ruleValue;
                
                LocalDateTime startTime = LocalDateTime.parse((String) timeRange.get("start"), DATE_TIME_FORMATTER);
                LocalDateTime endTime = LocalDateTime.parse((String) timeRange.get("end"), DATE_TIME_FORMATTER);
                
                return !requestTime.isBefore(startTime) && !requestTime.isAfter(endTime);
            }
            return false;
        } catch (Exception e) {
            log.warn("Error evaluating time overlap: {}", e.getMessage());
            return false;
        }
    }

    private boolean evaluateIsAvailableAt(Object fieldValue, Object ruleValue, Map<String, Object> patientData) {
        try {
            // Check if clinician is available at requested time
            LocalDateTime requestedTime = (LocalDateTime) extractValue(patientData, "service_date_time");
            if (requestedTime == null) {
                return false;
            }
            
            // fieldValue should be clinician's availability slots
            if (fieldValue instanceof Collection) {
                Collection<?> availabilitySlots = (Collection<?>) fieldValue;
                return availabilitySlots.stream().anyMatch(slot -> 
                    isTimeWithinSlot(requestedTime, slot));
            }
            
            return false;
        } catch (Exception e) {
            log.warn("Error evaluating availability: {}", e.getMessage());
            return false;
        }
    }

    private boolean evaluateHasCapacityFor(Object fieldValue, Object ruleValue, Map<String, Object> patientData) {
        try {
            // Check if clinician has capacity for the workload
            Integer currentWorkload = (Integer) fieldValue;
            Integer requestedWorkload = (Integer) extractValue(patientData, "workload_points");
            Integer maxCapacity = (Integer) ruleValue;
            
            if (currentWorkload == null || requestedWorkload == null || maxCapacity == null) {
                return false;
            }
            
            return (currentWorkload + requestedWorkload) <= maxCapacity;
        } catch (Exception e) {
            log.warn("Error evaluating capacity: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Compare two numeric values.
     */
    private int compareNumbers(Object value1, Object value2) {
        if (!(value1 instanceof Number) || !(value2 instanceof Number)) {
            throw new IllegalArgumentException("Cannot compare non-numeric values");
        }
        
        double num1 = ((Number) value1).doubleValue();
        double num2 = ((Number) value2).doubleValue();
        
        return Double.compare(num1, num2);
    }

    /**
     * Calculate distance between two geographic points using Haversine formula.
     */
    private double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
        final int EARTH_RADIUS = 6371; // Radius in kilometers
        
        double latDistance = Math.toRadians(lat2 - lat1);
        double lonDistance = Math.toRadians(lon2 - lon1);
        
        double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                + Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2))
                * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return EARTH_RADIUS * c;
    }

    /**
     * Check if a time falls within an availability slot.
     */
    @SuppressWarnings("unchecked")
    private boolean isTimeWithinSlot(LocalDateTime requestedTime, Object slot) {
        try {
            if (slot instanceof Map) {
                Map<String, Object> slotMap = (Map<String, Object>) slot;
                LocalDateTime slotStart = LocalDateTime.parse((String) slotMap.get("start_time"), DATE_TIME_FORMATTER);
                LocalDateTime slotEnd = LocalDateTime.parse((String) slotMap.get("end_time"), DATE_TIME_FORMATTER);
                
                return !requestedTime.isBefore(slotStart) && !requestedTime.isAfter(slotEnd);
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
}
