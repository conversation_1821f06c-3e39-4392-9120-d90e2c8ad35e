package com.caxl.assignment.test.common;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Common test data factory utilities to eliminate duplication in test data creation.
 * Provides reusable builders and factory methods for common test scenarios.
 */
public class TestDataFactory {
    
    private static final AtomicInteger idCounter = new AtomicInteger(1);
    
    // Default test constants
    public static final String DEFAULT_STATE = "CA";
    public static final LocalDateTime DEFAULT_START_TIME = LocalDateTime.of(2024, 1, 15, 9, 0);
    public static final Duration DEFAULT_VISIT_DURATION = Duration.ofHours(1);
    
    /**
     * Base builder for test entities with common patterns.
     */
    public static abstract class BaseTestBuilder<T, B extends BaseTestBuilder<T, B>> {
        protected String id;
        protected final Map<String, Object> attributes = new HashMap<>();
        
        protected abstract B self();
        
        public B withId(String id) {
            this.id = id;
            return self();
        }
        
        public B withAttribute(String key, Object value) {
            this.attributes.put(key, value);
            return self();
        }
        
        public B withRandomId() {
            this.id = generateId();
            return self();
        }
        
        protected String getIdOrGenerate() {
            return this.id != null ? this.id : generateId();
        }
        
        public abstract T build();
    }
    
    /**
     * Generate a unique ID for test entities.
     */
    public static String generateId() {
        return "test-" + idCounter.getAndIncrement();
    }
    
    /**
     * Generate a unique ID with prefix.
     */
    public static String generateId(String prefix) {
        return prefix + "-" + idCounter.getAndIncrement();
    }
    
    /**
     * Create a map with default test attributes.
     */
    public static Map<String, Object> defaultAttributes() {
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("created_by", "test");
        attributes.put("created_at", LocalDateTime.now());
        attributes.put("test_scenario", "default");
        return attributes;
    }
    
    /**
     * Create a list of skills for testing.
     */
    public static Set<String> createSkillSet(String... skills) {
        return new HashSet<>(Arrays.asList(skills));
    }
    
    /**
     * Create a list of skills with nursing as default.
     */
    public static Set<String> createDefaultSkillSet() {
        return createSkillSet("nursing", "basic-care");
    }
    
    /**
     * Create specialized skill sets for different scenarios.
     */
    public static Set<String> createSpecializedSkillSet() {
        return createSkillSet("nursing", "wound-care", "iv-therapy", "medication-administration");
    }
    
    /**
     * Create a list of required skills for testing.
     */
    public static List<String> createRequiredSkills(String... skills) {
        return Arrays.asList(skills);
    }
    
    /**
     * Create default required skills list.
     */
    public static List<String> createDefaultRequiredSkills() {
        return createRequiredSkills("nursing");
    }
    
    /**
     * Create time-based test data.
     */
    public static LocalDateTime createTimeSlot(int hour, int minute) {
        return DEFAULT_START_TIME.withHour(hour).withMinute(minute);
    }
    
    /**
     * Create a time window for testing.
     */
    public static TimeWindow createTimeWindow(LocalDateTime start, Duration duration) {
        return new TimeWindow(start, start.plus(duration));
    }
    
    /**
     * Create default time window.
     */
    public static TimeWindow createDefaultTimeWindow() {
        return createTimeWindow(DEFAULT_START_TIME, DEFAULT_VISIT_DURATION);
    }
    
    /**
     * Simple time window class for testing.
     */
    public static class TimeWindow {
        public final LocalDateTime start;
        public final LocalDateTime end;
        
        public TimeWindow(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
        
        public Duration getDuration() {
            return Duration.between(start, end);
        }
    }
    
    /**
     * Create compliance requirements for testing.
     */
    public static Map<String, Object> createComplianceRequirements(String state) {
        Map<String, Object> requirements = new HashMap<>();
        requirements.put("state", state != null ? state : DEFAULT_STATE);
        requirements.put("licensing_required", true);
        requirements.put("background_check", true);
        requirements.put("certifications", Arrays.asList("CPR", "First Aid"));
        return requirements;
    }
    
    /**
     * Create default compliance requirements.
     */
    public static Map<String, Object> createDefaultComplianceRequirements() {
        return createComplianceRequirements(DEFAULT_STATE);
    }
    
    /**
     * Create geographic location for testing.
     */
    public static Map<String, Object> createLocation(double latitude, double longitude, String address) {
        Map<String, Object> location = new HashMap<>();
        location.put("latitude", latitude);
        location.put("longitude", longitude);
        location.put("address", address != null ? address : "123 Test St, Test City, " + DEFAULT_STATE);
        location.put("zip_code", "12345");
        return location;
    }
    
    /**
     * Create default test location.
     */
    public static Map<String, Object> createDefaultLocation() {
        return createLocation(34.0522, -118.2437, null); // Los Angeles coordinates
    }
    
    /**
     * Create service type configuration for testing.
     */
    public static Map<String, Object> createServiceType(String primaryService, List<String> requiredSkills) {
        Map<String, Object> serviceType = new HashMap<>();
        serviceType.put("primary_service", primaryService != null ? primaryService : "Skilled Nursing");
        serviceType.put("required_skills", requiredSkills != null ? requiredSkills : createDefaultRequiredSkills());
        serviceType.put("estimated_duration", DEFAULT_VISIT_DURATION.toMinutes());
        serviceType.put("equipment_needed", Arrays.asList("stethoscope", "blood_pressure_cuff"));
        return serviceType;
    }
    
    /**
     * Create default service type.
     */
    public static Map<String, Object> createDefaultServiceType() {
        return createServiceType(null, null);
    }
    
    /**
     * Create working hours for testing.
     */
    public static Map<String, Object> createWorkingHours(int startHour, int endHour) {
        Map<String, Object> workingHours = new HashMap<>();
        workingHours.put("start_time", String.format("%02d:00", startHour));
        workingHours.put("end_time", String.format("%02d:00", endHour));
        workingHours.put("days_of_week", Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        workingHours.put("timezone", "America/Los_Angeles");
        return workingHours;
    }
    
    /**
     * Create default working hours (9 AM to 5 PM).
     */
    public static Map<String, Object> createDefaultWorkingHours() {
        return createWorkingHours(9, 17);
    }
    
    /**
     * Create operating zones for testing.
     */
    public static List<Map<String, Object>> createOperatingZones(String... zipCodes) {
        List<Map<String, Object>> zones = new ArrayList<>();
        for (String zipCode : zipCodes) {
            Map<String, Object> zone = new HashMap<>();
            zone.put("zip_code", zipCode);
            zone.put("radius_miles", 10);
            zone.put("priority", "normal");
            zones.add(zone);
        }
        return zones;
    }
    
    /**
     * Create default operating zones.
     */
    public static List<Map<String, Object>> createDefaultOperatingZones() {
        return createOperatingZones("12345", "12346", "12347");
    }
    
    /**
     * Create a batch of test entities with sequential IDs.
     */
    public static <T> List<T> createBatch(int count, java.util.function.Function<String, T> factory) {
        List<T> batch = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            batch.add(factory.apply(generateId()));
        }
        return batch;
    }
    
    /**
     * Create a batch with custom ID prefix.
     */
    public static <T> List<T> createBatch(int count, String idPrefix, java.util.function.Function<String, T> factory) {
        List<T> batch = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            batch.add(factory.apply(generateId(idPrefix)));
        }
        return batch;
    }
    
    /**
     * Reset the ID counter for consistent test runs.
     */
    public static void resetIdCounter() {
        idCounter.set(1);
    }
}
