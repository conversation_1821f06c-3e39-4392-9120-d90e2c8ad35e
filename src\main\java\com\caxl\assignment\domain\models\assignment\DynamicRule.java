package com.caxl.assignment.domain.models.assignment;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * Dynamic rule model for multi-level clinician assignment system.
 * Supports auto-creation from JSON configuration with flexible evaluation operators.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicRule {

    @NotBlank(message = "Rule ID is required")
    @JsonProperty("rule_id")
    private String ruleId;

    @NotBlank(message = "Rule name is required")
    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @NotNull(message = "Rule level is required")
    @JsonProperty("level")
    private RuleLevel level;

    @NotNull(message = "Rule type is required")
    @JsonProperty("type")
    private RuleType type;

    @NotNull(message = "Field to evaluate is required")
    @JsonProperty("field")
    private String field;

    @NotNull(message = "Operator is required")
    @JsonProperty("operator")
    private RuleOperator operator;

    @JsonProperty("value")
    private Object value;

    @JsonProperty("target_entity")
    @Builder.Default
    private String targetEntity = "patient"; // "patient" or "clinician"

    @DecimalMin(value = "0.0", message = "Weight cannot be negative")
    @JsonProperty("weight")
    @Builder.Default
    private double weight = 1.0;

    @JsonProperty("enabled")
    @Builder.Default
    private boolean enabled = true;

    @JsonProperty("tags")
    private List<String> tags;

    @JsonProperty("metadata")
    private Map<String, Object> metadata;

    // Transient evaluation function (not serialized)
    private transient BiFunction<Map<String, Object>, Map<String, Object>, Boolean> evaluationFunction;

    /**
     * Rule levels for multi-level filtering.
     */
    public enum RuleLevel {
        @JsonProperty("level_1")
        LEVEL_1, // Mandatory Assignment Criteria (Hard Constraints)
        
        @JsonProperty("level_2")
        LEVEL_2, // Resource Capacity and Load Management
        
        @JsonProperty("level_3")
        LEVEL_3  // Intelligent Alternative Assignment Pathways (Relaxation)
    }

    /**
     * Rule types for constraint classification.
     */
    public enum RuleType {
        @JsonProperty("hard")
        HARD,     // Must be satisfied
        
        @JsonProperty("soft")
        SOFT      // Optimization objectives
    }

    /**
     * Supported operators for rule evaluation.
     */
    public enum RuleOperator {
        @JsonProperty("contains_all")
        CONTAINS_ALL,
        
        @JsonProperty("contains_any")
        CONTAINS_ANY,
        
        @JsonProperty("in")
        IN,
        
        @JsonProperty("not_in")
        NOT_IN,
        
        @JsonProperty("equals")
        EQUALS,
        
        @JsonProperty("not_equals")
        NOT_EQUALS,
        
        @JsonProperty("greater_than")
        GREATER_THAN,
        
        @JsonProperty("greater_than_or_equal")
        GREATER_THAN_OR_EQUAL,
        
        @JsonProperty("less_than")
        LESS_THAN,
        
        @JsonProperty("less_than_or_equal")
        LESS_THAN_OR_EQUAL,
        
        @JsonProperty("within_distance")
        WITHIN_DISTANCE,
        
        @JsonProperty("overlaps_time")
        OVERLAPS_TIME,
        
        @JsonProperty("is_available_at")
        IS_AVAILABLE_AT,
        
        @JsonProperty("has_capacity_for")
        HAS_CAPACITY_FOR
    }

    /**
     * Check if this rule is a hard constraint.
     */
    public boolean isHardConstraint() {
        return type == RuleType.HARD;
    }

    /**
     * Check if this rule is a soft constraint.
     */
    public boolean isSoftConstraint() {
        return type == RuleType.SOFT;
    }

    /**
     * Check if this rule applies to Level 1 filtering.
     */
    public boolean isLevel1Rule() {
        return level == RuleLevel.LEVEL_1;
    }

    /**
     * Check if this rule applies to Level 2 filtering.
     */
    public boolean isLevel2Rule() {
        return level == RuleLevel.LEVEL_2;
    }

    /**
     * Check if this rule applies to Level 3 filtering.
     */
    public boolean isLevel3Rule() {
        return level == RuleLevel.LEVEL_3;
    }

    /**
     * Evaluate this rule against patient and clinician data.
     * 
     * @param patientData Patient attributes as key-value map
     * @param clinicianData Clinician attributes as key-value map
     * @return true if rule is satisfied, false otherwise
     */
    public boolean evaluate(Map<String, Object> patientData, Map<String, Object> clinicianData) {
        if (!enabled || evaluationFunction == null) {
            return true; // Disabled rules are considered satisfied
        }
        
        try {
            return evaluationFunction.apply(patientData, clinicianData);
        } catch (Exception e) {
            // Log error and return false for safety
            return false;
        }
    }

    /**
     * Get the effective weight for this rule based on its type and level.
     */
    public double getEffectiveWeight() {
        if (!enabled) {
            return 0.0;
        }
        
        // Apply level-based weight multipliers
        double levelMultiplier = switch (level) {
            case LEVEL_1 -> 10.0; // Highest priority
            case LEVEL_2 -> 5.0;  // Medium priority
            case LEVEL_3 -> 1.0;  // Lowest priority
        };
        
        return weight * levelMultiplier;
    }

    /**
     * Create a copy of this rule with modified parameters for Level 3 relaxation.
     */
    public DynamicRule createRelaxedVersion() {
        return DynamicRule.builder()
            .ruleId(ruleId + "_relaxed")
            .name(name + " (Relaxed)")
            .description(description + " - Relaxed for Level 3 processing")
            .level(RuleLevel.LEVEL_3)
            .type(RuleType.SOFT) // Convert hard rules to soft for relaxation
            .field(field)
            .operator(operator)
            .value(value)
            .targetEntity(targetEntity)
            .weight(weight * 0.5) // Reduce weight for relaxed rules
            .enabled(enabled)
            .tags(tags)
            .metadata(metadata)
            .build();
    }
}
