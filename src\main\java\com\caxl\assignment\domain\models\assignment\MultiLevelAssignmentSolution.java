package com.caxl.assignment.domain.models.assignment;

import ai.timefold.solver.core.api.domain.solution.PlanningEntityCollectionProperty;
import ai.timefold.solver.core.api.domain.solution.PlanningScore;
import ai.timefold.solver.core.api.domain.solution.PlanningSolution;
import ai.timefold.solver.core.api.domain.solution.ProblemFactCollectionProperty;
import ai.timefold.solver.core.api.domain.valuerange.ValueRangeProvider;
import ai.timefold.solver.core.api.score.buildin.hardsoft.HardSoftScore;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Timefold planning solution for dynamic multi-level clinician assignment.
 * Supports three levels of filtering with dynamic rule evaluation and relaxation strategies.
 */
@PlanningSolution
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MultiLevelAssignmentSolution {

    /**
     * Planning entities: Dynamic assignments to be optimized by Timefold.
     */
    @PlanningEntityCollectionProperty
    private List<DynamicClinicianAssignment> assignments;

    /**
     * Available clinicians (value range for assignment optimization).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "clinicianRange")
    private List<Clinician> clinicians;

    /**
     * Available care staff (extended clinicians with homecare capabilities).
     */
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "careStaffRange")
    private List<CareStaff> careStaff;

    /**
     * Assignment requests to be processed.
     */
    @ProblemFactCollectionProperty
    private List<ClinicianAssignmentRequest> assignmentRequests;

    /**
     * Dynamic rules for multi-level evaluation.
     */
    @ProblemFactCollectionProperty
    private List<DynamicRule> dynamicRules;

    /**
     * Level 1 rules (Mandatory Assignment Criteria - Hard Constraints).
     */
    @ProblemFactCollectionProperty
    private List<DynamicRule> level1Rules;

    /**
     * Level 2 rules (Resource Capacity and Load Management).
     */
    @ProblemFactCollectionProperty
    private List<DynamicRule> level2Rules;

    /**
     * Level 3 rules (Intelligent Alternative Assignment Pathways).
     */
    @ProblemFactCollectionProperty
    private List<DynamicRule> level3Rules;

    /**
     * Skill hierarchy for alternative skill matching.
     */
    @ProblemFactCollectionProperty
    private Map<String, Set<String>> skillHierarchy;

    /**
     * Geographic distance matrix for travel optimization.
     */
    @ProblemFactCollectionProperty
    private Map<String, Map<String, Double>> distanceMatrix;

    /**
     * Current workload tracking for capacity management.
     */
    @ProblemFactCollectionProperty
    private Map<String, Integer> clinicianWorkloads;

    /**
     * Assignment history for continuity of care.
     */
    @ProblemFactCollectionProperty
    private Map<String, List<String>> assignmentHistory;

    /**
     * Planning score calculated by Timefold.
     */
    @PlanningScore
    private HardSoftScore score;

    /**
     * Assignment processing level (1, 2, or 3).
     */
    @Builder.Default
    private int currentProcessingLevel = 1;

    /**
     * Scheduling period for multi-day assignments.
     */
    private List<LocalDate> schedulingPeriod;

    /**
     * Configuration for relaxation strategies.
     */
    private RelaxationConfiguration relaxationConfig;

    /**
     * Assignment statistics for monitoring.
     */
    private AssignmentStatistics statistics;

    /**
     * Configuration for relaxation strategies in Level 3 processing.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelaxationConfiguration {
        
        @Builder.Default
        private boolean enableSkillSubstitution = true;
        
        @Builder.Default
        private boolean enableExtendedRadius = true;
        
        @Builder.Default
        private boolean enableOvertimeAssignment = false;
        
        @Builder.Default
        private boolean enableVisitRescheduling = true;
        
        @Builder.Default
        private double maxExtendedRadiusKm = 50.0;
        
        @Builder.Default
        private int maxOvertimeHours = 2;
        
        @Builder.Default
        private int rescheduleWindowHours = 24;
        
        @Builder.Default
        private double skillSubstitutionPenalty = 0.5;
        
        @Builder.Default
        private double extendedRadiusPenalty = 0.3;
        
        @Builder.Default
        private double overtimePenalty = 0.8;
        
        @Builder.Default
        private double reschedulePenalty = 0.2;
    }

    /**
     * Statistics for assignment processing and optimization.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssignmentStatistics {
        
        @Builder.Default
        private int totalRequests = 0;
        
        @Builder.Default
        private int level1Assignments = 0;
        
        @Builder.Default
        private int level2Assignments = 0;
        
        @Builder.Default
        private int level3Assignments = 0;
        
        @Builder.Default
        private int unassignedRequests = 0;
        
        @Builder.Default
        private double averageAssignmentScore = 0.0;
        
        @Builder.Default
        private double level1SuccessRate = 0.0;
        
        @Builder.Default
        private double level2SuccessRate = 0.0;
        
        @Builder.Default
        private double level3SuccessRate = 0.0;
        
        @Builder.Default
        private long processingTimeMs = 0;
        
        private Map<String, Integer> ruleViolationCounts;
        
        private Map<String, Double> relaxationUsageRates;
    }

    /**
     * Get all assigned requests across all levels.
     */
    public List<ClinicianAssignmentRequest> getAssignedRequests() {
        return assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .map(DynamicClinicianAssignment::getAssignmentRequest)
                .toList();
    }

    /**
     * Get unassigned requests that need Level 3 processing.
     */
    public List<ClinicianAssignmentRequest> getUnassignedRequests() {
        Set<String> assignedRequestIds = assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .map(assignment -> assignment.getAssignmentRequest().getVisitId())
                .collect(java.util.stream.Collectors.toSet());
        
        return assignmentRequests.stream()
                .filter(request -> !assignedRequestIds.contains(request.getVisitId()))
                .toList();
    }

    /**
     * Get assignments by processing level.
     */
    public List<DynamicClinicianAssignment> getAssignmentsByLevel(int level) {
        return assignments.stream()
                .filter(assignment -> assignment.getProcessingLevel() == level)
                .toList();
    }

    /**
     * Calculate assignment success rate for a specific level.
     */
    public double getSuccessRateForLevel(int level) {
        long totalAtLevel = assignments.stream()
                .filter(assignment -> assignment.getProcessingLevel() <= level)
                .count();
        
        long successfulAtLevel = assignments.stream()
                .filter(assignment -> assignment.getProcessingLevel() <= level)
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .count();
        
        return totalAtLevel > 0 ? (double) successfulAtLevel / totalAtLevel : 0.0;
    }

    /**
     * Check if Level 3 relaxation is needed.
     */
    public boolean needsLevel3Processing() {
        return !getUnassignedRequests().isEmpty() && currentProcessingLevel < 3;
    }

    /**
     * Update statistics based on current assignments.
     */
    public void updateStatistics() {
        if (statistics == null) {
            statistics = new AssignmentStatistics();
        }
        
        statistics.setTotalRequests(assignmentRequests.size());
        statistics.setLevel1Assignments((int) getAssignmentsByLevel(1).size());
        statistics.setLevel2Assignments((int) getAssignmentsByLevel(2).size());
        statistics.setLevel3Assignments((int) getAssignmentsByLevel(3).size());
        statistics.setUnassignedRequests(getUnassignedRequests().size());
        
        statistics.setLevel1SuccessRate(getSuccessRateForLevel(1));
        statistics.setLevel2SuccessRate(getSuccessRateForLevel(2));
        statistics.setLevel3SuccessRate(getSuccessRateForLevel(3));
        
        // Calculate average assignment score
        double avgScore = assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .mapToDouble(DynamicClinicianAssignment::getAssignmentScore)
                .average()
                .orElse(0.0);
        statistics.setAverageAssignmentScore(avgScore);
    }

    /**
     * Check if solution is feasible (all hard constraints satisfied).
     */
    public boolean isFeasible() {
        return score != null && score.hardScore() >= 0;
    }

    /**
     * Get the overall assignment completion rate.
     */
    public double getCompletionRate() {
        if (assignmentRequests.isEmpty()) {
            return 1.0;
        }
        
        long assignedCount = assignments.stream()
                .filter(assignment -> assignment.getAssignedClinician() != null)
                .count();
        
        return (double) assignedCount / assignmentRequests.size();
    }
}
