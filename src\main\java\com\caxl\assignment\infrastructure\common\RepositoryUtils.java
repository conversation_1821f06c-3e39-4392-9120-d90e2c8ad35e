package com.caxl.assignment.infrastructure.common;

import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Generic repository utilities to eliminate repetitive stream filtering patterns
 * across repository adapters.
 */
public class RepositoryUtils {
    
    /**
     * Generic filter method that can be reused across different repository implementations.
     */
    public static <T> List<T> filterBy(List<T> items, Predicate<T> predicate) {
        if (items == null) {
            return List.of();
        }
        return items.stream()
                .filter(predicate)
                .collect(Collectors.toList());
    }
    
    /**
     * Find first item matching the predicate.
     */
    public static <T> Optional<T> findFirstBy(List<T> items, Predicate<T> predicate) {
        if (items == null) {
            return Optional.empty();
        }
        return items.stream()
                .filter(predicate)
                .findFirst();
    }
    
    /**
     * Count items matching the predicate.
     */
    public static <T> long countBy(List<T> items, Predicate<T> predicate) {
        if (items == null) {
            return 0;
        }
        return items.stream()
                .filter(predicate)
                .count();
    }
    
    /**
     * Check if any item matches the predicate.
     */
    public static <T> boolean anyMatch(List<T> items, Predicate<T> predicate) {
        if (items == null) {
            return false;
        }
        return items.stream()
                .anyMatch(predicate);
    }
    
    /**
     * Check if all items match the predicate.
     */
    public static <T> boolean allMatch(List<T> items, Predicate<T> predicate) {
        if (items == null) {
            return true;
        }
        return items.stream()
                .allMatch(predicate);
    }
    
    /**
     * Group items by a key function.
     */
    public static <T, K> java.util.Map<K, List<T>> groupBy(List<T> items, 
                                                           java.util.function.Function<T, K> keyMapper) {
        if (items == null) {
            return java.util.Map.of();
        }
        return items.stream()
                .collect(Collectors.groupingBy(keyMapper));
    }
    
    /**
     * Filter and map in one operation.
     */
    public static <T, R> List<R> filterAndMap(List<T> items, 
                                             Predicate<T> predicate,
                                             java.util.function.Function<T, R> mapper) {
        if (items == null) {
            return List.of();
        }
        return items.stream()
                .filter(predicate)
                .map(mapper)
                .collect(Collectors.toList());
    }
    
    /**
     * Check if the collection is null or empty.
     */
    public static boolean isNullOrEmpty(java.util.Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    
    /**
     * Get a safe list (never null).
     */
    public static <T> List<T> safeList(List<T> list) {
        return list != null ? list : List.of();
    }
}
