package com.caxl.carestaff.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Domain entity representing a match suggestion between carestaff and service request.
 * Framework-agnostic domain model for matching results.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatchSuggestion {

    private UUID suggestionId;
    private UUID serviceRequestId;
    private UUID suggestedCareStaffId;
    private double score;
    private String rationale;
    private LocalDateTime suggestedDateTime;
    private LocalDateTime createdAt;
    private boolean isActive;

    /**
     * Create a new match suggestion.
     */
    public static MatchSuggestion create(UUID serviceRequestId, UUID careStaffId, 
                                       double score, String rationale, LocalDateTime suggestedDateTime) {
        return MatchSuggestion.builder()
                .suggestionId(UUID.randomUUID())
                .serviceRequestId(serviceRequestId)
                .suggestedCareStaffId(careStaffId)
                .score(score)
                .rationale(rationale)
                .suggestedDateTime(suggestedDateTime)
                .createdAt(LocalDateTime.now())
                .isActive(true)
                .build();
    }

    /**
     * Deactivate this suggestion.
     */
    public void deactivate() {
        this.isActive = false;
    }
}
