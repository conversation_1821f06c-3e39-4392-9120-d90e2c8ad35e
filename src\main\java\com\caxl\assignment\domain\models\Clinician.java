package com.caxl.assignment.domain.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * Clinician domain model for the assignment system.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Clinician {

    @NotBlank(message = "Clinician ID is required")
    private String id;

    @NotBlank(message = "Clinician name is required")
    private String name;

    @Valid
    @NotNull(message = "Clinician location is required")
    private ClinicianLocation location;

    @NotNull(message = "Skills list is required")
    private List<@NotBlank String> skills;

    @NotNull(message = "Certifications list is required")
    private List<@NotBlank String> certifications;

    @NotNull(message = "Languages list is required")
    private List<@NotBlank String> languages;

    @Valid
    @NotNull(message = "Available slots list is required")
    @JsonProperty("available_slots")
    private List<AvailabilitySlot> availableSlots;

    @Valid
    @NotNull(message = "PII information is required")
    private ClinicianPII pii;

    @Valid
    @NotNull(message = "Profile information is required")
    private ClinicianProfile profile;

    @Builder.Default
    private boolean active = true;

    @Min(value = 1, message = "Max daily appointments must be at least 1")
    @JsonProperty("max_daily_appointments")
    @Builder.Default
    private int maxDailyAppointments = 8;

    @Min(value = 1, message = "Max weekly appointments must be at least 1")
    @JsonProperty("max_weekly_appointments")
    @Builder.Default
    private int maxWeeklyAppointments = 40;

    @Min(value = 1, message = "Max daily workload points must be at least 1")
    @JsonProperty("max_daily_workload_points")
    @Builder.Default
    private int maxDailyWorkloadPoints = 24;

    @Min(value = 1, message = "Max weekly workload points must be at least 1")
    @JsonProperty("max_weekly_workload_points")
    @Builder.Default
    private int maxWeeklyWorkloadPoints = 120;

    @Min(value = 0, message = "Daily appointments count cannot be negative")
    @JsonProperty("daily_appointments_count")
    @Builder.Default
    private int dailyAppointmentsCount = 0;

    @Min(value = 0, message = "Weekly appointments count cannot be negative")
    @JsonProperty("weekly_appointments_count")
    @Builder.Default
    private int weeklyAppointmentsCount = 0;

    @Min(value = 0, message = "Daily workload points cannot be negative")
    @JsonProperty("daily_workload_points")
    @Builder.Default
    private int dailyWorkloadPoints = 0;

    @Min(value = 0, message = "Weekly workload points cannot be negative")
    @JsonProperty("weekly_workload_points")
    @Builder.Default
    private int weeklyWorkloadPoints = 0;

    /**
     * Clinician location and service area information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClinicianLocation {
        
        @NotNull(message = "Service areas list is required")
        @JsonProperty("service_areas")
        private List<@NotBlank String> serviceAreas;

        @JsonProperty("postal_code")
        private String postalCode = "";

        private String city = "";

        private String state = "";

        private Map<String, Double> coordinates; // {"latitude": double, "longitude": double}
    }

    /**
     * Personally Identifiable Information for clinicians.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClinicianPII {
        
        private String gender;

        @JsonProperty("date_of_birth")
        private String dateOfBirth; // ISO format date
    }

    /**
     * Clinician profile information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClinicianProfile {
        
        @Min(value = 0, message = "Experience years cannot be negative")
        @JsonProperty("experience_years")
        @Builder.Default
        private int experienceYears = 0;

        @NotNull(message = "Specialties list is required")
        private List<String> specialties;

        @Min(value = 0, message = "Rating cannot be negative")
        @Max(value = 5, message = "Rating cannot exceed 5.0")
        @Builder.Default
        private double rating = 0.0; // 0-5 scale
    }

    /**
     * Availability time slot for clinicians.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AvailabilitySlot {
        
        @NotNull(message = "Date is required")
        private LocalDate date;

        @NotNull(message = "Start time is required")
        @JsonProperty("start_time")
        private LocalTime startTime;

        @NotNull(message = "End time is required")
        @JsonProperty("end_time")
        private LocalTime endTime;

        /**
         * Check if this availability slot contains another availability slot.
         *
         * @param other Another AvailabilitySlot object
         * @return True if this availability slot contains the other availability slot, False otherwise
         */
        public boolean contains(AvailabilitySlot other) {
            // Check if dates are the same
            if (!this.date.equals(other.date)) {
                return false;
            }

            // Check if times overlap
            return (other.startTime.compareTo(this.startTime) >= 0 &&
                    other.endTime.compareTo(this.endTime) <= 0);
        }
    }
}
