package com.caxl.assignment.domain.common;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Base builder class that provides common builder patterns and methods
 * used across different domain models. This eliminates duplication in
 * builder pattern implementations.
 */
public abstract class BaseBuilder<T, B extends BaseBuilder<T, B>> {
    
    /**
     * Returns the concrete builder instance for method chaining.
     */
    protected abstract B self();
    
    /**
     * Builds the final object.
     */
    public abstract T build();
    
    /**
     * Helper method to set a value if it's not null.
     */
    protected <V> B setIfNotNull(V value, java.util.function.Consumer<V> setter) {
        if (value != null) {
            setter.accept(value);
        }
        return self();
    }
    
    /**
     * Helper method to add to a list if the list is not null and not empty.
     */
    protected <V> B addAllIfNotEmpty(List<V> list, java.util.function.Consumer<List<V>> setter) {
        if (list != null && !list.isEmpty()) {
            setter.accept(list);
        }
        return self();
    }
    
    /**
     * Helper method to set boolean with default value.
     */
    protected B setBooleanWithDefault(Boolean value, boolean defaultValue, java.util.function.Consumer<Boolean> setter) {
        setter.accept(value != null ? value : defaultValue);
        return self();
    }
    
    /**
     * Helper method to validate required fields during build.
     */
    protected void validateRequired(Object value, String fieldName) {
        if (value == null) {
            throw new IllegalArgumentException(fieldName + " is required and cannot be null");
        }
    }
    
    /**
     * Helper method to validate string fields.
     */
    protected void validateRequiredString(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + " is required and cannot be null or empty");
        }
    }
    
    /**
     * Helper method to validate collections.
     */
    protected void validateRequiredCollection(java.util.Collection<?> collection, String fieldName) {
        if (collection == null || collection.isEmpty()) {
            throw new IllegalArgumentException(fieldName + " is required and cannot be null or empty");
        }
    }
}
