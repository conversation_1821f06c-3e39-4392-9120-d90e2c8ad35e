package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import static org.junit.jupiter.api.Assertions.*;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;

/**
 * Comprehensive test suite for time conflict constraints in homecare scheduling.
 * Tests that care staff cannot be assigned to overlapping visits.
 */
@DisplayName("Time Conflict Constraint Tests")
class TimeConflictConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Time Conflict Validation")
    class BasicTimeConflictValidation {

        @Test
        @DisplayName("testTimeConflictConstraint_whenVisitsDoNotOverlap_shouldPass")
        void testTimeConflictConstraint_whenVisitsDoNotOverlap_shouldPass() {
            // Given: Two sequential visits for same staff
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(1));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (no overlap)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenVisitsOverlap_shouldFail")
        void testTimeConflictConstraint_whenVisitsOverlap_shouldFail() {
            // Given: Two overlapping visits for same staff
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for time conflict
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenVisitsForDifferentStaff_shouldPass")
        void testTimeConflictConstraint_whenVisitsForDifferentStaff_shouldPass() {
            // Given: Two overlapping visits for different staff
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(2));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Should pass (different staff, no conflict)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2)
                    .penalizesBy(0);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Boundary Conditions")
    class EdgeCasesAndBoundaryConditions {

        @Test
        @DisplayName("testTimeConflictConstraint_whenVisitsAreAdjacent_shouldPass")
        void testTimeConflictConstraint_whenVisitsAreAdjacent_shouldPass() {
            // Given: Two adjacent visits (9-10 AM and 10-11 AM)
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(1));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (adjacent but not overlapping)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenVisitsHaveMinuteOverlap_shouldFail")
        void testTimeConflictConstraint_whenVisitsHaveMinuteOverlap_shouldFail() {
            // Given: Two visits with 1-minute overlap
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofMinutes(61));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            visit2.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (even minute overlap is conflict)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenOneVisitContainsAnother_shouldFail")
        void testTimeConflictConstraint_whenOneVisitContainsAnother_shouldFail() {
            // Given: One visit completely contains another
            HomecareVisit longVisit = testDataFactory.createVisitWithTimeWindow("longVisit", 
                    LocalDateTime.of(2024, 1, 15, 8, 0), Duration.ofHours(4));
            longVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit shortVisit = testDataFactory.createVisitWithTimeWindow("shortVisit", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            shortVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should fail (contained visit is conflict)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(longVisit, shortVisit)
                    .penalizesBy(1);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenMultipleOverlappingVisits_shouldPenalizeAll")
        void testTimeConflictConstraint_whenMultipleOverlappingVisits_shouldPenalizeAll() {
            // Given: Three overlapping visits for same staff
            HomecareVisit visit1 = testDataFactory.createVisitWithTimeWindow("visit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(3));
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createVisitWithTimeWindow("visit2", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            visit2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit3 = testDataFactory.createVisitWithTimeWindow("visit3", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(1));
            visit3.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should penalize for multiple conflicts (3 pairs: 1-2, 1-3, 2-3)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(visit1, visit2, visit3)
                    .penalizesBy(3);
        }
    }

    @Nested
    @DisplayName("Complex Scheduling Scenarios")
    class ComplexSchedulingScenarios {

        @Test
        @DisplayName("testTimeConflictConstraint_whenStaffHasFullDaySchedule_shouldValidateAllPairs")
        void testTimeConflictConstraint_whenStaffHasFullDaySchedule_shouldValidateAllPairs() {
            // Given: Staff with multiple non-overlapping visits throughout day
            HomecareVisit morningVisit = testDataFactory.createVisitWithTimeWindow("morning", 
                    LocalDateTime.of(2024, 1, 15, 8, 0), Duration.ofHours(1));
            morningVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit midMorningVisit = testDataFactory.createVisitWithTimeWindow("midMorning", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            midMorningVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit lunchVisit = testDataFactory.createVisitWithTimeWindow("lunch", 
                    LocalDateTime.of(2024, 1, 15, 12, 0), Duration.ofHours(1));
            lunchVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit afternoonVisit = testDataFactory.createVisitWithTimeWindow("afternoon", 
                    LocalDateTime.of(2024, 1, 15, 15, 0), Duration.ofHours(1));
            afternoonVisit.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should pass (no overlaps in properly scheduled day)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(morningVisit, midMorningVisit, lunchVisit, afternoonVisit)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testTimeConflictConstraint_whenUnassignedVisitsPresent_shouldIgnoreUnassigned")
        void testTimeConflictConstraint_whenUnassignedVisitsPresent_shouldIgnoreUnassigned() {
            // Given: Assigned and unassigned visits
            HomecareVisit assignedVisit = testDataFactory.createVisitWithTimeWindow("assigned", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(2));
            assignedVisit.setAssignedCareStaffId("staff1");
            
            HomecareVisit unassignedVisit = testDataFactory.createVisitWithTimeWindow("unassigned", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(2));
            unassignedVisit.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should pass (unassigned visits don't create conflicts)
            constraintVerifier.verifyThat((provider, factory) -> provider.noConflictingVisitsForStaff(factory))
                    .given(assignedVisit, unassignedVisit)
                    .penalizesBy(0);
        }
    }
}
