scenario_name: Reinforcement Learning Optimization
test_date: '2025-05-19T23:22:24.443061'
summary:
  total_patients: 3
  total_matches: 6
  validation_success: false
patients:
- patient_id: patient-001
  patient_name: <PERSON>
  required_skills:
  - wound_care
  - medication_management
  required_certifications:
  - RN
  matches:
  - rank: 1
    carestaff_id: carestaff-001
    carestaff_name: <PERSON>
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  - rank: 2
    carestaff_id: carestaff-002
    carestaff_name: <PERSON>
    score:
      total: '0.89'
      base: '0.70'
      bonus: '0.19'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.89
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - preferred-carestaff
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - continuity-of-care
  - rank: 3
    carestaff_id: carestaff-005
    carestaff_name: Sarah Johnson
    score:
      total: '0.84'
      base: '0.70'
      bonus: '0.14'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.84
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - continuity-of-care
  validation:
    success: false
    expected_order:
    - carestaff-002
    - carestaff-001
    - carestaff-005
    actual_order:
    - carestaff-001
    - carestaff-002
    - carestaff-005
    missing_carestaff: []
- patient_id: patient-002
  patient_name: Alice Brown
  required_skills:
  - physical_therapy
  - medication_management
  required_certifications:
  - PT
  matches:
  - rank: 1
    carestaff_id: carestaff-002
    carestaff_name: Robert Johnson
    score:
      total: '0.79'
      base: '0.70'
      bonus: '0.09'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.79
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - language-match
      - experience-match
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
      - gender-preference
      - continuity-of-care
  - rank: 2
    carestaff_id: carestaff-003
    carestaff_name: Maria Garcia
    score:
      total: '0.92'
      base: '0.70'
      bonus: '0.22'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.92
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - continuity-of-care
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
  validation:
    success: false
    expected_order:
    - carestaff-003
    - carestaff-002
    actual_order:
    - carestaff-002
    - carestaff-003
    missing_carestaff: []
- patient_id: patient-003
  patient_name: Bob Wilson
  required_skills:
  - respiratory_therapy
  - medication_management
  required_certifications:
  - RN
  matches:
  - rank: 1
    carestaff_id: carestaff-004
    carestaff_name: David Lee
    score:
      total: '0.92'
      base: '0.70'
      bonus: '0.22'
      formula: Final Score = Base Score (0.7) + Sum(Constraint Bonus x Weight Factor)
        = 0.92
    constraints_summary:
      hard_constraints_satisfied:
      - skill-match
      - service-area
      - date-availability-match
      - certification-match
      - patient-exclusion
      hard_constraints_not_satisfied: []
      soft_constraints_satisfied:
      - gender-preference
      - language-match
      - experience-match
      - continuity-of-care
      - carestaff-workload
      soft_constraints_not_satisfied:
      - preferred-carestaff
  validation:
    success: true
    expected_order:
    - carestaff-004
    actual_order:
    - carestaff-004
    missing_carestaff: []
