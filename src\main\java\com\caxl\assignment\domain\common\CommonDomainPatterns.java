package com.caxl.assignment.domain.common;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.time.LocalDateTime;

/**
 * Common domain patterns and meta-annotations to reduce duplication
 * across domain model nested classes.
 */
public class CommonDomainPatterns {
      /**
     * Meta-annotation that combines common Lombok and Jackson annotations
     * used across all nested domain classes.
     */
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    public @interface DomainNestedClass {
    }
    
    /**
     * Base interface for all nested domain classes to provide common behavior.
     */
    public interface DomainNested {
        /**
         * Validate the nested class instance.
         * @throws IllegalArgumentException if validation fails
         */
        default void validate() {
            // Default implementation does nothing
            // Subclasses can override to provide specific validation
        }
    }
    
    /**
     * Abstract base class for nested domain objects with common annotations.
     * This eliminates the need to repeat @Data @Builder @NoArgsConstructor @AllArgsConstructor @JsonIgnoreProperties
     * on every nested class.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public abstract static class BaseNestedDomain implements DomainNested {
        // Common fields that nested classes might need
        @JsonProperty("created_at")
        private LocalDateTime createdAt;
        
        @JsonProperty("updated_at")
        private LocalDateTime updatedAt;
        
        @JsonProperty("version")
        @Builder.Default
        private Long version = 1L;
    }
    
    /**
     * Common validation utilities for domain objects.
     */
    public static class ValidationHelper {
        
        public static void validateRequired(Object value, String fieldName) {
            if (value == null) {
                throw new IllegalArgumentException(fieldName + " is required and cannot be null");
            }
        }
        
        public static void validateRequiredString(String value, String fieldName) {
            if (value == null || value.trim().isEmpty()) {
                throw new IllegalArgumentException(fieldName + " is required and cannot be null or empty");
            }
        }
        
        public static void validateRequiredCollection(java.util.Collection<?> collection, String fieldName) {
            if (collection == null || collection.isEmpty()) {
                throw new IllegalArgumentException(fieldName + " is required and cannot be null or empty");
            }
        }
        
        public static void validatePositiveNumber(Number value, String fieldName) {
            if (value == null || value.doubleValue() <= 0) {
                throw new IllegalArgumentException(fieldName + " must be a positive number");
            }
        }
        
        public static void validateRange(int value, int min, int max, String fieldName) {
            if (value < min || value > max) {
                throw new IllegalArgumentException(fieldName + " must be between " + min + " and " + max);
            }
        }
    }
}
