package com.caxl.assignment.domain.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;

/**
 * Rule domain models for the clinician assignment system.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Rule {

    @NotBlank(message = "Rule ID is required")
    @JsonProperty("rule_id")
    private String ruleId;

    @NotBlank(message = "Rule name is required")
    private String name;

    @NotBlank(message = "Rule description is required")
    private String description;

    @NotNull(message = "Rule type is required")
    @JsonProperty("rule_type")
    private RuleType ruleType;

    @Valid
    private Condition condition;

    @Builder.Default
    private boolean enabled = true;

    @NotNull(message = "Tags list is required")
    @Builder.Default
    private List<String> tags = List.of();

    @DecimalMin(value = "0.0", message = "Weight cannot be negative")
    @Builder.Default
    private double weight = 1.0;

    @NotBlank(message = "Domain is required")
    @Builder.Default
    private String domain = "matching";

    @JsonProperty("rationale_positive")
    private String rationalePositive;

    @JsonProperty("rationale_negative")
    private String rationaleNegative;

    private String section;

    @JsonProperty("check_hierarchy")
    @Builder.Default
    private boolean checkHierarchy = false;

    // Evaluation function (not serialized)
    private transient BiFunction<Patient, Clinician, Object> evaluationFunction;

    /**
     * Enum for rule types.
     */
    public enum RuleType {
        HARD_CONSTRAINT("hard_constraint"),
        SOFT_CONSTRAINT("soft_constraint"),
        PRIORITIZATION("prioritization"),
        RESCHEDULING("rescheduling"),
        SECTION_HEADER("section_header");

        private final String value;

        RuleType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Enum for operator types.
     */
    public enum OperatorType {
        CONTAINS_ALL("contains_all"),
        CONTAINS_ANY("contains_any"),
        IN("in"),
        NOT_IN("not_in"),
        GREATER_THAN("greater_than"),
        GREATER_THAN_OR_EQUAL("greater_than_or_equal"),
        LESS_THAN("less_than"),
        LESS_THAN_OR_EQUAL("less_than_or_equal"),
        EQUAL("equal"),
        NOT_EQUAL("not_equal"),
        CUSTOM("custom");

        private final String value;

        OperatorType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /**
     * Condition class for rule evaluation.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Condition {

        @NotNull(message = "Operator is required")
        private OperatorType operator;

        @JsonProperty("left_operand")
        private String leftOperand;

        @JsonProperty("right_operand")
        private String rightOperand;

        private String function;

        @Builder.Default
        private Map<String, Object> parameters = Map.of();
    }

    /**
     * Evaluate the rule for a patient-clinician pair.
     * This is an abstract method that should be implemented by specific rule types.
     *
     * @param patient   The patient object
     * @param clinician The clinician object
     * @return The evaluation result (boolean for hard rules, double for soft rules)
     */
    public Object evaluate(Patient patient, Clinician clinician) {
        if (!enabled || evaluationFunction == null) {
            return getDefaultResult();
        }
        return evaluationFunction.apply(patient, clinician);
    }

    /**
     * Get the default result for this rule type when evaluation is not possible.
     *
     * @return Default result based on rule type
     */
    private Object getDefaultResult() {
        return switch (ruleType) {
            case HARD_CONSTRAINT, SECTION_HEADER -> true;
            case SOFT_CONSTRAINT, PRIORITIZATION, RESCHEDULING -> 0.0;
        };
    }

    /**
     * Hard rule implementation.
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HardRule extends Rule {

        public HardRule(String ruleId, String name, String description, Condition condition,
                       boolean enabled, List<String> tags, double weight, String domain,
                       String rationalePositive, String rationaleNegative, String section,
                       boolean checkHierarchy) {
            super(ruleId, name, description, RuleType.HARD_CONSTRAINT, condition, enabled,
                  tags, weight, domain, rationalePositive, rationaleNegative, section,
                  checkHierarchy, null);
        }

        @Override
        public Object evaluate(Patient patient, Clinician clinician) {
            Object result = super.evaluate(patient, clinician);
            return result instanceof Boolean ? result : Boolean.valueOf(result.toString());
        }
    }

    /**
     * Soft rule implementation.
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class SoftRule extends Rule {

        public SoftRule(String ruleId, String name, String description, Condition condition,
                       boolean enabled, List<String> tags, double weight, String domain,
                       String rationalePositive, String rationaleNegative, String section,
                       boolean checkHierarchy) {
            super(ruleId, name, description, RuleType.SOFT_CONSTRAINT, condition, enabled,
                  tags, weight, domain, rationalePositive, rationaleNegative, section,
                  checkHierarchy, null);
        }

        @Override
        public Object evaluate(Patient patient, Clinician clinician) {
            Object result = super.evaluate(patient, clinician);
            if (result instanceof Number) {
                return getWeight() * ((Number) result).doubleValue();
            }
            return getWeight() * (Boolean.TRUE.equals(result) ? 1.0 : 0.0);
        }
    }

    /**
     * Prioritization rule implementation.
     */
    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PrioritizationRule extends Rule {

        public PrioritizationRule(String ruleId, String name, String description, Condition condition,
                                 boolean enabled, List<String> tags, double weight, String domain,
                                 String rationalePositive, String rationaleNegative, String section,
                                 boolean checkHierarchy) {
            super(ruleId, name, description, RuleType.PRIORITIZATION, condition, enabled,
                  tags, weight, domain, rationalePositive, rationaleNegative, section,
                  checkHierarchy, null);
        }

        @Override
        public Object evaluate(Patient patient, Clinician clinician) {
            Object result = super.evaluate(patient, clinician);
            if (result instanceof Number) {
                return getWeight() * ((Number) result).doubleValue();
            }
            return getWeight() * (Boolean.TRUE.equals(result) ? 1.0 : 0.0);
        }
    }
}
