package com.caxl.assignment.config;

import ai.timefold.solver.core.api.solver.SolverFactory;
import ai.timefold.solver.core.api.solver.SolverManager;
import ai.timefold.solver.core.config.solver.SolverConfig;
import com.caxl.assignment.domain.models.healthcare.HomecareSchedule;
import com.caxl.assignment.domain.constraints.healthcare.HomecareConstraintProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.UUID;

/**
 * Configuration for Timefold Solver for homecare scheduling optimization.
 * This configures the solver with US state compliance and geofencing capabilities.
 */
@Configuration
public class TimefoldSolverConfig {

    @Bean
    public SolverConfig solverConfig() {
        return new SolverConfig()
                .withSolutionClass(HomecareSchedule.class)
                .withEntityClasses(com.caxl.assignment.domain.models.healthcare.HomecareVisit.class)
                .withConstraintProviderClass(HomecareConstraintProvider.class)
                .withTerminationSpentLimit(Duration.ofSeconds(30));
    }

    @Bean
    public SolverFactory<HomecareSchedule> solverFactory(SolverConfig solverConfig) {
        return SolverFactory.create(solverConfig);
    }

    @Bean
    public SolverManager<HomecareSchedule, java.util.UUID> solverManager(SolverFactory<HomecareSchedule> solverFactory) {
        return SolverManager.create(solverFactory);
    }
}
