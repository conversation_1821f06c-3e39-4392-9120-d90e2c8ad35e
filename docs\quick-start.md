# Quick Start Guide - CareStaff Matching Service

## Get Up and Running in 5 Minutes

This guide will help you quickly set up and test the CareStaff Matching Service.

## Prerequisites

- Java 17 or higher
- Docker and Docker Compose
- Maven 3.6+
- curl (for testing)

## Step 1: Start the Database

```bash
# Start PostgreSQL with PostGIS
docker-compose -f docker-compose-carestaff.yml up postgres-carestaff -d

# Wait for database to be ready (about 30 seconds)
docker-compose -f docker-compose-carestaff.yml logs postgres-carestaff
```

## Step 2: Run the Application

```bash
# Build and start the application
mvn clean spring-boot:run

# Or build and run the JAR
mvn clean package
java -jar target/carestaff-matching-service-1.0.0.jar
```

The application will start on `http://localhost:8080`

## Step 3: Verify Setup

```bash
# Check application health
curl http://localhost:8080/actuator/health

# Expected response:
# {"status":"UP"}
```

## Step 4: Test the API

### 4.1 Create a Test Service Request

First, let's create a service request in the database. The sample data initializer should have created some test data.

### 4.2 Generate Match Suggestions

```bash
# Replace {request_id} with an actual UUID from your database
curl -X POST http://localhost:8080/api/v1/service-requests/{request_id}/suggest-matches

# Example with a sample UUID:
curl -X POST http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-************/suggest-matches
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Generated 0 match suggestions",
  "data": [],
  "timestamp": "2024-01-14T10:30:00"
}
```

*Note: You may get 0 suggestions initially as the test database may not have matching carestaff data.*

### 4.3 Retrieve Existing Suggestions

```bash
curl -X GET http://localhost:8080/api/v1/service-requests/{request_id}/suggestions
```

### 4.4 Test Override Match (if you have suggestions)

```bash
curl -X POST http://localhost:8080/api/v1/service-requests/{request_id}/override-match \
  -H "Content-Type: application/json" \
  -d '{
    "selected_carestaff_id": "550e8400-e29b-41d4-a716-************",
    "reason": "Patient request",
    "explanation": "Patient specifically requested this caregiver"
  }'
```

## Step 5: Explore the Configuration

### 5.1 Check Active Configuration

The system uses a JSON-based configuration stored in the database. You can view the active configuration by connecting to the database:

```bash
# Connect to the database
docker exec -it caxl-postgres-carestaff psql -U caxl_user -d homecare_scheduling

# View active configuration
SELECT config_name, criteria_json FROM matching_configuration WHERE is_active = true;
```

### 5.2 Sample Configuration Structure

```json
{
  "filters": {
    "requiredSkillsMustMatchAll": false,
    "proximitySearchRadiusKm": 50.0,
    "mustRespectGeoServiceArea": true,
    "mustRespectAvailability": true,
    "mustNotBeBarred": true,
    "minScoreThreshold": 0.0
  },
  "scoring_weights": {
    "skillMatchBonusPerRequiredSkill": 10.0,
    "proximityKmPenalty": -1.0,
    "preferredCareStaffBonus": 15.0,
    "languageMatchBonus": 8.0,
    "experienceLevelBonusPerYear": 2.0
  }
}
```

## Step 6: Add Test Data (Optional)

To see the matching in action, you can add test data:

### 6.1 Add a Test Patient

```sql
INSERT INTO patient (patient_id, enc_first_name, enc_last_name, location_id, preferred_language, is_active)
VALUES (
  '550e8400-e29b-41d4-a716-446655440010',
  'John',
  'Doe',
  (SELECT location_id FROM location LIMIT 1),
  'English',
  true
);
```

### 6.2 Add a Test CareStaff

```sql
INSERT INTO caresstaff_pe (carestaff_id, enc_first_name, enc_last_name, location_id, experience_years, active)
VALUES (
  '550e8400-e29b-41d4-a716-446655440011',
  'Jane',
  'Smith',
  (SELECT location_id FROM location LIMIT 1),
  5,
  true
);
```

### 6.3 Add a Test Service Request

```sql
INSERT INTO service_request (
  request_id, patient_id, status, visit_type, priority, workload_points,
  arrival_window_start, arrival_window_end, visit_duration_minutes
) VALUES (
  '550e8400-e29b-41d4-a716-446655440012',
  '550e8400-e29b-41d4-a716-446655440010',
  'pending',
  'routine_checkup',
  3,
  2,
  NOW() + INTERVAL '1 day',
  NOW() + INTERVAL '1 day' + INTERVAL '8 hours',
  60
);
```

## Step 7: Test with Real Data

Now test the matching with your new data:

```bash
curl -X POST http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-446655440012/suggest-matches
```

## Common Issues and Solutions

### Issue: Database Connection Failed
**Solution:** Ensure PostgreSQL is running and accessible:
```bash
docker-compose -f docker-compose-carestaff.yml ps
docker-compose -f docker-compose-carestaff.yml logs postgres-carestaff
```

### Issue: No Match Suggestions Generated
**Possible Causes:**
1. No carestaff data in database
2. No matching skills or geographic coverage
3. All staff are unavailable

**Solution:** Add test data as shown in Step 6.

### Issue: Application Won't Start
**Solution:** Check Java version and Maven configuration:
```bash
java -version  # Should be 17+
mvn -version   # Should be 3.6+
```

### Issue: Port Already in Use
**Solution:** Change the port in `application.yml`:
```yaml
server:
  port: 8081
```

## Next Steps

1. **Explore the API**: Try all endpoints with different parameters
2. **Customize Configuration**: Modify the matching criteria JSON
3. **Add More Test Data**: Create realistic patient and carestaff scenarios
4. **Review Logs**: Check application logs for detailed matching information
5. **Read Documentation**: Explore the full documentation in the `docs/` folder

## Development Mode

For development, you can run with additional debugging:

```bash
# Run with debug logging
mvn spring-boot:run -Dspring-boot.run.arguments="--logging.level.com.caxl.assignment=DEBUG"

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

## Docker Quick Start

For a complete Docker setup:

```bash
# Start everything with Docker
docker-compose -f docker-compose-carestaff.yml up

# The application will be available at http://localhost:8081
```

## Support

If you encounter issues:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review the [Installation Guide](installation.md) for detailed setup
3. Check application logs for error messages
4. Verify database connectivity and data

You're now ready to explore the CareStaff Matching Service! 🚀
