package com.caxl.assignment.infrastructure.config;

import com.caxl.assignment.domain.models.assignment.DynamicRule;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Configuration loader for dynamic rules from JSON configuration files.
 * Supports loading rules from multiple sources and auto-creation of rule objects.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DynamicRuleConfigurationLoader {

    private final ObjectMapper objectMapper;
    
    private List<DynamicRule> cachedRules;
    private long lastLoadTime;
    private static final long CACHE_DURATION_MS = 300000; // 5 minutes

    /**
     * Load dynamic rules from JSON configuration file.
     * 
     * @param configPath Path to the JSON configuration file
     * @return List of dynamic rules
     */
    public List<DynamicRule> loadRules(String configPath) {
        log.info("Loading dynamic rules from configuration: {}", configPath);
        
        try {
            Resource resource = new ClassPathResource(configPath);
            
            if (!resource.exists()) {
                log.warn("Configuration file not found: {}", configPath);
                return List.of();
            }
            
            try (InputStream inputStream = resource.getInputStream()) {
                RuleConfiguration config = objectMapper.readValue(inputStream, RuleConfiguration.class);
                
                List<DynamicRule> rules = config.getRules();
                log.info("Successfully loaded {} dynamic rules from configuration", rules.size());
                
                // Cache the rules
                cachedRules = rules;
                lastLoadTime = System.currentTimeMillis();
                
                return rules;
            }
            
        } catch (IOException e) {
            log.error("Error loading dynamic rules configuration: {}", e.getMessage(), e);
            return List.of();
        }
    }

    /**
     * Load rules from default configuration file.
     */
    public List<DynamicRule> loadDefaultRules() {
        return loadRules("rules.json");
    }

    /**
     * Get cached rules if available and not expired.
     */
    public List<DynamicRule> getCachedRules() {
        if (cachedRules != null && 
            (System.currentTimeMillis() - lastLoadTime) < CACHE_DURATION_MS) {
            return cachedRules;
        }
        
        return loadDefaultRules();
    }

    /**
     * Load rules filtered by level.
     */
    public List<DynamicRule> loadRulesByLevel(DynamicRule.RuleLevel level) {
        return getCachedRules().stream()
                .filter(rule -> rule.getLevel() == level)
                .collect(Collectors.toList());
    }

    /**
     * Load rules filtered by type.
     */
    public List<DynamicRule> loadRulesByType(DynamicRule.RuleType type) {
        return getCachedRules().stream()
                .filter(rule -> rule.getType() == type)
                .collect(Collectors.toList());
    }

    /**
     * Load rules filtered by level and type.
     */
    public List<DynamicRule> loadRules(DynamicRule.RuleLevel level, DynamicRule.RuleType type) {
        return getCachedRules().stream()
                .filter(rule -> rule.getLevel() == level && rule.getType() == type)
                .collect(Collectors.toList());
    }

    /**
     * Reload rules from configuration (bypass cache).
     */
    public List<DynamicRule> reloadRules() {
        cachedRules = null;
        return loadDefaultRules();
    }

    /**
     * Validate rule configuration.
     */
    public boolean validateRuleConfiguration(String configPath) {
        try {
            List<DynamicRule> rules = loadRules(configPath);
            return validateRules(rules);
        } catch (Exception e) {
            log.error("Rule configuration validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Validate individual rules.
     */
    private boolean validateRules(List<DynamicRule> rules) {
        for (DynamicRule rule : rules) {
            if (!validateRule(rule)) {
                log.error("Invalid rule configuration: {}", rule.getRuleId());
                return false;
            }
        }
        return true;
    }

    /**
     * Validate a single rule.
     */
    private boolean validateRule(DynamicRule rule) {
        // Check required fields
        if (rule.getRuleId() == null || rule.getRuleId().trim().isEmpty()) {
            log.error("Rule ID is required");
            return false;
        }
        
        if (rule.getName() == null || rule.getName().trim().isEmpty()) {
            log.error("Rule name is required for rule: {}", rule.getRuleId());
            return false;
        }
        
        if (rule.getLevel() == null) {
            log.error("Rule level is required for rule: {}", rule.getRuleId());
            return false;
        }
        
        if (rule.getType() == null) {
            log.error("Rule type is required for rule: {}", rule.getRuleId());
            return false;
        }
        
        if (rule.getField() == null || rule.getField().trim().isEmpty()) {
            log.error("Rule field is required for rule: {}", rule.getRuleId());
            return false;
        }
        
        if (rule.getOperator() == null) {
            log.error("Rule operator is required for rule: {}", rule.getRuleId());
            return false;
        }
        
        // Validate operator-specific requirements
        if (!validateOperatorRequirements(rule)) {
            return false;
        }
        
        return true;
    }

    /**
     * Validate operator-specific requirements.
     */
    private boolean validateOperatorRequirements(DynamicRule rule) {
        switch (rule.getOperator()) {
            case CONTAINS_ALL, CONTAINS_ANY, IN, NOT_IN -> {
                if (rule.getValue() == null) {
                    log.error("Value is required for operator {} in rule: {}", 
                             rule.getOperator(), rule.getRuleId());
                    return false;
                }
            }
            case GREATER_THAN, GREATER_THAN_OR_EQUAL, LESS_THAN, LESS_THAN_OR_EQUAL -> {
                if (rule.getValue() == null || !(rule.getValue() instanceof Number)) {
                    log.error("Numeric value is required for operator {} in rule: {}", 
                             rule.getOperator(), rule.getRuleId());
                    return false;
                }
            }
            case WITHIN_DISTANCE -> {
                if (rule.getValue() == null || !(rule.getValue() instanceof Number)) {
                    log.error("Distance value is required for operator {} in rule: {}", 
                             rule.getOperator(), rule.getRuleId());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * Get rule statistics.
     */
    public RuleStatistics getRuleStatistics() {
        List<DynamicRule> rules = getCachedRules();
        
        Map<DynamicRule.RuleLevel, Long> rulesByLevel = rules.stream()
                .collect(Collectors.groupingBy(DynamicRule::getLevel, Collectors.counting()));
        
        Map<DynamicRule.RuleType, Long> rulesByType = rules.stream()
                .collect(Collectors.groupingBy(DynamicRule::getType, Collectors.counting()));
        
        Map<DynamicRule.RuleOperator, Long> rulesByOperator = rules.stream()
                .collect(Collectors.groupingBy(DynamicRule::getOperator, Collectors.counting()));
        
        long enabledRules = rules.stream().mapToLong(rule -> rule.isEnabled() ? 1 : 0).sum();
        
        return RuleStatistics.builder()
                .totalRules(rules.size())
                .enabledRules((int) enabledRules)
                .disabledRules(rules.size() - (int) enabledRules)
                .rulesByLevel(rulesByLevel)
                .rulesByType(rulesByType)
                .rulesByOperator(rulesByOperator)
                .lastLoadTime(lastLoadTime)
                .build();
    }

    /**
     * Initialize default configuration on startup.
     */
    @PostConstruct
    public void initialize() {
        log.info("Initializing dynamic rule configuration loader");
        loadDefaultRules();
    }

    /**
     * Configuration wrapper class for JSON deserialization.
     */
    public static class RuleConfiguration {
        private String version;
        private String description;
        private List<DynamicRule> rules;
        private Map<String, Object> metadata;

        // Getters and setters
        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public List<DynamicRule> getRules() { return rules; }
        public void setRules(List<DynamicRule> rules) { this.rules = rules; }
        
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * Statistics about loaded rules.
     */
    public static class RuleStatistics {
        private final int totalRules;
        private final int enabledRules;
        private final int disabledRules;
        private final Map<DynamicRule.RuleLevel, Long> rulesByLevel;
        private final Map<DynamicRule.RuleType, Long> rulesByType;
        private final Map<DynamicRule.RuleOperator, Long> rulesByOperator;
        private final long lastLoadTime;

        private RuleStatistics(Builder builder) {
            this.totalRules = builder.totalRules;
            this.enabledRules = builder.enabledRules;
            this.disabledRules = builder.disabledRules;
            this.rulesByLevel = builder.rulesByLevel;
            this.rulesByType = builder.rulesByType;
            this.rulesByOperator = builder.rulesByOperator;
            this.lastLoadTime = builder.lastLoadTime;
        }

        public static Builder builder() {
            return new Builder();
        }

        // Getters
        public int getTotalRules() { return totalRules; }
        public int getEnabledRules() { return enabledRules; }
        public int getDisabledRules() { return disabledRules; }
        public Map<DynamicRule.RuleLevel, Long> getRulesByLevel() { return rulesByLevel; }
        public Map<DynamicRule.RuleType, Long> getRulesByType() { return rulesByType; }
        public Map<DynamicRule.RuleOperator, Long> getRulesByOperator() { return rulesByOperator; }
        public long getLastLoadTime() { return lastLoadTime; }

        public static class Builder {
            private int totalRules;
            private int enabledRules;
            private int disabledRules;
            private Map<DynamicRule.RuleLevel, Long> rulesByLevel;
            private Map<DynamicRule.RuleType, Long> rulesByType;
            private Map<DynamicRule.RuleOperator, Long> rulesByOperator;
            private long lastLoadTime;

            public Builder totalRules(int totalRules) { this.totalRules = totalRules; return this; }
            public Builder enabledRules(int enabledRules) { this.enabledRules = enabledRules; return this; }
            public Builder disabledRules(int disabledRules) { this.disabledRules = disabledRules; return this; }
            public Builder rulesByLevel(Map<DynamicRule.RuleLevel, Long> rulesByLevel) { this.rulesByLevel = rulesByLevel; return this; }
            public Builder rulesByType(Map<DynamicRule.RuleType, Long> rulesByType) { this.rulesByType = rulesByType; return this; }
            public Builder rulesByOperator(Map<DynamicRule.RuleOperator, Long> rulesByOperator) { this.rulesByOperator = rulesByOperator; return this; }
            public Builder lastLoadTime(long lastLoadTime) { this.lastLoadTime = lastLoadTime; return this; }

            public RuleStatistics build() {
                return new RuleStatistics(this);
            }
        }
    }
}
