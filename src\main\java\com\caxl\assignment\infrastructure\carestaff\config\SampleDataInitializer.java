package com.caxl.assignment.infrastructure.config;

import com.caxl.assignment.infrastructure.persistence.entity.MatchingConfigurationEntity;
import com.caxl.assignment.infrastructure.persistence.entity.ServiceRequestEntity;
import com.caxl.assignment.infrastructure.persistence.repository.MatchingConfigurationRepository;
import com.caxl.assignment.infrastructure.persistence.repository.ServiceRequestRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Sample data initializer for demonstration purposes.
 * Creates default matching configuration and sample service requests.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SampleDataInitializer implements CommandLineRunner {

    private final MatchingConfigurationRepository matchingConfigRepository;
    private final ServiceRequestRepository serviceRequestRepository;
    private final ObjectMapper objectMapper;

    @Override
    public void run(String... args) throws Exception {
        initializeDefaultMatchingConfiguration();
        initializeSampleServiceRequests();
    }

    private void initializeDefaultMatchingConfiguration() {
        try {
            // Check if active configuration already exists
            if (matchingConfigRepository.findActiveConfiguration().isPresent()) {
                log.info("Active matching configuration already exists, skipping initialization");
                return;
            }

            // Create default matching criteria JSON
            String defaultCriteriaJson = """
                {
                  "filters": {
                    "requiredSkillsMustMatchAll": false,
                    "proximitySearchRadiusKm": 50.0,
                    "mustRespectGeoServiceArea": true,
                    "mustRespectAvailability": true,
                    "mustNotBeBarred": true,
                    "minScoreThreshold": 0.0
                  },
                  "scoring_weights": {
                    "skillMatchBonusPerRequiredSkill": 10.0,
                    "skillMatchAllBonus": 50.0,
                    "proximityKmPenalty": -1.0,
                    "geoServiceAreaBonus": 20.0,
                    "availabilityWindowFitPenaltyPerMinuteDeviation": -0.5,
                    "preferredCareStaffBonus": 15.0,
                    "continuityBonusPerRecentVisit": 5.0,
                    "languageMatchBonus": 8.0,
                    "experienceLevelBonusPerYear": 2.0
                  },
                  "geography": {
                    "staffServiceGeofenceTypes": ["service_area", "county_area"],
                    "geofenceStrictContainmentOnly": true
                  },
                  "availability": {
                    "overlapThresholdMinutes": 1,
                    "minTimeBeforeVisitMinutes": 30,
                    "minTimeAfterVisitMinutes": 30
                  }
                }
                """;

            MatchingConfigurationEntity defaultConfig = MatchingConfigurationEntity.builder()
                    .configId(UUID.randomUUID())
                    .configName("default_matching_config")
                    .criteriaJson(defaultCriteriaJson)
                    .isActive(true)
                    .createdBy("system")
                    .build();

            matchingConfigRepository.save(defaultConfig);
            log.info("Created default matching configuration: {}", defaultConfig.getConfigId());

        } catch (Exception e) {
            log.error("Error initializing default matching configuration: {}", e.getMessage());
        }
    }

    private void initializeSampleServiceRequests() {
        try {
            // Check if sample data already exists
            if (!serviceRequestRepository.findByStatus("pending").isEmpty()) {
                log.info("Sample service requests already exist, skipping initialization");
                return;
            }

            // Create sample service requests
            List<ServiceRequestEntity> sampleRequests = List.of(
                    ServiceRequestEntity.builder()
                            .requestId(UUID.randomUUID())
                            .patientId(UUID.randomUUID()) // This would reference an actual patient
                            .status("pending")
                            .requiredSkillIds(List.of(UUID.randomUUID(), UUID.randomUUID()))
                            .arrivalWindowStart(LocalDateTime.now().plusDays(1).withHour(9).withMinute(0))
                            .arrivalWindowEnd(LocalDateTime.now().plusDays(1).withHour(17).withMinute(0))
                            .visitDurationMinutes(60)
                            .visitType("routine_checkup")
                            .priority(3)
                            .workloadPoints(2)
                            .specialInstructions("Patient requires assistance with mobility")
                            .build(),

                    ServiceRequestEntity.builder()
                            .requestId(UUID.randomUUID())
                            .patientId(UUID.randomUUID())
                            .status("pending")
                            .requiredSkillIds(List.of(UUID.randomUUID()))
                            .arrivalWindowStart(LocalDateTime.now().plusDays(2).withHour(10).withMinute(0))
                            .arrivalWindowEnd(LocalDateTime.now().plusDays(2).withHour(16).withMinute(0))
                            .visitDurationMinutes(90)
                            .visitType("wound_care")
                            .priority(2)
                            .workloadPoints(3)
                            .specialInstructions("Sterile technique required")
                            .build(),

                    ServiceRequestEntity.builder()
                            .requestId(UUID.randomUUID())
                            .patientId(UUID.randomUUID())
                            .status("pending")
                            .requiredSkillIds(List.of(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID()))
                            .arrivalWindowStart(LocalDateTime.now().plusDays(1).withHour(14).withMinute(0))
                            .arrivalWindowEnd(LocalDateTime.now().plusDays(1).withHour(18).withMinute(0))
                            .visitDurationMinutes(120)
                            .visitType("medication_management")
                            .priority(1)
                            .workloadPoints(4)
                            .specialInstructions("Complex medication regimen, requires RN")
                            .build()
            );

            serviceRequestRepository.saveAll(sampleRequests);
            log.info("Created {} sample service requests", sampleRequests.size());

        } catch (Exception e) {
            log.error("Error initializing sample service requests: {}", e.getMessage());
        }
    }
}
