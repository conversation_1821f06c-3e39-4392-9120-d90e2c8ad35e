package com.caxl.assignment.infrastructure.adapters;

import java.util.List;

import org.springframework.stereotype.Component;

import com.caxl.assignment.domain.models.Rule;
import com.caxl.assignment.application.port.RuleRepository;
import com.caxl.assignment.infrastructure.common.RepositoryUtils;

/**
 * File-based implementation of the RuleRepository interface.
 */
@Component
public class RuleRepositoryAdapter implements RuleRepository {

    private final List<Rule> rules;

    public RuleRepositoryAdapter(List<Rule> rules) {
        this.rules = rules;
    }    @Override
    public List<Rule> getHardRules() {
        return RepositoryUtils.filterBy(rules, rule -> rule.getRuleType() == Rule.RuleType.HARD_CONSTRAINT);
    }

    @Override
    public List<Rule> getSoftRules() {
        return RepositoryUtils.filterBy(rules, rule -> rule.getRuleType() == Rule.RuleType.SOFT_CONSTRAINT);
    }

    @Override
    public List<Rule> getPrioritizationRules() {
        return RepositoryUtils.filterBy(rules, rule -> rule.getRuleType() == Rule.RuleType.PRIORITIZATION);
    }

    @Override
    public List<Rule> getReschedulingRules() {
        return RepositoryUtils.filterBy(rules, rule -> rule.getRuleType() == Rule.RuleType.RESCHEDULING);
    }

    @Override
    public List<Rule> getAllRules() {
        return rules;
    }    @Override
    public List<Rule> getRulesByDomain(String domain) {
        return RepositoryUtils.filterBy(rules, rule -> domain.equals(rule.getDomain()));
    }

    @Override
    public Rule getRuleById(String ruleId) {
        return RepositoryUtils.findFirstBy(rules, rule -> rule.getRuleId().equals(ruleId))
                .orElse(null);
    }    @Override
    public boolean areRulesLoaded() {
        return !RepositoryUtils.isNullOrEmpty(rules);
    }

    @Override
    public void reloadRules() {
        // Implementation would reload rules from configuration
        // For now, this is a no-op as rules are injected via constructor
    }

    public List<Rule> getRulesByTags(List<String> tags) {
        return RepositoryUtils.filterBy(rules, rule -> rule.getTags() != null && rule.getTags().containsAll(tags));
    }

    public List<Rule> getEnabledRules() {
        return RepositoryUtils.filterBy(rules, Rule::isEnabled);
    }
}
