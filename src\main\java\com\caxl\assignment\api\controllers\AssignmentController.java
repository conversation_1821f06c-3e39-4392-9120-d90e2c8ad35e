package com.caxl.assignment.api.controllers;

import com.caxl.assignment.api.dtos.AssignmentResponse;
import com.caxl.assignment.api.dtos.CreateAssignmentRequest;
import com.caxl.assignment.application.services.SchedulingService;
import com.caxl.assignment.domain.exceptions.NoFeasibleAssignmentException;
import com.caxl.assignment.domain.models.Assignment;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.Data;
import lombok.Builder;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;

/**
 * REST controller for assignment operations.
 */
@RestController
@RequestMapping("/api/v1/assignments")
@RequiredArgsConstructor
@Slf4j
public class AssignmentController {

    private final SchedulingService schedulingService;

    /**
     * Create assignments for patients and clinicians.
     *
     * @param request The assignment request
     * @return Assignment response with optimization details
     */
    @PostMapping
    public ResponseEntity<AssignmentResponse> createAssignments(
            @Valid @RequestBody CreateAssignmentRequest request
    ) {
        log.info("Creating assignments for {} patients and {} clinicians",
                request.getPatients().size(), request.getClinicians().size());

        long startTime = System.currentTimeMillis();

        try {
            // Create assignments using optimal Timefold scheduling
            List<Assignment> assignments = schedulingService.createAssignments(
                    request.getPatients(),
                    request.getClinicians(),
                    request.getRelaxationDetails()
            );

            long endTime = System.currentTimeMillis();

            // Create response with optimization details
            AssignmentResponse response = AssignmentResponse.builder()
                    .assignments(assignments)
                    .optimizationDetails(AssignmentResponse.OptimizationDetails.builder()
                            .solvingTimeMs(endTime - startTime)
                            .assignedPatients(assignments.size())
                            .totalPatients(request.getPatients().size())
                            .totalScore(assignments.stream()
                                    .mapToDouble(a -> a.getRationale().getTotalScore())
                                    .sum())
                            .solverStatus("COMPLETED")
                            .build())
                    .build();

            log.info("Successfully created {} assignments in {}ms",
                    assignments.size(), endTime - startTime);

            return ResponseEntity.ok(response);

        } catch (NoFeasibleAssignmentException e) {
            log.error("Failed to find feasible assignments: {}", e.getMessage());

            // Return partial solution with error status
            AssignmentResponse response = AssignmentResponse.builder()
                    .optimizationDetails(AssignmentResponse.OptimizationDetails.builder()
                            .solvingTimeMs(System.currentTimeMillis() - startTime)
                            .assignedPatients(0)
                            .totalPatients(request.getPatients().size())
                            .totalScore(0.0)
                            .solverStatus("FAILED")
                            .build())
                    .build();

            return ResponseEntity.ok(response);
        }
    }

    /**
     * Handle validation exceptions.
     */
    @ExceptionHandler(NoFeasibleAssignmentException.class)
    public ResponseEntity<ErrorResponse> handleNoFeasibleAssignmentException(NoFeasibleAssignmentException e) {
        ErrorResponse error = ErrorResponse.builder()
                .timestamp(Instant.now().toString())
                .status(400)
                .error("Bad Request")
                .message(e.getMessage())
                .build();

        return ResponseEntity.badRequest().body(error);
    }

    /**
     * Error response DTO.
     */
    @Data
    @Builder
    private static class ErrorResponse {
        private String timestamp;
        private int status;
        private String error;
        private String message;
    }
}
