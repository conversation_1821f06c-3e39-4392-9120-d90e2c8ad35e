package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.constraints.healthcare.TestDataFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.List;

/**
 * Comprehensive test suite for workload balance constraints in homecare scheduling.
 * Tests fair distribution of visits and workload among care staff.
 */
@DisplayName("Workload Balance Constraint Tests")
class WorkloadBalanceConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
                new EnhancedHomecareConstraintProvider(),
                HomecareSchedule.class,
                HomecareVisit.class);
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Workload Distribution")
    class BasicWorkloadDistribution {

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenEqualDistribution_shouldMinimizePenalty")
        void testWorkloadBalanceConstraint_whenEqualDistribution_shouldMinimizePenalty() {
            // Given: Three care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            CareStaff staff3 = testDataFactory.createCareStaffWithFullAvailability("staff3");
            
            // And: Six visits equally distributed (2 each)
            HomecareVisit visit1 = testDataFactory.createBasicVisit("visit1");
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createBasicVisit("visit2");
            visit2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit3 = testDataFactory.createBasicVisit("visit3");
            visit3.setAssignedCareStaffId("staff2");
            
            HomecareVisit visit4 = testDataFactory.createBasicVisit("visit4");
            visit4.setAssignedCareStaffId("staff2");
            
            HomecareVisit visit5 = testDataFactory.createBasicVisit("visit5");
            visit5.setAssignedCareStaffId("staff3");
            
            HomecareVisit visit6 = testDataFactory.createBasicVisit("visit6");
            visit6.setAssignedCareStaffId("staff3");
            
            // When: Verifying the constraint
            // Then: Should have minimal penalty for balanced distribution
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, staff3, visit1, visit2, visit3, visit4, visit5, visit6)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenUnevenDistribution_shouldPenalize")
        void testWorkloadBalanceConstraint_whenUnevenDistribution_shouldPenalize() {
            // Given: Three care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            CareStaff staff3 = testDataFactory.createCareStaffWithFullAvailability("staff3");
            
            // And: Uneven distribution (4, 1, 1)
            HomecareVisit visit1 = testDataFactory.createBasicVisit("visit1");
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createBasicVisit("visit2");
            visit2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit3 = testDataFactory.createBasicVisit("visit3");
            visit3.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit4 = testDataFactory.createBasicVisit("visit4");
            visit4.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit5 = testDataFactory.createBasicVisit("visit5");
            visit5.setAssignedCareStaffId("staff2");
            
            HomecareVisit visit6 = testDataFactory.createBasicVisit("visit6");
            visit6.setAssignedCareStaffId("staff3");
            
            // When: Verifying the constraint
            // Then: Should penalize for uneven distribution
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, staff3, visit1, visit2, visit3, visit4, visit5, visit6)
                    .penalizesBy(4); // Significant imbalance penalty
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenSingleStaffAllVisits_shouldHighlyPenalize")
        void testWorkloadBalanceConstraint_whenSingleStaffAllVisits_shouldHighlyPenalize() {
            // Given: Multiple care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            CareStaff staff3 = testDataFactory.createCareStaffWithFullAvailability("staff3");
            
            // And: All visits assigned to one staff member
            HomecareVisit visit1 = testDataFactory.createBasicVisit("visit1");
            visit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit2 = testDataFactory.createBasicVisit("visit2");
            visit2.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit3 = testDataFactory.createBasicVisit("visit3");
            visit3.setAssignedCareStaffId("staff1");
            
            HomecareVisit visit4 = testDataFactory.createBasicVisit("visit4");
            visit4.setAssignedCareStaffId("staff1");
            
            // When: Verifying the constraint
            // Then: Should highly penalize extreme imbalance
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, staff3, visit1, visit2, visit3, visit4)
                    .penalizesBy(9); // Very high penalty for extreme imbalance
        }
    }

    @Nested
    @DisplayName("Time-Based Workload Balancing")
    class TimeBasedWorkloadBalancing {

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenBalancedByDuration_shouldMinimizePenalty")
        void testWorkloadBalanceConstraint_whenBalancedByDuration_shouldMinimizePenalty() {
            // Given: Two care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            
            // And: Different number of visits but equal total duration
            // Staff1: 1 long visit (4 hours)
            HomecareVisit longVisit = testDataFactory.createVisitWithTimeWindow("longVisit", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(4));
            longVisit.setAssignedCareStaffId("staff1");
            
            // Staff2: 4 short visits (1 hour each)
            HomecareVisit shortVisit1 = testDataFactory.createVisitWithTimeWindow("shortVisit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(1));
            shortVisit1.setAssignedCareStaffId("staff2");
            
            HomecareVisit shortVisit2 = testDataFactory.createVisitWithTimeWindow("shortVisit2", 
                    LocalDateTime.of(2024, 1, 15, 11, 0), Duration.ofHours(1));
            shortVisit2.setAssignedCareStaffId("staff2");
            
            HomecareVisit shortVisit3 = testDataFactory.createVisitWithTimeWindow("shortVisit3", 
                    LocalDateTime.of(2024, 1, 15, 13, 0), Duration.ofHours(1));
            shortVisit3.setAssignedCareStaffId("staff2");
            
            HomecareVisit shortVisit4 = testDataFactory.createVisitWithTimeWindow("shortVisit4", 
                    LocalDateTime.of(2024, 1, 15, 15, 0), Duration.ofHours(1));
            shortVisit4.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint (if it considers duration)
            // Then: Should have minimal penalty for balanced total duration
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, longVisit, shortVisit1, shortVisit2, shortVisit3, shortVisit4)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenImbalancedByDuration_shouldPenalize")
        void testWorkloadBalanceConstraint_whenImbalancedByDuration_shouldPenalize() {
            // Given: Two care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            
            // And: Significant duration imbalance
            // Staff1: Multiple long visits (8 hours total)
            HomecareVisit longVisit1 = testDataFactory.createVisitWithTimeWindow("longVisit1", 
                    LocalDateTime.of(2024, 1, 15, 9, 0), Duration.ofHours(4));
            longVisit1.setAssignedCareStaffId("staff1");
            
            HomecareVisit longVisit2 = testDataFactory.createVisitWithTimeWindow("longVisit2", 
                    LocalDateTime.of(2024, 1, 15, 14, 0), Duration.ofHours(4));
            longVisit2.setAssignedCareStaffId("staff1");
            
            // Staff2: One short visit (1 hour total)
            HomecareVisit shortVisit = testDataFactory.createVisitWithTimeWindow("shortVisit", 
                    LocalDateTime.of(2024, 1, 15, 10, 0), Duration.ofHours(1));
            shortVisit.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Should penalize for duration imbalance
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, longVisit1, longVisit2, shortVisit)
                    .penalizesBy(1); // Penalty for imbalance
        }
    }

    @Nested
    @DisplayName("Priority-Based Workload Distribution")
    class PriorityBasedWorkloadDistribution {

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenBalancedHighPriorityVisits_shouldOptimize")
        void testWorkloadBalanceConstraint_whenBalancedHighPriorityVisits_shouldOptimize() {
            // Given: Two care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            
            // And: High-priority visits equally distributed
            HomecareVisit highPriority1 = testDataFactory.createVisitWithPriority("highPriority1", 
                    ServicePriority.URGENT);
            highPriority1.setAssignedCareStaffId("staff1");
            
            HomecareVisit highPriority2 = testDataFactory.createVisitWithPriority("highPriority2", 
                    ServicePriority.URGENT);
            highPriority2.setAssignedCareStaffId("staff2");
            
            // And: Regular visits also balanced
            HomecareVisit regular1 = testDataFactory.createVisitWithPriority("regular1", 
                    ServicePriority.ROUTINE);
            regular1.setAssignedCareStaffId("staff1");
            
            HomecareVisit regular2 = testDataFactory.createVisitWithPriority("regular2", 
                    ServicePriority.ROUTINE);
            regular2.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Should favor balanced priority distribution
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, highPriority1, highPriority2, regular1, regular2)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenHighPriorityConcentrated_shouldPenalize")
        void testWorkloadBalanceConstraint_whenHighPriorityConcentrated_shouldPenalize() {
            // Given: Two care staff members
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            
            // And: All high-priority visits assigned to one staff
            HomecareVisit urgent1 = testDataFactory.createVisitWithPriority("urgent1", 
                    ServicePriority.URGENT);
            urgent1.setAssignedCareStaffId("staff1");
            
            HomecareVisit urgent2 = testDataFactory.createVisitWithPriority("urgent2", 
                    ServicePriority.URGENT);
            urgent2.setAssignedCareStaffId("staff1");
            
            HomecareVisit urgent3 = testDataFactory.createVisitWithPriority("urgent3", 
                    ServicePriority.URGENT);
            urgent3.setAssignedCareStaffId("staff1");
            
            // And: Only routine visits for other staff
            HomecareVisit routine1 = testDataFactory.createVisitWithPriority("routine1", 
                    ServicePriority.ROUTINE);
            routine1.setAssignedCareStaffId("staff2");
            
            HomecareVisit routine2 = testDataFactory.createVisitWithPriority("routine2", 
                    ServicePriority.ROUTINE);
            routine2.setAssignedCareStaffId("staff2");
            
            // When: Verifying the constraint
            // Then: Should penalize uneven priority distribution
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, urgent1, urgent2, urgent3, routine1, routine2)
                    .penalizesBy(1); // Penalty for priority imbalance
        }
    }

    @Nested
    @DisplayName("Complex Workload Scenarios")
    class ComplexWorkloadScenarios {

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenMixedAssignmentPatterns_shouldBalanceOverall")
        void testWorkloadBalanceConstraint_whenMixedAssignmentPatterns_shouldBalanceOverall() {
            // Given: Four care staff members with different availability patterns
            CareStaff fullTimeStaff1 = testDataFactory.createCareStaffWithFullAvailability("fullTimeStaff1");
            CareStaff fullTimeStaff2 = testDataFactory.createCareStaffWithFullAvailability("fullTimeStaff2");
            CareStaff partTimeStaff1 = testDataFactory.createCareStaffWithLimitedAvailability("partTimeStaff1");
            CareStaff partTimeStaff2 = testDataFactory.createCareStaffWithLimitedAvailability("partTimeStaff2");
            
            // And: Balanced distribution considering availability
            // Full-time staff get more visits
            List<HomecareVisit> fullTime1Visits = testDataFactory.createMultipleVisits("ft1_", 4, "fullTimeStaff1");
            List<HomecareVisit> fullTime2Visits = testDataFactory.createMultipleVisits("ft2_", 4, "fullTimeStaff2");
            
            // Part-time staff get fewer visits
            List<HomecareVisit> partTime1Visits = testDataFactory.createMultipleVisits("pt1_", 2, "partTimeStaff1");
            List<HomecareVisit> partTime2Visits = testDataFactory.createMultipleVisits("pt2_", 2, "partTimeStaff2");
            
            // When: Verifying the constraint
            // Then: Should have minimal penalty for proportional distribution
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(fullTimeStaff1, fullTimeStaff2, partTimeStaff1, partTimeStaff2)
                    .givenList(fullTime1Visits)
                    .givenList(fullTime2Visits)
                    .givenList(partTime1Visits)
                    .givenList(partTime2Visits)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenNoVisitsAssigned_shouldNotPenalize")
        void testWorkloadBalanceConstraint_whenNoVisitsAssigned_shouldNotPenalize() {
            // Given: Care staff with no assigned visits
            CareStaff staff1 = testDataFactory.createCareStaffWithFullAvailability("staff1");
            CareStaff staff2 = testDataFactory.createCareStaffWithFullAvailability("staff2");
            CareStaff staff3 = testDataFactory.createCareStaffWithFullAvailability("staff3");
            
            // And: Unassigned visits
            HomecareVisit unassigned1 = testDataFactory.createBasicVisit("unassigned1");
            unassigned1.setAssignedCareStaffId(null);
            
            HomecareVisit unassigned2 = testDataFactory.createBasicVisit("unassigned2");
            unassigned2.setAssignedCareStaffId(null);
            
            // When: Verifying the constraint
            // Then: Should not penalize (no workload to balance)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(staff1, staff2, staff3, unassigned1, unassigned2)
                    .penalizesBy(0);
        }

        @Test
        @DisplayName("testWorkloadBalanceConstraint_whenDynamicStaffCount_shouldAdaptBalancing")
        void testWorkloadBalanceConstraint_whenDynamicStaffCount_shouldAdaptBalancing() {
            // Given: Variable number of active staff (some may be unavailable)
            CareStaff activeStaff1 = testDataFactory.createCareStaffWithFullAvailability("activeStaff1");
            CareStaff activeStaff2 = testDataFactory.createCareStaffWithFullAvailability("activeStaff2");
            // Note: No third staff member in this scenario
            
            // And: Visits distributed among available staff
            HomecareVisit visit1 = testDataFactory.createBasicVisit("visit1");
            visit1.setAssignedCareStaffId("activeStaff1");
            
            HomecareVisit visit2 = testDataFactory.createBasicVisit("visit2");
            visit2.setAssignedCareStaffId("activeStaff1");
            
            HomecareVisit visit3 = testDataFactory.createBasicVisit("visit3");
            visit3.setAssignedCareStaffId("activeStaff2");
            
            HomecareVisit visit4 = testDataFactory.createBasicVisit("visit4");
            visit4.setAssignedCareStaffId("activeStaff2");
            
            // When: Verifying the constraint
            // Then: Should balance among available staff only
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalance)
                    .given(activeStaff1, activeStaff2, visit1, visit2, visit3, visit4)
                    .penalizesBy(0);
        }
    }
}
