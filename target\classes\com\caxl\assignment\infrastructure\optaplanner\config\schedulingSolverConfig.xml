<?xml version="1.0" encoding="UTF-8"?>
<solver xmlns="https://timefold.ai/xsd/solver" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="https://timefold.ai/xsd/solver https://timefold.ai/xsd/solver/solver.xsd">

    <!-- Clean domain model configuration -->
    <solutionClass>com.caxl.assignment.domain.scheduling.SchedulingSolution</solutionClass>
    <entityClass>com.caxl.assignment.domain.scheduling.PatientAssignment</entityClass>

    <!-- Constraint provider configuration -->
    <scoreDirectorFactory>
        <constraintProviderClass>com.caxl.assignment.domain.constraints.SchedulingConstraints</constraintProviderClass>
    </scoreDirectorFactory>

    <!-- Simplified termination configuration -->
    <termination>
        <secondsSpentLimit>60</secondsSpentLimit>
        <unimprovedSecondsSpentLimit>20</unimprovedSecondsSpentLimit>
        <bestScoreLimit>0hard/*soft</bestScoreLimit>
    </termination>

    <!-- Phase 1: Construction Heuristic -->
    <constructionHeuristic>
        <constructionHeuristicType>FIRST_FIT_DECREASING</constructionHeuristicType>
    </constructionHeuristic>

    <!-- Phase 2: Local Search -->
    <localSearch>
        <unionMoveSelector>
            <changeMoveSelector/>
            <swapMoveSelector/>
            <listChangeMoveSelector/>
            <listSwapMoveSelector/>
        </unionMoveSelector>

        <acceptor>
            <entityTabuSize>7</entityTabuSize>
        </acceptor>
    </localSearch>

</solver>
