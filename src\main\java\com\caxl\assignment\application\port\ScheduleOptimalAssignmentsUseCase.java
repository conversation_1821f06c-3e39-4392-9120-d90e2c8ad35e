package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Assignment;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;

import java.time.LocalDate;
import java.util.List;

/**
 * Core use case for automated optimal scheduling of carestaff to patients.
 * This is the main entry point for the dynamic scheduling system.
 */
public interface ScheduleOptimalAssignmentsUseCase {

    /**
     * Create optimal assignments for a given date using automated scheduling.
     * 
     * @param patients List of patients requiring care
     * @param clinicians Available carestaff/clinicians
     * @param schedulingDate Date to schedule for
     * @param optimizationPreferences Optimization preferences
     * @return Optimal assignment result
     */
    SchedulingResult scheduleOptimalAssignments(
        List<Patient> patients,
        List<Clinician> clinicians, 
        LocalDate schedulingDate,
        OptimizationPreferences optimizationPreferences
    );

    /**
     * Create optimal assignments for multiple dates (batch scheduling).
     * 
     * @param patients List of patients requiring care
     * @param clinicians Available carestaff/clinicians
     * @param schedulingPeriod Date range to schedule for
     * @param optimizationPreferences Optimization preferences
     * @return Batch scheduling result
     */
    BatchSchedulingResult scheduleOptimalAssignmentsBatch(
        List<Patient> patients,
        List<Clinician> clinicians,
        List<LocalDate> schedulingPeriod,
        OptimizationPreferences optimizationPreferences
    );

    /**
     * Optimization preferences for scheduling.
     */
    record OptimizationPreferences(
        int timeLimitSeconds,
        int unimprovedTimeLimitSeconds,
        String optimizationStrategy,
        boolean enableRelaxation,
        double minAcceptableScore
    ) {
        public static OptimizationPreferences defaultPreferences() {
            return new OptimizationPreferences(30, 10, "BALANCED", true, 0.0);
        }
    }

    /**
     * Result of scheduling operation.
     */
    record SchedulingResult(
        List<Assignment> assignments,
        SchedulingMetrics metrics,
        boolean isOptimal,
        String status
    ) {}

    /**
     * Result of batch scheduling operation.
     */
    record BatchSchedulingResult(
        List<SchedulingResult> dailyResults,
        SchedulingMetrics overallMetrics,
        boolean allOptimal,
        String status
    ) {}

    /**
     * Scheduling performance metrics.
     */
    record SchedulingMetrics(
        long solvingTimeMs,
        int totalPatients,
        int assignedPatients,
        int unassignedPatients,
        double totalScore,
        double averageScore,
        String solverStatus
    ) {}
}
