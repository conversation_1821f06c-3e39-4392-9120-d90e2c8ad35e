package com.caxl.carestaff.application.port.out.persistence;

import com.caxl.carestaff.domain.entities.MatchSuggestion;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Port interface for match suggestion persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface MatchSuggestionPort {

    /**
     * Save all match suggestions.
     * 
     * @param suggestions List of match suggestions to save
     * @return List of saved match suggestions
     */
    List<MatchSuggestion> saveAll(List<MatchSuggestion> suggestions);

    /**
     * Find match suggestions by service request ID.
     * 
     * @param serviceRequestId Service request ID
     * @return List of active match suggestions
     */
    List<MatchSuggestion> findByServiceRequestId(UUID serviceRequestId);

    /**
     * Find match suggestions by IDs.
     * 
     * @param suggestionIds List of suggestion IDs
     * @return List of match suggestions
     */
    List<MatchSuggestion> findAllById(List<UUID> suggestionIds);

    /**
     * Find match suggestion by ID.
     * 
     * @param suggestionId Suggestion ID
     * @return Match suggestion if exists
     */
    Optional<MatchSuggestion> findById(UUID suggestionId);

    /**
     * Save match suggestion.
     * 
     * @param suggestion Match suggestion to save
     * @return Saved match suggestion
     */
    MatchSuggestion save(MatchSuggestion suggestion);
}
