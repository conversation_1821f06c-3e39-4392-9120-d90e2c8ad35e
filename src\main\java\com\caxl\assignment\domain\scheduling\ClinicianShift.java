package com.caxl.assignment.domain.scheduling;

import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Represents a clinician's scheduled shift for a specific day and time.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClinicianShift {
    public enum ShiftType {
        MORNING,
        AFTERNOON,
        EVENING
    }

    private String id;
    private LocalDate date;
    private ShiftType shiftType;
    private LocalTime startTime;
    private LocalTime endTime;
    private int maxPatients;
    private int maxWorkloadPoints;
    private String serviceArea;
    private List<Patient> patientVisits;
    private Clinician assignedClinician;
    private boolean active;
    
    // Additional fields could include location, shift type, etc.
}