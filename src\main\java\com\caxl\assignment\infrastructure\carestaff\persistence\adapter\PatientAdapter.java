package com.caxl.assignment.infrastructure.persistence.adapter;

import com.caxl.assignment.application.port.out.persistence.PatientPort;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKBReader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.sql.Array;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Persistence adapter for patient operations.
 * Implements the PatientPort driving port.
 * Uses existing PATIENT and LOCATION tables.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class PatientAdapter implements PatientPort {

    private final JdbcTemplate jdbcTemplate;
    private final WKBReader wkbReader = new WKBReader();

    @Override
    public Optional<PatientDomain> findByIdWithLocation(UUID patientId) {
        log.debug("Finding patient by ID with location: {}", patientId);

        String sql = """
            SELECT p.patient_id, p.enc_first_name, p.enc_last_name, 
                   l.coordinates, p.preferred_language,
                   p.preferred_carestaff_ids, p.barred_carestaff_ids,
                   p.medical_conditions, p.special_instructions
            FROM patient p
            LEFT JOIN location l ON p.location_id = l.location_id
            WHERE p.patient_id = ? AND p.is_active = true
            """;

        try {
            return jdbcTemplate.query(sql, rs -> {
                if (rs.next()) {
                    UUID id = (UUID) rs.getObject("patient_id");
                    String firstName = rs.getString("enc_first_name");
                    String lastName = rs.getString("enc_last_name");
                    String name = (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
                    
                    Point coordinates = null;
                    byte[] coordBytes = rs.getBytes("coordinates");
                    if (coordBytes != null) {
                        try {
                            coordinates = (Point) wkbReader.read(coordBytes);
                        } catch (Exception e) {
                            log.warn("Error parsing patient location coordinates: {}", e.getMessage());
                        }
                    }
                    
                    String preferredLanguage = rs.getString("preferred_language");
                    String medicalConditions = rs.getString("medical_conditions");
                    String specialInstructions = rs.getString("special_instructions");
                    
                    // Parse JSON arrays for preferred and barred staff IDs
                    List<UUID> preferredCareStaffIds = parseUuidArray(rs.getArray("preferred_carestaff_ids"));
                    List<UUID> barredCareStaffIds = parseUuidArray(rs.getArray("barred_carestaff_ids"));

                    return new PatientDomain(
                            id,
                            name.trim(),
                            coordinates,
                            preferredLanguage,
                            preferredCareStaffIds,
                            barredCareStaffIds,
                            medicalConditions,
                            specialInstructions
                    );
                }
                return null;
            }, patientId);
            
        } catch (Exception e) {
            log.error("Error finding patient by ID {}: {}", patientId, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Parse UUID array from database result.
     */
    private List<UUID> parseUuidArray(Array sqlArray) {
        if (sqlArray == null) {
            return List.of();
        }
        
        try {
            Object[] array = (Object[]) sqlArray.getArray();
            return Arrays.stream(array)
                    .filter(UUID.class::isInstance)
                    .map(UUID.class::cast)
                    .toList();
        } catch (Exception e) {
            log.warn("Error parsing UUID array: {}", e.getMessage());
            return List.of();
        }
    }
}
