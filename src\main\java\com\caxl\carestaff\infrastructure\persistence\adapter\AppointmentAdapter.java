package com.caxl.carestaff.infrastructure.persistence.adapter;

import com.caxl.carestaff.application.port.out.persistence.AppointmentPort;
import com.caxl.carestaff.domain.entities.Appointment;
import com.caxl.carestaff.infrastructure.persistence.entity.AppointmentEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Spring Data JPA repository for appointment entities.
 */
interface AppointmentRepository extends JpaRepository<AppointmentEntity, UUID> {
}

/**
 * Persistence adapter for appointment operations.
 * Implements the AppointmentPort driving port.
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class AppointmentAdapter implements AppointmentPort {

    private final AppointmentRepository repository;

    @Override
    public Appointment save(Appointment appointment) {
        log.debug("Saving appointment: {}", appointment.getAppointmentId());
        
        AppointmentEntity entity = mapToEntity(appointment);
        AppointmentEntity saved = repository.save(entity);
        return mapToDomain(saved);
    }

    @Override
    public List<Appointment> saveAll(List<Appointment> appointments) {
        log.debug("Saving {} appointments", appointments.size());
        
        List<AppointmentEntity> entities = appointments.stream()
                .map(this::mapToEntity)
                .toList();
        
        List<AppointmentEntity> saved = repository.saveAll(entities);
        
        return saved.stream()
                .map(this::mapToDomain)
                .toList();
    }

    /**
     * Map domain entity to JPA entity.
     */
    private AppointmentEntity mapToEntity(Appointment domain) {
        return AppointmentEntity.builder()
                .appointmentId(domain.getAppointmentId())
                .serviceRequestId(domain.getServiceRequestId())
                .careStaffId(domain.getCareStaffId())
                .patientId(domain.getPatientId())
                .scheduledStartTime(domain.getScheduledStartTime())
                .scheduledEndTime(domain.getScheduledEndTime())
                .actualStartTime(domain.getActualStartTime())
                .actualEndTime(domain.getActualEndTime())
                .status(domain.getStatus())
                .notes(domain.getNotes())
                .schedulerId(domain.getSchedulerId())
                .createdAt(domain.getCreatedAt())
                .updatedAt(domain.getUpdatedAt())
                .build();
    }

    /**
     * Map JPA entity to domain entity.
     */
    private Appointment mapToDomain(AppointmentEntity entity) {
        return Appointment.builder()
                .appointmentId(entity.getAppointmentId())
                .serviceRequestId(entity.getServiceRequestId())
                .careStaffId(entity.getCareStaffId())
                .patientId(entity.getPatientId())
                .scheduledStartTime(entity.getScheduledStartTime())
                .scheduledEndTime(entity.getScheduledEndTime())
                .actualStartTime(entity.getActualStartTime())
                .actualEndTime(entity.getActualEndTime())
                .status(entity.getStatus())
                .notes(entity.getNotes())
                .schedulerId(entity.getSchedulerId())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}
