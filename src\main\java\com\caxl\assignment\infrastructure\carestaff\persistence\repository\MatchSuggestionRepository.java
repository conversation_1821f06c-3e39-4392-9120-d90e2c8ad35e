package com.caxl.assignment.infrastructure.persistence.repository;

import com.caxl.assignment.infrastructure.persistence.entity.MatchSuggestionEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * Spring Data JPA repository for match suggestion entities.
 */
@Repository
public interface MatchSuggestionRepository extends JpaRepository<MatchSuggestionEntity, UUID> {

    /**
     * Find active match suggestions by service request ID.
     */
    @Query("SELECT ms FROM MatchSuggestionEntity ms WHERE ms.serviceRequestId = :serviceRequestId AND ms.isActive = true ORDER BY ms.score DESC")
    List<MatchSuggestionEntity> findByServiceRequestIdAndIsActiveTrue(@Param("serviceRequestId") UUID serviceRequestId);

    /**
     * Find match suggestions by service request ID (including inactive).
     */
    List<MatchSuggestionEntity> findByServiceRequestId(UUID serviceRequestId);

    /**
     * Find match suggestions by care staff ID.
     */
    List<MatchSuggestionEntity> findBySuggestedCareStaffId(UUID careStaffId);
}
