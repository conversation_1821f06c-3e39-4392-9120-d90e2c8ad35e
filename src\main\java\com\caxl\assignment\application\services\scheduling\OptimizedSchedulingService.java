package com.caxl.assignment.application.services.scheduling;

import ai.timefold.solver.core.api.solver.Solver;
import ai.timefold.solver.core.api.solver.SolverFactory;
import ai.timefold.solver.core.config.solver.SolverConfig;
import com.caxl.assignment.domain.constraints.scheduling.SchedulingConstraintProvider;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import com.caxl.assignment.domain.scheduling.PatientAssignment;
import com.caxl.assignment.domain.scheduling.SchedulingSolution;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Service for optimized patient-clinician scheduling using the new SchedulingConstraintProvider.
 * Demonstrates practical usage of the constraint provider with real scheduling scenarios.
 */
@Service
@Slf4j
public class OptimizedSchedulingService {

    private final SolverFactory<SchedulingSolution> solverFactory;

    public OptimizedSchedulingService() {
        this.solverFactory = createSolverFactory();
    }

    /**
     * Create and optimize a schedule for the given patients and clinicians.
     */
    public SchedulingSolution optimizeSchedule(List<Patient> patients, List<Clinician> clinicians, 
                                             LocalDate scheduleDate) {
        log.info("Starting schedule optimization for {} patients and {} clinicians on {}", 
                patients.size(), clinicians.size(), scheduleDate);

        // Create the problem
        SchedulingSolution problem = createProblem(patients, clinicians, scheduleDate);
        
        // Solve the problem
        Solver<SchedulingSolution> solver = solverFactory.buildSolver();
        SchedulingSolution solution = solver.solve(problem);
        
        log.info("Schedule optimization completed. Score: {}", solution.getScore());
        logSolutionSummary(solution);
        
        return solution;
    }

    /**
     * Create a scheduling problem from patients and clinicians.
     */
    private SchedulingSolution createProblem(List<Patient> patients, List<Clinician> clinicians, 
                                           LocalDate scheduleDate) {
        List<PatientAssignment> assignments = new ArrayList<>();
        List<PatientAssignment.TimeSlot> timeSlots = generateTimeSlots();
        
        // Create assignments for each patient
        for (Patient patient : patients) {
            PatientAssignment assignment = PatientAssignment.builder()
                .id(UUID.randomUUID().toString())
                .patient(patient)
                .assignmentDate(scheduleDate)
                .priority(patient.getVisitPriority())
                .geographicZone(determineGeographicZone(patient))
                .build();
            assignments.add(assignment);
        }
        
        return SchedulingSolution.builder()
            .assignments(assignments)
            .clinicians(clinicians)
            .timeSlots(timeSlots)
            .patients(patients)
            .schedulingPeriod(List.of(scheduleDate))
            .build();
    }

    /**
     * Generate standard time slots for scheduling.
     */
    private List<PatientAssignment.TimeSlot> generateTimeSlots() {
        List<PatientAssignment.TimeSlot> timeSlots = new ArrayList<>();
        
        // Morning slots (9:00 AM - 12:00 PM)
        for (int hour = 9; hour < 12; hour++) {
            timeSlots.add(createTimeSlot(
                "MORNING_" + hour,
                LocalTime.of(hour, 0),
                LocalTime.of(hour + 1, 0),
                "MORNING",
                3 // Capacity for 3 concurrent appointments
            ));
        }
        
        // Afternoon slots (1:00 PM - 5:00 PM)
        for (int hour = 13; hour < 17; hour++) {
            timeSlots.add(createTimeSlot(
                "AFTERNOON_" + hour,
                LocalTime.of(hour, 0),
                LocalTime.of(hour + 1, 0),
                "AFTERNOON",
                3
            ));
        }
        
        // Evening slots (5:00 PM - 7:00 PM)
        for (int hour = 17; hour < 19; hour++) {
            timeSlots.add(createTimeSlot(
                "EVENING_" + hour,
                LocalTime.of(hour, 0),
                LocalTime.of(hour + 1, 0),
                "EVENING",
                2 // Reduced capacity for evening
            ));
        }
        
        return timeSlots;
    }

    /**
     * Create a time slot with specified parameters.
     */
    private PatientAssignment.TimeSlot createTimeSlot(String id, LocalTime startTime, LocalTime endTime, 
                                                    String shiftType, int capacity) {
        return PatientAssignment.TimeSlot.builder()
            .id(id)
            .startTime(startTime)
            .endTime(endTime)
            .shiftType(shiftType)
            .capacity(capacity)
            .build();
    }

    /**
     * Determine geographic zone for a patient (simplified implementation).
     */
    private String determineGeographicZone(Patient patient) {
        // In a real implementation, this would use patient address/location
        // For demo purposes, use a simple hash-based assignment
        int hash = patient.getId().hashCode();
        String[] zones = {"NORTH", "SOUTH", "EAST", "WEST", "CENTRAL"};
        return zones[Math.abs(hash) % zones.length];
    }

    /**
     * Create solver factory with the new constraint provider.
     */
    private SolverFactory<SchedulingSolution> createSolverFactory() {
        SolverConfig solverConfig = new SolverConfig()
            .withSolutionClass(SchedulingSolution.class)
            .withEntityClasses(PatientAssignment.class)
            .withConstraintProviderClass(SchedulingConstraintProvider.class)
            .withTerminationSpentLimit(Duration.ofSeconds(30));
            
        return SolverFactory.create(solverConfig);
    }

    /**
     * Log a summary of the solution for analysis.
     */
    private void logSolutionSummary(SchedulingSolution solution) {
        int totalAssignments = solution.getAssignments().size();
        long completedAssignments = solution.getAssignments().stream()
            .mapToLong(assignment -> assignment.isFeasible() ? 1 : 0)
            .sum();
        
        log.info("Solution Summary:");
        log.info("- Total assignments: {}", totalAssignments);
        log.info("- Completed assignments: {}", completedAssignments);
        log.info("- Completion rate: {:.1f}%", 
                (double) completedAssignments / totalAssignments * 100);
        
        // Log clinician utilization
        solution.getClinicians().forEach(clinician -> {
            long assignmentCount = solution.getAssignments().stream()
                .mapToLong(assignment -> 
                    clinician.equals(assignment.getAssignedClinician()) ? 1 : 0)
                .sum();
            log.info("- Clinician {}: {} assignments", clinician.getName(), assignmentCount);
        });
    }

    /**
     * Validate that a solution meets all hard constraints.
     */
    public boolean validateSolution(SchedulingSolution solution) {
        if (solution.getScore() == null) {
            log.warn("Solution has no score - cannot validate");
            return false;
        }
        
        boolean isValid = solution.getScore().hardScore() >= 0;
        if (!isValid) {
            log.warn("Solution violates hard constraints. Hard score: {}", 
                    solution.getScore().hardScore());
        }
        
        return isValid;
    }

    /**
     * Get optimization statistics for monitoring.
     */
    public OptimizationStats getOptimizationStats(SchedulingSolution solution) {
        int totalAssignments = solution.getAssignments().size();
        long feasibleAssignments = solution.getAssignments().stream()
            .mapToLong(assignment -> assignment.isFeasible() ? 1 : 0)
            .sum();
        
        double utilizationRate = solution.getClinicians().stream()
            .mapToDouble(clinician -> {
                long assignments = solution.getAssignments().stream()
                    .mapToLong(assignment -> 
                        clinician.equals(assignment.getAssignedClinician()) ? 1 : 0)
                    .sum();
                return (double) assignments / clinician.getMaxDailyAppointments();
            })
            .average()
            .orElse(0.0);
        
        return OptimizationStats.builder()
            .totalAssignments(totalAssignments)
            .feasibleAssignments((int) feasibleAssignments)
            .completionRate((double) feasibleAssignments / totalAssignments)
            .averageUtilization(utilizationRate)
            .hardScore(solution.getScore() != null ? solution.getScore().hardScore() : 0)
            .softScore(solution.getScore() != null ? solution.getScore().softScore() : 0)
            .build();
    }

    /**
     * Statistics for optimization results.
     */
    public static class OptimizationStats {
        public final int totalAssignments;
        public final int feasibleAssignments;
        public final double completionRate;
        public final double averageUtilization;
        public final int hardScore;
        public final int softScore;

        private OptimizationStats(Builder builder) {
            this.totalAssignments = builder.totalAssignments;
            this.feasibleAssignments = builder.feasibleAssignments;
            this.completionRate = builder.completionRate;
            this.averageUtilization = builder.averageUtilization;
            this.hardScore = builder.hardScore;
            this.softScore = builder.softScore;
        }

        public static Builder builder() {
            return new Builder();
        }

        public static class Builder {
            private int totalAssignments;
            private int feasibleAssignments;
            private double completionRate;
            private double averageUtilization;
            private int hardScore;
            private int softScore;

            public Builder totalAssignments(int totalAssignments) {
                this.totalAssignments = totalAssignments;
                return this;
            }

            public Builder feasibleAssignments(int feasibleAssignments) {
                this.feasibleAssignments = feasibleAssignments;
                return this;
            }

            public Builder completionRate(double completionRate) {
                this.completionRate = completionRate;
                return this;
            }

            public Builder averageUtilization(double averageUtilization) {
                this.averageUtilization = averageUtilization;
                return this;
            }

            public Builder hardScore(int hardScore) {
                this.hardScore = hardScore;
                return this;
            }

            public Builder softScore(int softScore) {
                this.softScore = softScore;
                return this;
            }

            public OptimizationStats build() {
                return new OptimizationStats(this);
            }
        }
    }
}
