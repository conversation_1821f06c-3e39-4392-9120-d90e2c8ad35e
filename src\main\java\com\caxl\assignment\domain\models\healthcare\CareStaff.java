package com.caxl.assignment.domain.models.healthcare;

import com.caxl.assignment.domain.models.Clinician;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Extended CareStaff domain model for US homecare scheduling.
 * Extends the base Clinician with homecare-specific attributes and state compliance.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CareStaff extends Clinician {

    /**
     * US state-specific licensing information.
     */
    @Valid
    @NotNull(message = "State licensing information is required")
    @JsonProperty("state_licensing")
    private StateLicensing stateLicensing;

    /**
     * Care specializations for homecare services.
     */
    @Valid
    @NotNull(message = "Care specializations are required")
    @JsonProperty("care_specializations")
    private CareSpecializations careSpecializations;

    /**
     * Operating zones for geofencing-based assignment.
     */
    @Valid
    @NotNull(message = "Operating zones are required")
    @JsonProperty("operating_zones")
    private List<OperatingZone> operatingZones;

    /**
     * Working hours and overtime constraints.
     */
    @Valid
    @NotNull(message = "Working hours configuration is required")
    @JsonProperty("working_hours")
    private WorkingHours workingHours;

    /**
     * Background check and compliance status.
     */
    @Valid
    @NotNull(message = "Compliance status is required")
    @JsonProperty("compliance_status")
    private ComplianceStatus complianceStatus;

    /**
     * Vehicle information for home visits.
     */
    @Valid
    @JsonProperty("vehicle_info")
    private VehicleInfo vehicleInfo;

    /**
     * Emergency contact information.
     */
    @Valid
    @JsonProperty("emergency_contact")
    private EmergencyContact emergencyContact;

    /**
     * Currently assigned visits for this staff member.
     * Used during scheduling optimization.
     */
    @Builder.Default
    @JsonProperty("assigned_visits")
    private List<HomecareVisit> visits = new ArrayList<>();

    // === NESTED CLASSES FOR US HOMECARE SPECIFICS ===

    /**
     * US state-specific licensing information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StateLicensing {
        
        @NotBlank(message = "Primary state is required")
        @JsonProperty("primary_state")
        private String primaryState;

        @NotNull(message = "Licensed states list is required")
        @JsonProperty("licensed_states")
        private Set<String> licensedStates;

        @NotNull(message = "License numbers map is required")
        @JsonProperty("license_numbers")
        private Map<String, String> licenseNumbers; // state -> license number

        @NotNull(message = "License expiration dates are required")
        @JsonProperty("license_expiration_dates")
        private Map<String, LocalDate> licenseExpirationDates;

        @JsonProperty("medicaid_enrollment")
        private Map<String, Boolean> medicaidEnrollment; // state -> enrolled

        @JsonProperty("medicare_enrollment")
        @Builder.Default
        private boolean medicareEnrollment = false;

        @JsonProperty("compact_license")
        @Builder.Default
        private boolean compactLicense = false; // Nurse Licensure Compact
    }

    /**
     * Care specializations for different types of homecare services.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CareSpecializations {
        
        @JsonProperty("skilled_nursing")
        @Builder.Default
        private boolean skilledNursing = false;

        @JsonProperty("home_health_aide")
        @Builder.Default
        private boolean homeHealthAide = false;

        @JsonProperty("physical_therapy")
        @Builder.Default
        private boolean physicalTherapy = false;

        @JsonProperty("occupational_therapy")
        @Builder.Default
        private boolean occupationalTherapy = false;

        @JsonProperty("speech_therapy")
        @Builder.Default
        private boolean speechTherapy = false;

        @JsonProperty("medical_social_work")
        @Builder.Default
        private boolean medicalSocialWork = false;

        @JsonProperty("wound_care")
        @Builder.Default
        private boolean woundCare = false;

        @JsonProperty("iv_therapy")
        @Builder.Default
        private boolean ivTherapy = false;

        @JsonProperty("pediatric_care")
        @Builder.Default
        private boolean pediatricCare = false;

        @JsonProperty("geriatric_care")
        @Builder.Default
        private boolean geriatricCare = false;

        @JsonProperty("mental_health")
        @Builder.Default
        private boolean mentalHealth = false;

        @JsonProperty("hospice_care")
        @Builder.Default
        private boolean hospiceCare = false;

        @JsonProperty("chronic_disease_management")
        private List<String> chronicDiseaseManagement;
    }

    /**
     * Operating zones for geofencing-based assignment.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OperatingZone {
        
        @NotBlank(message = "Zone ID is required")
        @JsonProperty("zone_id")
        private String zoneId;

        @NotBlank(message = "Zone name is required")
        @JsonProperty("zone_name")
        private String zoneName;

        @NotNull(message = "Coverage areas are required")
        @JsonProperty("coverage_areas")
        private List<String> coverageAreas; // ZIP codes, counties, or regions

        @JsonProperty("zone_boundaries")
        private ZoneBoundaries zoneBoundaries;

        @Min(value = 1, message = "Priority must be at least 1")
        @Max(value = 10, message = "Priority must be at most 10")
        @JsonProperty("priority")
        @Builder.Default
        private int priority = 5;

        @Min(value = 0, message = "Maximum travel distance cannot be negative")
        @JsonProperty("max_travel_distance_miles")
        @Builder.Default
        private double maxTravelDistanceMiles = 25.0;

        @JsonProperty("rural_area")
        @Builder.Default
        private boolean ruralArea = false;
    }

    /**
     * Geographic boundaries for precise geofencing.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ZoneBoundaries {
        
        @JsonProperty("center_coordinates")
        private Map<String, Double> centerCoordinates; // lat, lng

        @JsonProperty("radius_miles")
        private double radiusMiles;

        @JsonProperty("polygon_coordinates")
        private List<Map<String, Double>> polygonCoordinates; // for complex shapes

        @JsonProperty("excluded_areas")
        private List<String> excludedAreas; // ZIP codes to exclude
    }

    /**
     * Working hours and overtime constraints.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WorkingHours {
        
        @Min(value = 1, message = "Max daily hours must be at least 1")
        @Max(value = 16, message = "Max daily hours cannot exceed 16")
        @JsonProperty("max_daily_hours")
        @Builder.Default
        private int maxDailyHours = 8;

        @Min(value = 1, message = "Max weekly hours must be at least 1")
        @Max(value = 84, message = "Max weekly hours cannot exceed 84")
        @JsonProperty("max_weekly_hours")
        @Builder.Default
        private int maxWeeklyHours = 40;

        @JsonProperty("overtime_eligible")
        @Builder.Default
        private boolean overtimeEligible = true;

        @Min(value = 0, message = "Overtime threshold cannot be negative")
        @JsonProperty("overtime_threshold_daily")
        @Builder.Default
        private int overtimeThresholdDaily = 8;

        @Min(value = 0, message = "Overtime threshold cannot be negative")
        @JsonProperty("overtime_threshold_weekly")
        @Builder.Default
        private int overtimeThresholdWeekly = 40;

        @JsonProperty("break_requirements")
        private BreakRequirements breakRequirements;

        @JsonProperty("on_call_availability")
        @Builder.Default
        private boolean onCallAvailability = false;

        @JsonProperty("weekend_availability")
        @Builder.Default
        private boolean weekendAvailability = false;

        @JsonProperty("holiday_availability")
        @Builder.Default
        private boolean holidayAvailability = false;
    }

    /**
     * Break requirements based on state regulations.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BreakRequirements {
        
        @JsonProperty("meal_break_required")
        @Builder.Default
        private boolean mealBreakRequired = true;

        @Min(value = 0, message = "Meal break duration cannot be negative")
        @JsonProperty("meal_break_duration_minutes")
        @Builder.Default
        private int mealBreakDurationMinutes = 30;

        @JsonProperty("rest_break_required")
        @Builder.Default
        private boolean restBreakRequired = true;

        @Min(value = 0, message = "Rest break duration cannot be negative")
        @JsonProperty("rest_break_duration_minutes")
        @Builder.Default
        private int restBreakDurationMinutes = 15;

        @Min(value = 1, message = "Hours before break must be at least 1")
        @JsonProperty("hours_before_break")
        @Builder.Default
        private int hoursBeforeBreak = 4;
    }

    /**
     * Compliance status for background checks and certifications.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComplianceStatus {
        
        @JsonProperty("background_check_status")
        @NotNull(message = "Background check status is required")
        private BackgroundCheckStatus backgroundCheckStatus;

        @JsonProperty("background_check_date")
        private LocalDate backgroundCheckDate;

        @JsonProperty("background_check_expiration")
        private LocalDate backgroundCheckExpiration;

        @JsonProperty("drug_test_status")
        private DrugTestStatus drugTestStatus;

        @JsonProperty("drug_test_date")
        private LocalDate drugTestDate;

        @JsonProperty("tb_test_status")
        private TbTestStatus tbTestStatus;

        @JsonProperty("tb_test_date")
        private LocalDate tbTestDate;

        @JsonProperty("immunization_records")
        private Map<String, LocalDate> immunizationRecords; // vaccine -> date

        @JsonProperty("cpr_certification_expiration")
        private LocalDate cprCertificationExpiration;

        @JsonProperty("first_aid_certification_expiration")
        private LocalDate firstAidCertificationExpiration;
    }

    /**
     * Vehicle information for home visits.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class VehicleInfo {
        
        @JsonProperty("has_reliable_transportation")
        @Builder.Default
        private boolean hasReliableTransportation = true;

        @JsonProperty("vehicle_type")
        private String vehicleType; // car, van, suv, etc.

        @JsonProperty("insurance_valid")
        @Builder.Default
        private boolean insuranceValid = true;

        @JsonProperty("insurance_expiration")
        private LocalDate insuranceExpiration;

        @JsonProperty("license_valid")
        @Builder.Default
        private boolean licenseValid = true;

        @JsonProperty("license_expiration")
        private LocalDate licenseExpiration;

        @JsonProperty("medical_equipment_capacity")
        @Builder.Default
        private boolean medicalEquipmentCapacity = false;
    }

    /**
     * Emergency contact information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EmergencyContact {
        
        @NotBlank(message = "Emergency contact name is required")
        @JsonProperty("name")
        private String name;

        @NotBlank(message = "Emergency contact phone is required")
        @JsonProperty("phone")
        private String phone;

        @JsonProperty("relationship")
        private String relationship;

        @JsonProperty("alternate_phone")
        private String alternatePhone;
    }

    // === ENUMS FOR STATUS TRACKING ===

    public enum BackgroundCheckStatus {
        PASSED, FAILED, PENDING, EXPIRED, NOT_REQUIRED
    }

    public enum DrugTestStatus {
        PASSED, FAILED, PENDING, EXPIRED, NOT_REQUIRED
    }

    public enum TbTestStatus {
        NEGATIVE, POSITIVE, PENDING, EXPIRED, NOT_REQUIRED
    }
}
