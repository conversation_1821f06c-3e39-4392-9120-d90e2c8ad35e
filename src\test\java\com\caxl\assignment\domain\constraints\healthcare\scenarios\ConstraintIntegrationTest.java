package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.models.healthcare.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Comprehensive integration test suite for combined constraint interactions.
 * Tests real-world scenarios where multiple constraints interact and compete.
 */
@DisplayName("Constraint Integration Tests")
class ConstraintIntegrationTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
            new EnhancedHomecareConstraintProvider(),
            HomecareSchedule.class,
            CareStaff.class,
            HomecareVisit.class
        );
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Hard Constraint Interactions")
    class HardConstraintInteractions {

        @Test
        @DisplayName("testIntegration_whenSkillAndAvailabilityConflict_shouldFailBoth")
        void testIntegration_whenSkillAndAvailabilityConflict_shouldFailBoth() {
            // Given: Staff without required skill AND unavailable at visit time
            CareStaff staff = testDataFactory.createCareStaffWithSkills(Arrays.asList("Basic Care"));
            staff.setAvailability(testDataFactory.createAvailabilityWindow(
                LocalDateTime.of(2025, 5, 28, 14, 0),
                LocalDateTime.of(2025, 5, 28, 18, 0)
            ));
            
            HomecareVisit visit = testDataFactory.createVisitRequiringSkills(Arrays.asList("Wound Care"));
            visit.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0)); // Outside availability
            visit.setEndTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            staff.setVisits(Arrays.asList(visit));

            // When: Checking multiple hard constraints
            // Then: Should fail both skill matching and availability constraints
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::skillMatchingConstraint)
                .given(staff, visit)
                .penalizesBy(100);
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::staffAvailabilityConstraint)
                .given(staff, visit)
                .penalizesBy(1000);
        }

        @Test
        @DisplayName("testIntegration_whenGeofenceAndLicenseViolation_shouldFailBoth")
        void testIntegration_whenGeofenceAndLicenseViolation_shouldFailBoth() {
            // Given: Staff outside geofence AND without required license
            CareStaff staff = testDataFactory.createCareStaffWithLicenses(Arrays.asList("RN"));
            staff.setAllowedGeofences(Arrays.asList("Zone A"));
            
            HomecareVisit visit = testDataFactory.createVisitInGeofence("Zone B"); // Outside allowed
            visit.setRequiredLicenses(Arrays.asList("LPN")); // Different license required
            
            staff.setVisits(Arrays.asList(visit));

            // When: Checking geofence and license constraints
            // Then: Should fail both constraints
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::geofencingConstraint)
                .given(staff, visit)
                .penalizesBy(500);
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::licenseComplianceConstraint)
                .given(staff, visit)
                .penalizesBy(200);
        }

        @Test
        @DisplayName("testIntegration_whenTimeConflictWithStateCompliance_shouldPrioritizeTimeConflict")
        void testIntegration_whenTimeConflictWithStateCompliance_shouldPrioritizeTimeConflict() {
            // Given: Time conflict with cross-state assignment
            CareStaff staff = testDataFactory.createCareStaffWithStateLicenses(Arrays.asList("NY", "NJ"));
            
            HomecareVisit visit1 = testDataFactory.createVisitInState("NY");
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            visit1.setEndTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitInState("NJ");
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 10, 0)); // Overlapping time
            visit2.setEndTime(LocalDateTime.of(2025, 5, 28, 12, 0));
            
            staff.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking time conflict (should fail) vs state compliance (should pass)
            // Then: Time conflict should be detected despite valid state licenses
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::timeConflictConstraint)
                .given(staff, visit1, visit2)
                .penalizesBy(10000); // Critical time conflict penalty
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::stateComplianceConstraint)
                .given(staff, visit1, visit2)
                .penalizesBy(0); // No state compliance violation
        }
    }

    @Nested
    @DisplayName("Soft Constraint Competitions")
    class SoftConstraintCompetitions {

        @Test
        @DisplayName("testIntegration_whenTravelTimeVsContinuity_shouldBalanceObjectives")
        void testIntegration_whenTravelTimeVsContinuity_shouldBalanceObjectives() {
            // Given: Choice between travel optimization and continuity of care
            CareStaff regularCaregiver = testDataFactory.createCareStaffWithId("REGULAR");
            CareStaff nearbyCaregiver = testDataFactory.createCareStaffWithId("NEARBY");
            Patient patient = testDataFactory.createPatientWithPreferredCaregiver("REGULAR");
            
            // Regular caregiver far away but provides continuity
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setPatient(testDataFactory.createPatientAtLocation(10.0, 20.0));
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setPatient(testDataFactory.createPatientAtLocation(15.0, 25.0)); // Far from visit1
            
            regularCaregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking travel time vs continuity trade-off
            // Then: Should balance travel penalty with continuity reward
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(regularCaregiver, visit1, visit2)
                .penalizesBy(50); // Travel time penalty
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(regularCaregiver, visit1, visit2)
                .rewardsWith(20); // Continuity reward (partially offsets travel penalty)
        }

        @Test
        @DisplayName("testIntegration_whenWorkloadVsPreference_shouldConsiderPriority")
        void testIntegration_whenWorkloadVsPreference_shouldConsiderPriority() {
            // Given: Workload balance vs patient preference conflict
            CareStaff overloadedStaff = testDataFactory.createCareStaffWithWorkload(8); // High workload
            CareStaff balancedStaff = testDataFactory.createCareStaffWithWorkload(4);   // Balanced workload
            Patient patient = testDataFactory.createPatientWithPreferredCaregiver(overloadedStaff.getId());
            
            HomecareVisit visit = testDataFactory.createVisitForPatient(patient);
            
            // Assigning to overloaded staff (preference) vs balanced staff (workload)
            overloadedStaff.setVisits(Arrays.asList(visit));

            // When: Checking workload balance vs patient preference
            // Then: Should show tension between objectives
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(overloadedStaff, visit)
                .penalizesBy(40); // Workload imbalance penalty
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(overloadedStaff, visit)
                .rewardsWith(20); // Preference reward
        }

        @Test
        @DisplayName("testIntegration_whenMultipleSoftConstraintsAlign_shouldProvideMaximumBenefit")
        void testIntegration_whenMultipleSoftConstraintsAlign_shouldProvideMaximumBenefit() {
            // Given: Scenario where multiple soft constraints align perfectly
            CareStaff idealCaregiver = testDataFactory.createCareStaffWithWorkload(3); // Balanced
            Patient patient = testDataFactory.createPatientWithPreferredCaregiver(idealCaregiver.getId());
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setPatient(testDataFactory.createPatientAtLocation(10.0, 20.0));
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setPatient(testDataFactory.createPatientAtLocation(10.1, 20.1)); // Very close
            visit2.setStartTime(LocalDateTime.of(2025, 5, 28, 11, 0));
            
            idealCaregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: All soft constraints align
            // Then: Should maximize combined benefits
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(idealCaregiver, visit1, visit2)
                .penalizesBy(1); // Minimal travel penalty
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(idealCaregiver, visit1, visit2)
                .rewardsWith(20); // Strong continuity reward
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(idealCaregiver, visit1, visit2)
                .penalizesBy(0); // No workload penalty
        }
    }

    @Nested
    @DisplayName("Real-World Scenario Simulations")
    class RealWorldScenarioSimulations {

        @Test
        @DisplayName("testIntegration_withEmergencyRescheduling_shouldPrioritizeUrgency")
        void testIntegration_withEmergencyRescheduling_shouldPrioritizeUrgency() {
            // Given: Emergency visit requiring immediate rescheduling
            CareStaff staff = testDataFactory.createBasicCareStaff();
            
            HomecareVisit regularVisit = testDataFactory.createRegularVisit();
            regularVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            HomecareVisit emergencyVisit = testDataFactory.createEmergencyVisit();
            emergencyVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 10, 30)); // Conflicts with regular
            emergencyVisit.setPriority(ServicePriority.EMERGENCY);
            
            staff.setVisits(Arrays.asList(regularVisit, emergencyVisit));

            // When: Emergency creates scheduling conflict
            // Then: Should show that emergency priority overrides normal scheduling
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::timeConflictConstraint)
                .given(staff, regularVisit, emergencyVisit)
                .penalizesBy(10000); // Time conflict detected
                
            // Emergency priority should be considered in workload balancing
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(staff, regularVisit, emergencyVisit)
                .penalizesBy(5); // Reduced penalty for emergency priority
        }

        @Test
        @DisplayName("testIntegration_withWeekendStaffing_shouldAdaptConstraints")
        void testIntegration_withWeekendStaffing_shouldAdaptConstraints() {
            // Given: Weekend staffing with limited availability
            CareStaff weekendStaff = testDataFactory.createWeekendStaff();
            weekendStaff.setAvailability(testDataFactory.createWeekendAvailability());
            
            HomecareVisit weekendVisit = testDataFactory.createWeekendVisit();
            weekendVisit.setStartTime(LocalDateTime.of(2025, 5, 31, 14, 0)); // Saturday
            
            weekendStaff.setVisits(Arrays.asList(weekendVisit));

            // When: Checking weekend staffing constraints
            // Then: Should handle weekend-specific constraint adaptations
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::staffAvailabilityConstraint)
                .given(weekendStaff, weekendVisit)
                .penalizesBy(0); // Should pass weekend availability
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(weekendStaff, weekendVisit)
                .penalizesBy(0); // Adjusted for weekend staffing patterns
        }

        @Test
        @DisplayName("testIntegration_withMultiDayScheduling_shouldMaintainConsistency")
        void testIntegration_withMultiDayScheduling_shouldMaintainConsistency() {
            // Given: Multi-day scheduling scenario
            CareStaff staff = testDataFactory.createFullTimeStaff();
            Patient chronicPatient = testDataFactory.createChronicCarePatient();
            
            HomecareVisit mondayVisit = testDataFactory.createVisitForPatient(chronicPatient);
            mondayVisit.setStartTime(LocalDateTime.of(2025, 5, 26, 9, 0)); // Monday
            
            HomecareVisit wednesdayVisit = testDataFactory.createVisitForPatient(chronicPatient);
            wednesdayVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0)); // Wednesday
            
            HomecareVisit fridayVisit = testDataFactory.createVisitForPatient(chronicPatient);
            fridayVisit.setStartTime(LocalDateTime.of(2025, 5, 30, 9, 0)); // Friday
            
            staff.setVisits(Arrays.asList(mondayVisit, wednesdayVisit, fridayVisit));

            // When: Checking consistency across multiple days
            // Then: Should maintain strong continuity with distributed workload
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(staff, mondayVisit, wednesdayVisit, fridayVisit)
                .rewardsWith(30); // Strong multi-day continuity
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(staff, mondayVisit, wednesdayVisit, fridayVisit)
                .penalizesBy(0); // Well-distributed workload
        }
    }

    @Nested
    @DisplayName("Constraint Priority Resolution")
    class ConstraintPriorityResolution {

        @Test
        @DisplayName("testIntegration_whenHardConstraintViolated_shouldIgnoreSoftOptimizations")
        void testIntegration_whenHardConstraintViolated_shouldIgnoreSoftOptimizations() {
            // Given: Hard constraint violation with good soft constraint performance
            CareStaff unlicensedStaff = testDataFactory.createCareStaffWithLicenses(Arrays.asList());
            Patient patient = testDataFactory.createPatientWithPreferredCaregiver(unlicensedStaff.getId());
            
            HomecareVisit visit1 = testDataFactory.createVisitRequiringLicense("RN");
            visit1.setPatient(testDataFactory.createPatientAtLocation(10.0, 20.0));
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setPatient(testDataFactory.createPatientAtLocation(10.1, 20.1)); // Close proximity
            
            unlicensedStaff.setVisits(Arrays.asList(visit1, visit2));

            // When: Hard constraint fails but soft constraints would be optimal
            // Then: Hard constraint violation should dominate
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::licenseComplianceConstraint)
                .given(unlicensedStaff, visit1)
                .penalizesBy(200); // Hard constraint violation
                
            // Soft constraints still evaluated but don't override hard constraint failure
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(unlicensedStaff, visit1, visit2)
                .penalizesBy(1); // Good travel optimization
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(unlicensedStaff, visit2)
                .rewardsWith(20); // Good continuity
        }

        @Test
        @DisplayName("testIntegration_withConstraintWeighting_shouldReflectBusinessPriorities")
        void testIntegration_withConstraintWeighting_shouldReflectBusinessPriorities() {
            // Given: Scenario testing business rule priority weighting
            CareStaff staff = testDataFactory.createBasicCareStaff();
            Patient vipPatient = testDataFactory.createVipPatient();
            Patient regularPatient = testDataFactory.createBasicPatient();
            
            HomecareVisit vipVisit = testDataFactory.createVipVisit(vipPatient);
            HomecareVisit regularVisit = testDataFactory.createVisitForPatient(regularPatient);
            
            staff.setVisits(Arrays.asList(vipVisit, regularVisit));

            // When: VIP patient constraints should have higher weight
            // Then: Should show business priority weighting in constraint evaluation
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(staff, vipVisit)
                .rewardsWith(50); // Higher reward for VIP continuity
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(staff, regularVisit)
                .rewardsWith(10); // Standard reward for regular patient
        }
    }

    @Nested
    @DisplayName("System Stress Testing")
    class SystemStressTesting {

        @Test
        @DisplayName("testIntegration_withHighVolumeScheduling_shouldMaintainPerformance")
        void testIntegration_withHighVolumeScheduling_shouldMaintainPerformance() {
            // Given: High volume scheduling scenario
            List<CareStaff> staffList = testDataFactory.createMultipleStaff(10);
            List<HomecareVisit> visitList = testDataFactory.createMultipleVisits(50);
            
            // Distribute visits among staff
            for (int i = 0; i < visitList.size(); i++) {
                CareStaff staff = staffList.get(i % staffList.size());
                HomecareVisit visit = visitList.get(i);
                staff.getVisits().add(visit);
            }

            // When: Processing high volume with multiple constraints
            // Then: Should handle volume without constraint evaluation failures
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::workloadBalanceConstraint)
                .given(staffList.toArray())
                .penalizesBy(250); // Reasonable penalty for volume distribution
        }

        @Test
        @DisplayName("testIntegration_withComplexGeographicDistribution_shouldOptimizeRouting")
        void testIntegration_withComplexGeographicDistribution_shouldOptimizeRouting() {
            // Given: Complex geographic distribution across multiple zones
            CareStaff mobileStaff = testDataFactory.createMobileStaff();
            
            HomecareVisit northVisit = testDataFactory.createVisitInZone("North");
            northVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 8, 0));
            
            HomecareVisit centralVisit = testDataFactory.createVisitInZone("Central");
            centralVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 10, 0));
            
            HomecareVisit southVisit = testDataFactory.createVisitInZone("South");
            southVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 14, 0));
            
            HomecareVisit eastVisit = testDataFactory.createVisitInZone("East");
            eastVisit.setStartTime(LocalDateTime.of(2025, 5, 28, 16, 0));
            
            mobileStaff.setVisits(Arrays.asList(northVisit, centralVisit, southVisit, eastVisit));

            // When: Optimizing complex geographic routing
            // Then: Should balance travel time with schedule constraints
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::minimizeTravelTime)
                .given(mobileStaff, northVisit, centralVisit, southVisit, eastVisit)
                .penalizesBy(120); // Reasonable penalty for complex routing
                
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::geofencingConstraint)
                .given(mobileStaff, northVisit, centralVisit, southVisit, eastVisit)
                .penalizesBy(0); // All visits within mobile staff's allowed zones
        }
    }
}
