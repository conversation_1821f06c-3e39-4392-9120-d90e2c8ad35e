package com.caxl.assignment.infrastructure.persistence.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * JPA entity for appointment storage.
 * Maps to APPOINTMENT table.
 */
@Entity
@Table(name = "appointment")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentEntity {

    @Id
    @Column(name = "appointment_id")
    private UUID appointmentId;

    @Column(name = "service_request_id")
    private UUID serviceRequestId;

    @Column(name = "carestaff_id", nullable = false)
    private UUID careStaffId;

    @Column(name = "patient_id", nullable = false)
    private UUID patientId;

    @Column(name = "scheduled_start_time")
    private LocalDateTime scheduledStartTime;

    @Column(name = "scheduled_end_time")
    private LocalDateTime scheduledEndTime;

    @Column(name = "actual_start_time")
    private LocalDateTime actualStartTime;

    @Column(name = "actual_end_time")
    private LocalDateTime actualEndTime;

    @Column(name = "status")
    @Builder.Default
    private String status = "scheduled";

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "scheduler_id")
    private UUID schedulerId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        if (appointmentId == null) {
            appointmentId = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
