package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Medical information for patients.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MedicalInformation {

    @Valid
    @JsonProperty("conditions")
    private List<String> conditions;

    @Valid
    @JsonProperty("allergies")
    private List<String> allergies;

    @Valid
    @JsonProperty("medications")
    private List<String> medications;

    @JsonProperty("mobility_level")
    private String mobilityLevel;

    @JsonProperty("cognitive_status")
    private String cognitiveStatus;

    @JsonProperty("emergency_medical_info")
    private String emergencyMedicalInfo;

    @JsonProperty("physician_contact")
    private String physicianContact;

    @JsonProperty("insurance_info")
    private String insuranceInfo;

    @JsonProperty("age")
    private Integer age;
}
