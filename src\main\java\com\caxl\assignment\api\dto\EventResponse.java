package com.caxl.assignment.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for scheduling event processing.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventResponse {
    private String eventId;
    private String status;
    private String message;
    private boolean processingStarted;
    private boolean requiresReoptimization;
    private Long estimatedCompletionTimeMs;
}
