# Domain Services - CareStaff Matching Service

## Overview

The CareStaff Matching Service implements sophisticated business logic through five comprehensive domain services. These services encapsulate complex business rules and provide clean interfaces for the application layer.

## Domain Services Architecture

```
Domain Services Layer
├── SkillExtractionService          # Intelligent skill derivation
├── ContinuityOfCareService         # Historical relationship analysis
├── AvailabilityWindowScoringService # Time optimization logic
├── ConflictDetectionService        # Comprehensive conflict analysis
└── AppointmentTimeCalculationService # Optimal scheduling
```

## 1. SkillExtractionService

### Purpose
Intelligently extracts required skills from patient conditions, visit types, and service request characteristics.

### Key Features

#### Medical Condition Mapping
Maps 15+ medical conditions to specific skill requirements:

```java
// Example mappings
"diabetes" → [Blood glucose monitoring, Insulin administration]
"wound_care" → [Sterile technique, Wound assessment]
"cardiac" → [Cardiac monitoring, Medication management]
"respiratory" → [Oxygen therapy, Respiratory assessment]
```

#### Visit Type Analysis
Supports 6+ specialized visit types:

- **routine_checkup**: Vital signs, Basic assessment
- **wound_care**: Sterile technique, Wound assessment, Dressing changes
- **medication_management**: Medication management, Reconciliation, Patient education
- **post_surgical**: Sterile technique, Post-op monitoring, Pain management
- **physical_therapy**: Physical therapy, Transfer techniques, Exercise therapy
- **mental_health**: Mental health assessment, Crisis intervention, Therapeutic communication

#### Priority-Based Escalation
- **Priority 1 (Critical)**: Advanced life support, Emergency response
- **Priority 2 (High)**: Specialized care, Complex procedures

#### Workload Complexity Assessment
- **High Workload (4+ points)**: Complex care management skills

### Usage Example

```java
@Autowired
private SkillExtractionService skillExtractionService;

List<UUID> requiredSkills = skillExtractionService.extractRequiredSkills(
    serviceRequest, patient);
```

## 2. ContinuityOfCareService

### Purpose
Calculates continuity scores based on historical care relationships and outcomes.

### Scoring Components

#### Preferred Staff Bonus (20 points)
- Explicit patient preference for specific carestaff

#### Recent Visit History (5 points per visit)
- Configurable timeframe (default: 30 days)
- Weighted by visit recency

#### Historical Relationship (up to 20 points)
- Long-term care relationships
- Total visit count consideration

#### Outcome-Based Scoring (variable)
- Bonus/penalty based on previous visit outcomes
- Analyzes appointment notes for success indicators

#### Care Plan Familiarity (up to 10 points)
- Experience with similar patient needs
- Visit type diversity bonus

#### Team Consistency (up to 5 points)
- Bonus for being part of consistent care team
- Smaller teams get higher scores

### Continuity Levels

```java
public enum ContinuityLevel {
    NONE(0.0),      // No previous relationship
    LOW(1.0),       // Limited history
    MODERATE(1.5),  // Some previous interaction
    HIGH(2.0),      // Strong relationship
    EXCELLENT(2.5)  // Preferred staff with recent visits
}
```

### Usage Example

```java
ContinuityScore score = continuityOfCareService.calculateContinuityScore(
    careStaffId, patientId, patient, 30); // Last 30 days

// Result includes score, level, visit counts, and rationale
```

## 3. AvailabilityWindowScoringService

### Purpose
Optimizes appointment scheduling based on staff availability, workload, and time preferences.

### Scoring Components

#### Buffer Time Analysis
- Scores based on time before/after appointments
- Configurable minimum buffer requirements
- Bonus for adequate spacing, penalty for tight scheduling

#### Preferred Time Alignment
- Staff preferred working hours consideration
- Bonus for appointments within preferred times
- Penalty based on distance from preferred hours

#### Workload Distribution
- Optimal 4-6 appointments per day
- Bonus for balanced workload
- Penalty for overloading or underutilization

#### Travel Time Optimization
- Efficient routing between appointments
- Bonus for appointments close to previous/next visits
- Penalty for excessive travel time

#### Shift Boundary Alignment
- Day shift (7:00-15:00): +5 points
- Evening shift (15:00-23:00): +3 points
- Night shift (23:00-7:00): -5 points

### Usage Example

```java
AvailabilityWindowScore score = availabilityWindowScoringService
    .calculateAvailabilityScore(careStaffId, serviceRequest, 
                               availabilityConfig, weights);
```

## 4. ConflictDetectionService

### Purpose
Comprehensive conflict detection for bulk assignment operations with multi-level severity assessment.

### Conflict Types

#### Staff Double-Booking (HIGH severity)
- Overlapping appointments for same staff member
- Real-time validation against existing schedule

#### Patient Double-Booking (MEDIUM severity)
- Multiple appointments too close together
- Minimum 2-hour spacing requirement

#### Existing Appointment Conflicts (HIGH severity)
- Conflicts with already scheduled appointments
- Database-level validation

#### Service Request Status (HIGH severity)
- Invalid status for assignment (non-pending requests)
- Business rule enforcement

#### Daily Capacity Limits (MEDIUM severity)
- Maximum 8 appointments per staff per day
- Workload balancing enforcement

#### Time Window Violations (HIGH severity)
- Appointments outside valid time windows
- Service request constraint validation

#### Business Rule Violations (LOW severity)
- Weekend restrictions for non-urgent cases
- Priority-based scheduling rules

### Conflict Resolution Strategy

```java
ConflictDetectionResult result = conflictDetectionService
    .detectConflicts(suggestions, serviceRequests);

// Process only valid (non-conflicted) suggestions
Set<UUID> validSuggestions = result.validSuggestionIds();
List<ConflictInfo> conflicts = result.conflicts();
```

## 5. AppointmentTimeCalculationService

### Purpose
Calculates optimal appointment times considering multiple factors and constraints.

### Optimization Process

#### 1. Optimal Start Time Determination
Priority order:
1. Preferred start time (if within window)
2. Suggested time (if within window)
3. Staff optimal time based on schedule
4. Default to arrival window start

#### 2. Duration Calculation
Base duration adjustments by visit type:
- **routine_checkup**: Minimum 45 minutes
- **wound_care**: Minimum 60 minutes
- **medication_management**: Minimum 30 minutes
- **post_surgical**: Minimum 90 minutes
- **physical_therapy**: Minimum 60 minutes
- **mental_health**: Minimum 50 minutes

Additional adjustments:
- High priority: +15 minutes
- Complex cases (4+ workload points): +20 minutes
- Special instructions: +10 minutes
- Maximum cap: 180 minutes

#### 3. Staff Schedule Optimization
- Travel time optimization between appointments
- Workload balancing across the day
- Break time avoidance (lunch, shift changes)

#### 4. Time Window Validation
- Ensures compliance with service request constraints
- Automatic corrections for violations
- Duration adjustments if needed

### Usage Example

```java
AppointmentTimeResult timeResult = appointmentTimeCalculationService
    .calculateOptimalAppointmentTime(serviceRequest, careStaffId, suggestedDateTime);

LocalDateTime optimalStart = timeResult.startTime();
LocalDateTime optimalEnd = timeResult.endTime();
String rationale = timeResult.rationale();
```

## Integration with Main Service

All domain services are integrated into the main `CareStaffMatchingService`:

```java
@Service
@RequiredArgsConstructor
public class CareStaffMatchingService {
    
    // Domain services injection
    private final SkillExtractionService skillExtractionService;
    private final ContinuityOfCareService continuityOfCareService;
    private final AvailabilityWindowScoringService availabilityWindowScoringService;
    private final ConflictDetectionService conflictDetectionService;
    private final AppointmentTimeCalculationService appointmentTimeCalculationService;
    
    // Service methods use domain services for business logic
}
```

## Performance Considerations

### Database Optimization
- Efficient queries with proper indexing
- Batch processing for bulk operations
- Spatial query optimization with PostGIS

### Caching Strategy
- Domain services are stateless and cache-friendly
- Historical data caching for continuity analysis
- Configuration caching for performance

### Scalability
- Services designed for horizontal scaling
- Stateless design enables load balancing
- Database connection pooling

## Testing Strategy

### Unit Testing
- Individual domain service testing
- Mock dependencies for isolation
- Business rule validation

### Integration Testing
- End-to-end workflow testing
- Database integration validation
- Performance testing under load

## Future Enhancements

### Machine Learning Integration
- Historical data collection for predictive modeling
- Pattern recognition for optimal matching
- Continuous learning from outcomes

### Real-time Updates
- Event-driven architecture for live updates
- WebSocket integration for real-time notifications
- Stream processing for high-volume scenarios

### Advanced Analytics
- Comprehensive metrics collection
- Performance dashboards
- Predictive analytics for capacity planning

These domain services provide the foundation for sophisticated, configurable, and highly optimized carestaff matching while maintaining clean architecture and extensibility.
