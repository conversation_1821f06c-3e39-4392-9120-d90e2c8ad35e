# CareStaff Matching Service - Architecture Guide

## Overview

The CareStaff Matching Service is a configurable, geo-filtered & constraint-aware carestaff matching system built using hexagonal architecture with programmed heuristics. It intelligently matches available carestaff to incoming service requests for home-based healthcare based on configurable hard and soft constraints.

## Architecture

### Hexagonal Architecture (Ports and Adapters)

The system follows strict hexagonal architecture principles:

```
src/main/java/com/caxl/carestaff/
├── domain/                     # Domain Layer (Framework-agnostic)
│   ├── entities/              # Domain Entities
│   ├── valueobjects/          # Value Objects (MatchingCriteria)
│   ├── services/              # Domain Services
│   └── exceptions/            # Custom Domain Exceptions
├── application/               # Application Layer
│   ├── port/
│   │   ├── in/               # Driven Ports (Use Case Interfaces)
│   │   └── out/              # Driving Ports
│   │       └── persistence/  # Persistence Ports
│   └── service/              # Use Case Implementations
└── infrastructure/           # Infrastructure Layer
    ├── api/                  # Spring Web Adapter
    │   ├── dto/             # Request/Response DTOs
    │   └── exceptionhandler/ # Global Exception Handler
    └── persistence/         # JPA/Hibernate Adapter
        ├── entity/          # JPA Entities
        ├── repository/      # Spring Data JPA Repositories
        └── adapter/         # Port Implementations
```

## Core Features

### 1. Configurable Constraint System

The matching algorithm uses JSON-based configuration stored in the database:

- **Hard Constraints (Filters)**: Must be satisfied for a match to be considered
  - ✅ Sophisticated skill matching (all vs any requirements)
  - ✅ Geographic service area compliance with PostGIS
  - ✅ Comprehensive availability checking with overlap detection
  - ✅ Barred staff filtering with patient preferences
  - ✅ Experience level requirements for high-priority cases
  - ✅ Required certifications validation
  - ✅ Minimum score thresholds

- **Soft Constraints (Scoring Weights)**: Used for optimization and ranking
  - ✅ Multi-factor skill match bonuses
  - ✅ Distance-based proximity penalties
  - ✅ Preferred staff relationship bonuses
  - ✅ Continuity of care scoring with historical analysis
  - ✅ Language matching bonuses
  - ✅ Experience level bonuses with years calculation
  - ✅ Availability window fit optimization
  - ✅ Workload distribution balancing

### 2. Spatial/Geographic Filtering

- Uses PostGIS and Hibernate Spatial for geographic operations
- ST_DWithin for proximity searches
- ST_Contains/ST_Intersects for geofence validation
- Configurable search radius and service area types

### 3. Advanced Heuristic Matching Algorithm

The service implements a sophisticated multi-stage heuristic algorithm:

1. **Intelligent Skill Extraction**: 
   - ✅ Medical condition to skill mapping (15+ conditions)
   - ✅ Visit type analysis (6+ specialized types)
   - ✅ Priority-based skill escalation
   - ✅ Workload complexity assessment

2. **Spatial Candidate Filtering**:
   - ✅ PostGIS proximity searches with configurable radius
   - ✅ Geofence boundary validation
   - ✅ Service area compliance checking

3. **Comprehensive Hard Constraint Validation**:
   - ✅ Multi-factor availability analysis
   - ✅ Experience and certification requirements
   - ✅ Patient preference and barred staff filtering

4. **Advanced Soft Constraint Scoring**:
   - ✅ 13-factor scoring algorithm with configurable weights
   - ✅ Continuity of care analysis with historical data
   - ✅ Availability window fit optimization
   - ✅ Travel time and workload balancing

5. **Conflict Detection & Resolution**:
   - ✅ Real-time conflict detection for bulk operations
   - ✅ Multi-level conflict severity assessment
   - ✅ Automatic conflict resolution strategies

## Technology Stack

### Core Technologies
- **Language**: Java 17+
- **Framework**: Spring Boot 3.5.0+
- **Persistence**: JPA (Jakarta Persistence API) with Hibernate Spatial
- **Database**: PostgreSQL with PostGIS extension
- **JSON Processing**: Jackson (standard with Spring Boot)
- **Logging**: SLF4j (standard Spring Boot integration)
- **Geometry Types**: JTS (Java Topology Suite)

### Dependencies
- **Hibernate Spatial**: For PostGIS integration
- **Hypersistence Utils**: For JSONB mapping
- **Lombok**: For boilerplate reduction
- **Spring Boot Test**: For comprehensive testing

## Domain Services

The system includes five comprehensive domain services:

1. **SkillExtractionService**: Intelligent skill derivation from patient conditions and visit types
2. **ContinuityOfCareService**: Historical relationship analysis and scoring
3. **AvailabilityWindowScoringService**: Time optimization and scheduling efficiency
4. **ConflictDetectionService**: Comprehensive conflict detection for bulk operations
5. **AppointmentTimeCalculationService**: Optimal appointment time calculation

## Integration Points

### Existing System Integration
- Extends existing CAXL assignment system
- Reuses existing domain models (Patient, Clinician, CareStaff)
- Leverages existing database schema and spatial infrastructure
- Maintains compatibility with existing Spring Boot configuration

### Future Integration Ready
- **Timefold Integration**: Planning entities and constraints are solver-ready
- **Machine Learning**: Historical data collection for predictive matching
- **Real-time Updates**: Event-driven architecture for live scheduling
- **Mobile Integration**: API-first design supports mobile applications

## Quality Attributes

### Performance
- Efficient spatial queries with PostGIS
- Optimized database operations with proper indexing
- Batch processing for bulk operations
- Caching-friendly design patterns

### Scalability
- Hexagonal architecture supports horizontal scaling
- Stateless service design
- Database-driven configuration for multi-tenancy
- Event-driven patterns for real-time updates

### Maintainability
- Clean separation of concerns
- Comprehensive test coverage
- Extensive documentation
- Zero code duplication

### Reliability
- Comprehensive error handling
- Transaction management
- Conflict detection and prevention
- Graceful degradation strategies

## Security Considerations

- Input validation at API boundaries
- SQL injection prevention through parameterized queries
- Encrypted sensitive data handling (PII fields)
- Audit trail for all matching decisions

## Monitoring and Observability

- Comprehensive logging with SLF4j
- Performance metrics collection
- Health check endpoints
- Error tracking and alerting

This architecture provides a robust, scalable, and maintainable foundation for the carestaff matching service while maintaining clean separation of concerns and supporting future enhancements.
