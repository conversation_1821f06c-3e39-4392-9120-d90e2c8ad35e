# API Reference - CareStaff Matching Service

## Overview

The CareStaff Matching Service provides RESTful APIs for intelligent carestaff-to-patient matching based on configurable constraints. All endpoints return JSON responses with consistent error handling.

## Base URL

```
http://localhost:8080/api/v1
```

## Authentication

Currently, the service operates without authentication. In production, implement appropriate authentication mechanisms.

## API Endpoints

### 1. Suggest Matches

Generate match suggestions for a service request using configurable constraints.

**Endpoint:** `POST /service-requests/{request_id}/suggest-matches`

**Parameters:**
- `request_id` (path, required): UUID of the service request

**Response:**
```json
{
  "success": true,
  "message": "Generated 5 match suggestions",
  "data": [
    {
      "suggestion_id": "550e8400-e29b-41d4-a716-************",
      "service_request_id": "550e8400-e29b-41d4-a716-************",
      "suggested_carestaff_id": "550e8400-e29b-41d4-a716-************",
      "score": 85.5,
      "rationale": "Score breakdown: Skills(+30) Proximity(-5) Geo(+20) Preferred(+15) Language(+8) Experience(+17.5) = 85.5",
      "suggested_datetime": "2024-01-15T09:00:00",
      "created_at": "2024-01-14T10:30:00",
      "is_active": true
    }
  ],
  "timestamp": "2024-01-14T10:30:00"
}
```

**Error Responses:**
- `404 Not Found`: Service request not found
- `500 Internal Server Error`: No active matching configuration
- `400 Bad Request`: Invalid request parameters

**Example:**
```bash
curl -X POST http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-************/suggest-matches
```

### 2. Override Match

Override match suggestion with manual carestaff selection.

**Endpoint:** `POST /service-requests/{request_id}/override-match`

**Parameters:**
- `request_id` (path, required): UUID of the service request

**Request Body:**
```json
{
  "selected_carestaff_id": "550e8400-e29b-41d4-a716-************",
  "reason": "Patient specifically requested this caregiver",
  "explanation": "Patient has established relationship and comfort level with this staff member"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Match successfully overridden",
  "data": true,
  "timestamp": "2024-01-14T10:35:00"
}
```

**Error Responses:**
- `404 Not Found`: Service request or carestaff not found
- `400 Bad Request`: Invalid request status or missing required fields
- `409 Conflict`: Carestaff unavailable during requested time

**Example:**
```bash
curl -X POST http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-************/override-match \
  -H "Content-Type: application/json" \
  -d '{
    "selected_carestaff_id": "550e8400-e29b-41d4-a716-************",
    "reason": "Patient request",
    "explanation": "Patient specifically requested this caregiver"
  }'
```

### 3. Bulk Assignment

Assign multiple match suggestions in bulk with conflict detection.

**Endpoint:** `POST /matches/bulk-assign`

**Request Body:**
```json
{
  "suggestion_ids": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-446655440005"
  ],
  "scheduler_id": "550e8400-e29b-41d4-a716-446655440006"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk assignment completed: 2 successful, 1 failed",
  "data": {
    "total_requested": 3,
    "successful_assignments": 2,
    "failed_assignments": 1,
    "errors": [
      "[HIGH] Staff 550e8400-e29b-41d4-a716-446655440007 has overlapping appointments (Suggestions: [550e8400-e29b-41d4-a716-446655440005])"
    ],
    "success": false
  },
  "timestamp": "2024-01-14T10:40:00"
}
```

**Error Responses:**
- `400 Bad Request`: Empty suggestion list or validation errors
- `500 Internal Server Error`: System error during bulk processing

**Example:**
```bash
curl -X POST http://localhost:8080/api/v1/matches/bulk-assign \
  -H "Content-Type: application/json" \
  -d '{
    "suggestion_ids": [
      "550e8400-e29b-41d4-a716-************",
      "550e8400-e29b-41d4-a716-************"
    ]
  }'
```

### 4. Retrieve Suggestions

Get existing match suggestions for a service request.

**Endpoint:** `GET /service-requests/{request_id}/suggestions`

**Parameters:**
- `request_id` (path, required): UUID of the service request

**Response:**
```json
{
  "success": true,
  "message": "Found 3 existing suggestions",
  "data": [
    {
      "suggestion_id": "550e8400-e29b-41d4-a716-************",
      "service_request_id": "550e8400-e29b-41d4-a716-************",
      "suggested_carestaff_id": "550e8400-e29b-41d4-a716-************",
      "score": 85.5,
      "rationale": "Score breakdown: Skills(+30) Proximity(-5) Geo(+20) Preferred(+15) Language(+8) Experience(+17.5) = 85.5",
      "suggested_datetime": "2024-01-15T09:00:00",
      "created_at": "2024-01-14T10:30:00",
      "is_active": true
    }
  ],
  "timestamp": "2024-01-14T10:45:00"
}
```

**Error Responses:**
- `404 Not Found`: Service request not found
- `500 Internal Server Error`: System error

**Example:**
```bash
curl -X GET http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-************/suggestions
```

## Response Format

All API responses follow a consistent format:

```json
{
  "success": boolean,
  "message": "string",
  "data": object | array | primitive,
  "timestamp": "ISO 8601 datetime"
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "data": null,
  "timestamp": "2024-01-14T10:50:00"
}
```

### Validation Error Format

```json
{
  "success": false,
  "message": "Validation failed",
  "data": {
    "selected_carestaff_id": "Selected care staff ID is required",
    "reason": "Reason cannot be empty"
  },
  "timestamp": "2024-01-14T10:50:00"
}
```

### HTTP Status Codes

- `200 OK`: Successful operation
- `400 Bad Request`: Invalid request data or validation errors
- `404 Not Found`: Resource not found
- `409 Conflict`: Scheduling conflicts or business rule violations
- `500 Internal Server Error`: System errors or configuration issues

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing appropriate rate limiting based on your requirements.

## Pagination

For endpoints that may return large datasets, pagination will be implemented in future versions using standard `page` and `size` parameters.

## Filtering and Sorting

Future versions will support filtering and sorting parameters for list endpoints:

- `sort`: Sort field and direction (e.g., `score,desc`)
- `filter`: Filter criteria (e.g., `score>50`)
- `active`: Filter by active status

## Webhooks

Future versions will support webhook notifications for:
- Match suggestions generated
- Assignments completed
- Conflicts detected
- Status changes

## SDK and Client Libraries

Client libraries for popular programming languages will be provided in future releases:
- Java SDK
- Python SDK
- JavaScript/TypeScript SDK
- C# SDK

## Testing

Use the provided test endpoints for development and testing:

```bash
# Health check
curl http://localhost:8080/actuator/health

# Application info
curl http://localhost:8080/actuator/info
```

## Support

For API support and questions:
1. Check the troubleshooting guide
2. Review the configuration documentation
3. Create an issue with request/response details
