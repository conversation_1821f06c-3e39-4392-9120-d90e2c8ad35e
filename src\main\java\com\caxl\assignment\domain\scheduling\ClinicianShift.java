package com.caxl.assignment.domain.scheduling;

import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.Patient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * Represents a clinician's scheduled shift for a specific day and time.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClinicianShift {
    public enum ShiftType {
        MORNING,
        AFTERNOON,
        EVENING
    }

    private String id;
    private LocalDate date;
    private ShiftType shiftType;
    private LocalTime startTime;
    private LocalTime endTime;
    private int maxPatients;
    private int maxWorkloadPoints;
    private String serviceArea;
    private List<Patient> patientVisits;
    private Clinician assignedClinician;
    private boolean active;

    // Additional fields could include location, shift type, etc.

    /**
     * Check if this shift has any patients assigned.
     */
    public boolean hasPatients() {
        return patientVisits != null && !patientVisits.isEmpty();
    }

    /**
     * Get the number of patients assigned to this shift.
     */
    public int getPatientCount() {
        return patientVisits != null ? patientVisits.size() : 0;
    }

    /**
     * Get the current workload for this shift.
     */
    public int getCurrentWorkload() {
        if (patientVisits == null) return 0;
        return patientVisits.stream()
                .mapToInt(patient -> patient.getWorkloadPoints())
                .sum();
    }

    /**
     * Get the shift date.
     */
    public LocalDate getShiftDate() {
        return date;
    }

    /**
     * Get total travel time for this shift.
     */
    public Duration getTotalTravelTime() {
        // Simplified calculation - in real implementation would use actual routing
        if (patientVisits == null || patientVisits.size() <= 1) {
            return Duration.ZERO;
        }
        // Estimate 15 minutes travel time between each patient
        return Duration.ofMinutes((patientVisits.size() - 1) * 15L);
    }
}
