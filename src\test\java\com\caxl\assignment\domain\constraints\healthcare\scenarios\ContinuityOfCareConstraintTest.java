package com.caxl.assignment.domain.constraints.healthcare.scenarios;

import ai.timefold.solver.test.api.score.stream.ConstraintVerifier;
import com.caxl.assignment.domain.constraints.healthcare.EnhancedHomecareConstraintProvider;
import com.caxl.assignment.domain.models.healthcare.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * Comprehensive test suite for continuity of care constraints.
 * Tests soft constraints that prefer assigning the same caregiver to patients over time.
 */
@DisplayName("Continuity of Care Constraint Tests")
class ContinuityOfCareConstraintTest {

    private ConstraintVerifier<EnhancedHomecareConstraintProvider, HomecareSchedule> constraintVerifier;
    private TestDataFactory testDataFactory;

    @BeforeEach
    void setUp() {
        constraintVerifier = ConstraintVerifier.build(
            new EnhancedHomecareConstraintProvider(),
            HomecareSchedule.class,
            CareStaff.class,
            HomecareVisit.class
        );
        testDataFactory = new TestDataFactory();
    }

    @Nested
    @DisplayName("Basic Continuity Preferences")
    class BasicContinuityPreferences {

        @Test
        @DisplayName("testContinuityOfCare_whenSameCaregiverAssigned_shouldReward")
        void testContinuityOfCare_whenSameCaregiverAssigned_shouldReward() {
            // Given: Same caregiver assigned to multiple visits for same patient
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient patient = testDataFactory.createBasicPatient();
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setStartTime(LocalDateTime.of(2025, 5, 29, 9, 0)); // Next day
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity of care
            // Then: Should reward same caregiver assignment
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(10); // Positive reward for continuity
        }

        @Test
        @DisplayName("testContinuityOfCare_whenDifferentCaregiversAssigned_shouldNotReward")
        void testContinuityOfCare_whenDifferentCaregiversAssigned_shouldNotReward() {
            // Given: Different caregivers assigned to same patient
            CareStaff caregiver1 = testDataFactory.createCareStaffWithId("STAFF1");
            CareStaff caregiver2 = testDataFactory.createCareStaffWithId("STAFF2");
            Patient patient = testDataFactory.createBasicPatient();
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            
            caregiver1.setVisits(Arrays.asList(visit1));
            caregiver2.setVisits(Arrays.asList(visit2));

            // When: Checking continuity of care with different caregivers
            // Then: Should not provide continuity reward
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver1, visit1, caregiver2, visit2)
                .rewardsWith(0); // No reward for different caregivers
        }

        @Test
        @DisplayName("testContinuityOfCare_withPatientPreference_shouldPrioritize")
        void testContinuityOfCare_withPatientPreference_shouldPrioritize() {
            // Given: Patient with specific caregiver preference
            CareStaff preferredCaregiver = testDataFactory.createCareStaffWithId("PREFERRED");
            Patient patient = testDataFactory.createPatientWithPreferredCaregiver(preferredCaregiver.getId());
            
            HomecareVisit visit = testDataFactory.createVisitForPatient(patient);
            preferredCaregiver.setVisits(Arrays.asList(visit));

            // When: Checking continuity with preferred caregiver
            // Then: Should provide enhanced reward for preference match
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(preferredCaregiver, visit)
                .rewardsWith(20); // Enhanced reward for preference match
        }
    }

    @Nested
    @DisplayName("Long-term Continuity Patterns")
    class LongTermContinuityPatterns {

        @Test
        @DisplayName("testContinuityOfCare_withWeeklyPattern_shouldRewardConsistency")
        void testContinuityOfCare_withWeeklyPattern_shouldRewardConsistency() {
            // Given: Same caregiver assigned for weekly recurring visits
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient patient = testDataFactory.createBasicPatient();
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0)); // Week 1
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setStartTime(LocalDateTime.of(2025, 6, 4, 9, 0)); // Week 2
            
            HomecareVisit visit3 = testDataFactory.createVisitForPatient(patient);
            visit3.setStartTime(LocalDateTime.of(2025, 6, 11, 9, 0)); // Week 3
            
            caregiver.setVisits(Arrays.asList(visit1, visit2, visit3));

            // When: Checking weekly continuity pattern
            // Then: Should provide strong reward for consistent weekly pattern
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2, visit3)
                .rewardsWith(30); // High reward for long-term consistency
        }

        @Test
        @DisplayName("testContinuityOfCare_withInterruption_shouldReduceReward")
        void testContinuityOfCare_withInterruption_shouldReduceReward() {
            // Given: Continuity interrupted by different caregiver
            CareStaff caregiver1 = testDataFactory.createCareStaffWithId("STAFF1");
            CareStaff caregiver2 = testDataFactory.createCareStaffWithId("STAFF2");
            Patient patient = testDataFactory.createBasicPatient();
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setStartTime(LocalDateTime.of(2025, 5, 28, 9, 0));
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setStartTime(LocalDateTime.of(2025, 5, 29, 9, 0)); // Different caregiver
            
            HomecareVisit visit3 = testDataFactory.createVisitForPatient(patient);
            visit3.setStartTime(LocalDateTime.of(2025, 5, 30, 9, 0)); // Back to original
            
            caregiver1.setVisits(Arrays.asList(visit1, visit3));
            caregiver2.setVisits(Arrays.asList(visit2));

            // When: Checking continuity with interruption
            // Then: Should have reduced reward due to interruption
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver1, visit1, visit3, caregiver2, visit2)
                .rewardsWith(5); // Reduced reward due to interruption
        }

        @Test
        @DisplayName("testContinuityOfCare_withSeasonalPatterns_shouldAdaptRewards")
        void testContinuityOfCare_withSeasonalPatterns_shouldAdaptRewards() {
            // Given: Long-term care spanning multiple months
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient chronicPatient = testDataFactory.createChronicCarePatient();
            
            HomecareVisit springVisit = testDataFactory.createVisitForPatient(chronicPatient);
            springVisit.setStartTime(LocalDateTime.of(2025, 3, 15, 9, 0));
            
            HomecareVisit summerVisit = testDataFactory.createVisitForPatient(chronicPatient);
            summerVisit.setStartTime(LocalDateTime.of(2025, 6, 15, 9, 0));
            
            HomecareVisit fallVisit = testDataFactory.createVisitForPatient(chronicPatient);
            fallVisit.setStartTime(LocalDateTime.of(2025, 9, 15, 9, 0));
            
            caregiver.setVisits(Arrays.asList(springVisit, summerVisit, fallVisit));

            // When: Checking seasonal continuity patterns
            // Then: Should provide maximum reward for long-term continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, springVisit, summerVisit, fallVisit)
                .rewardsWith(50); // Maximum reward for seasonal continuity
        }
    }

    @Nested
    @DisplayName("Care Type Specific Continuity")
    class CareTypeSpecificContinuity {

        @Test
        @DisplayName("testContinuityOfCare_forChronicCare_shouldHaveHigherReward")
        void testContinuityOfCare_forChronicCare_shouldHaveHigherReward() {
            // Given: Chronic care visits requiring high continuity
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient chronicPatient = testDataFactory.createChronicCarePatient();
            
            HomecareVisit visit1 = testDataFactory.createChronicCareVisit(chronicPatient);
            HomecareVisit visit2 = testDataFactory.createChronicCareVisit(chronicPatient);
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity for chronic care
            // Then: Should have higher reward for chronic care continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(25); // Higher reward for chronic care
        }

        @Test
        @DisplayName("testContinuityOfCare_forAcuteCare_shouldHaveStandardReward")
        void testContinuityOfCare_forAcuteCare_shouldHaveStandardReward() {
            // Given: Acute care visits with standard continuity needs
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient acutePatient = testDataFactory.createAcuteCarePatient();
            
            HomecareVisit visit1 = testDataFactory.createAcuteCareVisit(acutePatient);
            HomecareVisit visit2 = testDataFactory.createAcuteCareVisit(acutePatient);
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity for acute care
            // Then: Should have standard reward for acute care continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(10); // Standard reward for acute care
        }

        @Test
        @DisplayName("testContinuityOfCare_forPediatricCare_shouldPrioritizeStability")
        void testContinuityOfCare_forPediatricCare_shouldPrioritizeStability() {
            // Given: Pediatric care visits requiring stable caregiver relationships
            CareStaff caregiver = testDataFactory.createPediatricSpecialist();
            Patient pediatricPatient = testDataFactory.createPediatricPatient();
            
            HomecareVisit visit1 = testDataFactory.createPediatricCareVisit(pediatricPatient);
            HomecareVisit visit2 = testDataFactory.createPediatricCareVisit(pediatricPatient);
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity for pediatric care
            // Then: Should prioritize stability with enhanced reward
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(35); // Enhanced reward for pediatric stability
        }
    }

    @Nested
    @DisplayName("Family and Caregiver Relationships")
    class FamilyAndCaregiverRelationships {

        @Test
        @DisplayName("testContinuityOfCare_withFamilyMembers_shouldGroupAssignments")
        void testContinuityOfCare_withFamilyMembers_shouldGroupAssignments() {
            // Given: Family members preferring same caregiver
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient spouse1 = testDataFactory.createPatientWithId("SPOUSE1");
            Patient spouse2 = testDataFactory.createPatientWithId("SPOUSE2");
            testDataFactory.setFamilyRelationship(spouse1, spouse2);
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(spouse1);
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(spouse2);
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity for family members
            // Then: Should reward family continuity grouping
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(15); // Reward for family continuity
        }

        @Test
        @DisplayName("testContinuityOfCare_withCaregiverSpecialization_shouldMatchExpertise")
        void testContinuityOfCare_withCaregiverSpecialization_shouldMatchExpertise() {
            // Given: Caregiver with specific expertise and matching patient needs
            CareStaff diabetesSpecialist = testDataFactory.createDiabetesSpecialist();
            Patient diabeticPatient = testDataFactory.createDiabeticPatient();
            
            HomecareVisit visit1 = testDataFactory.createDiabetesCareVisit(diabeticPatient);
            HomecareVisit visit2 = testDataFactory.createDiabetesCareVisit(diabeticPatient);
            
            diabetesSpecialist.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity with specialized expertise
            // Then: Should provide enhanced reward for expertise continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(diabetesSpecialist, visit1, visit2)
                .rewardsWith(30); // Enhanced reward for specialized continuity
        }

        @Test
        @DisplayName("testContinuityOfCare_withCulturalPreferences_shouldRespectValues")
        void testContinuityOfCare_withCulturalPreferences_shouldRespectValues() {
            // Given: Patient with cultural preferences and matching caregiver
            CareStaff culturallyMatchedCaregiver = testDataFactory.createCareStaffWithCulturalBackground("Spanish");
            Patient patientWithCulturalNeeds = testDataFactory.createPatientWithCulturalPreference("Spanish");
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patientWithCulturalNeeds);
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patientWithCulturalNeeds);
            
            culturallyMatchedCaregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity with cultural matching
            // Then: Should provide enhanced reward for cultural continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(culturallyMatchedCaregiver, visit1, visit2)
                .rewardsWith(25); // Enhanced reward for cultural continuity
        }
    }

    @Nested
    @DisplayName("Multiple Patient Continuity Management")
    class MultiplePatientContinuityManagement {

        @Test
        @DisplayName("testContinuityOfCare_withMultiplePatients_shouldBalanceAssignments")
        void testContinuityOfCare_withMultiplePatients_shouldBalanceAssignments() {
            // Given: Caregiver managing multiple patients with continuity needs
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient patient1 = testDataFactory.createPatientWithId("PATIENT1");
            Patient patient2 = testDataFactory.createPatientWithId("PATIENT2");
            
            HomecareVisit visit1a = testDataFactory.createVisitForPatient(patient1);
            HomecareVisit visit1b = testDataFactory.createVisitForPatient(patient1);
            HomecareVisit visit2a = testDataFactory.createVisitForPatient(patient2);
            HomecareVisit visit2b = testDataFactory.createVisitForPatient(patient2);
            
            caregiver.setVisits(Arrays.asList(visit1a, visit1b, visit2a, visit2b));

            // When: Checking continuity across multiple patients
            // Then: Should reward balanced continuity management
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1a, visit1b, visit2a, visit2b)
                .rewardsWith(20); // Reward for managing multiple patient continuity
        }

        @Test
        @DisplayName("testContinuityOfCare_withConflictingSchedules_shouldPrioritizeHighNeed")
        void testContinuityOfCare_withConflictingSchedules_shouldPrioritizeHighNeed() {
            // Given: Conflicting continuity needs with different priority levels
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient highPriorityPatient = testDataFactory.createHighPriorityPatient();
            Patient standardPatient = testDataFactory.createBasicPatient();
            
            HomecareVisit highPriorityVisit = testDataFactory.createHighPriorityVisit(highPriorityPatient);
            HomecareVisit standardVisit = testDataFactory.createVisitForPatient(standardPatient);
            
            caregiver.setVisits(Arrays.asList(highPriorityVisit, standardVisit));

            // When: Checking continuity with priority conflicts
            // Then: Should prioritize high-need patient continuity
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, highPriorityVisit, standardVisit)
                .rewardsWith(40); // Higher reward for prioritizing high-need continuity
        }
    }

    @Nested
    @DisplayName("Edge Cases and Exception Handling")
    class EdgeCasesAndExceptionHandling {

        @Test
        @DisplayName("testContinuityOfCare_withNewPatient_shouldHaveNeutralImpact")
        void testContinuityOfCare_withNewPatient_shouldHaveNeutralImpact() {
            // Given: New patient with no previous caregiver history
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient newPatient = testDataFactory.createNewPatient();
            
            HomecareVisit firstVisit = testDataFactory.createVisitForPatient(newPatient);
            caregiver.setVisits(Arrays.asList(firstVisit));

            // When: Checking continuity for new patient
            // Then: Should have neutral impact (no reward or penalty)
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, firstVisit)
                .rewardsWith(0); // Neutral for new patient relationships
        }

        @Test
        @DisplayName("testContinuityOfCare_withEmptyVisitHistory_shouldHandleGracefully")
        void testContinuityOfCare_withEmptyVisitHistory_shouldHandleGracefully() {
            // Given: Caregiver with no visit history
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            caregiver.setVisits(Arrays.asList());

            // When: Checking continuity with empty history
            // Then: Should handle gracefully without errors
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver)
                .rewardsWith(0); // No reward for empty history
        }

        @Test
        @DisplayName("testContinuityOfCare_withLargeTimeGaps_shouldReduceReward")
        void testContinuityOfCare_withLargeTimeGaps_shouldReduceReward() {
            // Given: Same caregiver assigned with large time gaps
            CareStaff caregiver = testDataFactory.createBasicCareStaff();
            Patient patient = testDataFactory.createBasicPatient();
            
            HomecareVisit visit1 = testDataFactory.createVisitForPatient(patient);
            visit1.setStartTime(LocalDateTime.of(2025, 1, 1, 9, 0)); // January
            
            HomecareVisit visit2 = testDataFactory.createVisitForPatient(patient);
            visit2.setStartTime(LocalDateTime.of(2025, 6, 1, 9, 0)); // June (5 months gap)
            
            caregiver.setVisits(Arrays.asList(visit1, visit2));

            // When: Checking continuity with large time gaps
            // Then: Should reduce reward due to time gap
            constraintVerifier.verifyThat(EnhancedHomecareConstraintProvider::rewardContinuityOfCare)
                .given(caregiver, visit1, visit2)
                .rewardsWith(3); // Reduced reward for large time gap
        }
    }
}
