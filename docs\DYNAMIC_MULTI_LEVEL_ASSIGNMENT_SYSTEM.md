# Dynamic Multi-Level Clinician Auto-Assignment System

## Overview

The Dynamic Multi-Level Clinician Auto-Assignment System is a comprehensive solution for optimizing healthcare clinician assignments using Timefold Solver. The system implements a three-level filtering approach with dynamic rule evaluation and intelligent relaxation strategies.

## Architecture

### Hexagonal Architecture Pattern

The system follows the hexagonal architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Interfaces Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ REST Controller │  │   Web UI        │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │     DynamicMultiLevelAssignmentService                  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Domain Models   │  │ Dynamic Rules   │  │ Constraints  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Configuration   │  │ Timefold Solver │  │   Database   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Multi-Level Filtering System

### Level 1: Mandatory Assignment Criteria (Hard Constraints)

**Purpose**: Ensure basic feasibility requirements are met.

**Criteria**:
- **Service Date and Time**: Clinician availability validation
- **Geographic Service Area**: Location within service boundaries
- **Required Skills and Competencies**: Skill matching requirements
- **Active Status and Compliance**: Regulatory and certification compliance

**Implementation**: Hard constraints that must be satisfied for any valid assignment.

### Level 2: Resource Capacity and Load Management

**Purpose**: Optimize resource utilization while maintaining quality.

**Criteria**:
- **Workload Limits**: Daily/weekly workload point management
- **Visit Count**: Maximum visits per clinician per period
- **Travel Time and Buffer**: Realistic scheduling with travel considerations

**Implementation**: Capacity constraints with optimization objectives.

### Level 3: Intelligent Alternative Assignment Pathways (Relaxation)

**Purpose**: Provide alternative solutions when strict criteria cannot be met.

**Strategies**:
- **Skill Substitution/Hierarchy**: Alternative skill matching
- **Extended Service Radius/Network**: Expanded geographic coverage
- **Overtime/Stretched Capacity**: Controlled capacity extension
- **Visit Rescheduling**: Flexible timing within care windows

**Implementation**: Soft constraints with penalty-based relaxation.

## Dynamic Rule System

### Rule Configuration

Rules are defined in JSON configuration files with the following structure:

```json
{
  "rule_id": "L1_SKILL_MATCH",
  "name": "Required Skills Match",
  "description": "Clinician must have all required skills",
  "level": "level_1",
  "type": "hard",
  "field": "skills",
  "operator": "contains_all",
  "value": "required_skills",
  "target_entity": "clinician",
  "weight": 1000.0,
  "enabled": true,
  "tags": ["skills", "mandatory", "level1"]
}
```

### Supported Operators

- **contains_all**: All values must be present
- **contains_any**: At least one value must be present
- **in**: Value must be in the specified list
- **not_in**: Value must not be in the specified list
- **equals**: Exact value match
- **not_equals**: Value must not match
- **greater_than**: Numeric comparison
- **greater_than_or_equal**: Numeric comparison
- **less_than**: Numeric comparison
- **less_than_or_equal**: Numeric comparison
- **within_distance**: Geographic distance constraint
- **overlaps_time**: Time range overlap validation
- **is_available_at**: Availability slot validation
- **has_capacity_for**: Capacity constraint validation

### Rule Factory

The `DynamicRuleFactory` automatically creates rule objects from JSON configuration:

1. **Rule Loading**: Parse JSON configuration into rule objects
2. **Function Generation**: Create evaluation functions based on operators
3. **Validation**: Ensure rule consistency and completeness
4. **Caching**: Optimize performance with intelligent caching

## Core Components

### Domain Models

#### ClinicianAssignmentRequest
- Patient requirements and visit details
- Geographic location and timing
- Skill and competency requirements
- Flexibility options for Level 3 processing

#### DynamicRule
- Rule definition with metadata
- Evaluation operators and target entities
- Level and type classification
- Weight and priority settings

#### DynamicClinicianAssignment (Planning Entity)
- Timefold planning entity for optimization
- Assignment tracking across levels
- Relaxation strategy application
- Quality and score metrics

#### MultiLevelAssignmentSolution (Planning Solution)
- Complete solution representation
- Multi-level rule organization
- Statistics and monitoring data
- Relaxation configuration

### Services

#### DynamicMultiLevelAssignmentService
- Main orchestration service
- Level-by-level processing workflow
- Solver configuration and execution
- Solution optimization and validation

#### DynamicRuleFactory
- Rule object creation from configuration
- Evaluation function generation
- Operator-specific logic implementation
- Error handling and validation

### Infrastructure

#### DynamicRuleConfigurationLoader
- JSON configuration loading
- Rule validation and caching
- Statistics and monitoring
- Hot-reload capabilities

#### DynamicMultiLevelConstraintProvider
- Timefold constraint implementation
- Multi-level constraint evaluation
- Relaxation penalty application
- Optimization objective definition

## API Endpoints

### Assignment Processing
```
POST /api/v1/dynamic-assignment/process
```
Process assignment requests through the multi-level system.

### Rule Management
```
GET /api/v1/dynamic-assignment/rules
POST /api/v1/dynamic-assignment/rules/reload
POST /api/v1/dynamic-assignment/rules/validate
```
Manage dynamic rules configuration.

### System Monitoring
```
GET /api/v1/dynamic-assignment/statistics
```
Retrieve system statistics and performance metrics.

## Configuration

### Application Properties
```yaml
timefold:
  solver:
    termination:
      spent-limit: PT30S
    environment-mode: REPRODUCIBLE

assignment:
  rules:
    config-path: config/dynamic-assignment-rules.json
    cache-duration: PT5M
    validation-enabled: true
  
  relaxation:
    skill-substitution:
      enabled: true
      penalty: 0.5
    extended-radius:
      enabled: true
      max-distance: 50.0
      penalty: 0.3
    overtime:
      enabled: false
      max-hours: 2
      penalty: 0.8
    rescheduling:
      enabled: true
      window-hours: 24
      penalty: 0.2
```

### Rule Configuration File
Located at `src/main/resources/config/dynamic-assignment-rules.json`

## Usage Examples

### Basic Assignment Processing
```java
@Autowired
private DynamicMultiLevelAssignmentService assignmentService;

@Autowired
private DynamicRuleConfigurationLoader ruleLoader;

public void processAssignments() {
    // Load requests and resources
    List<ClinicianAssignmentRequest> requests = loadAssignmentRequests();
    List<Clinician> clinicians = loadClinicians();
    List<CareStaff> careStaff = loadCareStaff();
    
    // Load dynamic rules
    List<DynamicRule> rules = ruleLoader.getCachedRules();
    
    // Process assignments
    MultiLevelAssignmentSolution solution = assignmentService.processAssignments(
        requests, clinicians, careStaff, rules);
    
    // Analyze results
    System.out.println("Completion Rate: " + solution.getCompletionRate());
    System.out.println("Level 1 Success: " + solution.getSuccessRateForLevel(1));
    System.out.println("Level 2 Success: " + solution.getSuccessRateForLevel(2));
    System.out.println("Level 3 Success: " + solution.getSuccessRateForLevel(3));
}
```

### Custom Rule Creation
```java
DynamicRule customRule = DynamicRule.builder()
    .ruleId("CUSTOM_EXPERIENCE")
    .name("Experience Requirement")
    .description("Minimum experience for complex cases")
    .level(DynamicRule.RuleLevel.LEVEL_1)
    .type(DynamicRule.RuleType.HARD)
    .field("experience_years")
    .operator(DynamicRule.RuleOperator.GREATER_THAN_OR_EQUAL)
    .value(5)
    .targetEntity("clinician")
    .weight(800.0)
    .enabled(true)
    .build();

// Create rule with evaluation function
DynamicRule processedRule = ruleFactory.createRule(customRule);
```

## Performance Considerations

### Optimization Strategies
1. **Rule Caching**: Intelligent caching of rule configurations
2. **Incremental Processing**: Level-by-level optimization
3. **Constraint Ordering**: Hard constraints evaluated first
4. **Solver Tuning**: Level-specific solver configurations

### Scalability
- **Small Scale**: 10-50 assignments, < 1 second processing
- **Medium Scale**: 100-500 assignments, 5-30 seconds processing
- **Large Scale**: 1000+ assignments, 1-5 minutes processing

### Memory Management
- Efficient data structures for large-scale processing
- Garbage collection optimization
- Resource cleanup after processing

## Monitoring and Analytics

### Key Metrics
- **Completion Rate**: Overall assignment success percentage
- **Level Success Rates**: Success rate by processing level
- **Relaxation Usage**: Frequency of relaxation strategies
- **Processing Time**: Performance metrics by scale
- **Rule Violations**: Constraint violation tracking

### Logging
- Structured logging with correlation IDs
- Performance timing at each level
- Rule evaluation results
- Error tracking and alerting

## Extension Points

### Adding New Operators
1. Define operator enum value
2. Implement evaluation logic in `DynamicRuleFactory`
3. Add validation rules
4. Update documentation

### Custom Relaxation Strategies
1. Extend `RelaxationConfiguration`
2. Implement relaxation logic in service
3. Add penalty calculations
4. Update constraint provider

### Integration Points
- **External APIs**: Geographic services, traffic data
- **Machine Learning**: Predictive assignment optimization
- **Reporting**: Advanced analytics and dashboards
- **Workflow Systems**: Integration with care management platforms

## Best Practices

### Rule Design
- Keep rules simple and focused
- Use descriptive names and documentation
- Test rules thoroughly before deployment
- Monitor rule performance and effectiveness

### System Configuration
- Start with conservative relaxation settings
- Monitor system performance under load
- Regularly review and update rule configurations
- Implement proper error handling and fallbacks

### Deployment
- Use blue-green deployment for rule updates
- Implement configuration validation
- Monitor system health after changes
- Maintain rollback capabilities
