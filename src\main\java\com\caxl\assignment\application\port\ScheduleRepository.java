package com.caxl.assignment.application.port;

import com.caxl.assignment.domain.models.Assignment;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for schedule data access.
 * Manages complete schedules and their optimization results.
 */
public interface ScheduleRepository {

    /**
     * Save a complete schedule for a date.
     * 
     * @param schedulingDate Date of the schedule
     * @param assignments List of assignments in the schedule
     * @param metadata Schedule metadata
     * @return Saved schedule
     */
    Schedule saveSchedule(LocalDate schedulingDate, List<Assignment> assignments, ScheduleMetadata metadata);

    /**
     * Find schedule by date.
     * 
     * @param schedulingDate Date to query
     * @return Schedule if found
     */
    Optional<Schedule> findScheduleByDate(LocalDate schedulingDate);

    /**
     * Find schedules within a date range.
     * 
     * @param startDate Start date
     * @param endDate End date
     * @return List of schedules in the range
     */
    List<Schedule> findSchedulesByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * Update schedule with new assignments.
     * 
     * @param schedulingDate Date of the schedule
     * @param assignments Updated assignments
     * @param metadata Updated metadata
     * @return Updated schedule
     */
    Schedule updateSchedule(LocalDate schedulingDate, List<Assignment> assignments, ScheduleMetadata metadata);

    /**
     * Delete schedule for a specific date.
     * 
     * @param schedulingDate Date of schedule to delete
     */
    void deleteSchedule(LocalDate schedulingDate);

    /**
     * Check if schedule exists for a date.
     * 
     * @param schedulingDate Date to check
     * @return True if schedule exists
     */
    boolean scheduleExists(LocalDate schedulingDate);

    /**
     * Get schedule optimization history.
     * 
     * @param schedulingDate Date to query
     * @return List of optimization attempts
     */
    List<OptimizationAttempt> getOptimizationHistory(LocalDate schedulingDate);

    /**
     * Complete schedule representation.
     */
    record Schedule(
        LocalDate schedulingDate,
        List<Assignment> assignments,
        ScheduleMetadata metadata,
        ScheduleStatus status
    ) {}

    /**
     * Schedule metadata.
     */
    record ScheduleMetadata(
        long optimizationTimeMs,
        double totalScore,
        int totalAssignments,
        int unassignedPatients,
        String optimizationStrategy,
        boolean isOptimal
    ) {}

    /**
     * Schedule status.
     */
    enum ScheduleStatus {
        DRAFT, OPTIMIZED, PUBLISHED, ACTIVE, COMPLETED, CANCELLED
    }

    /**
     * Optimization attempt record.
     */
    record OptimizationAttempt(
        LocalDate schedulingDate,
        java.time.LocalDateTime attemptTime,
        long optimizationTimeMs,
        double score,
        String strategy,
        boolean successful,
        String notes
    ) {}
}
