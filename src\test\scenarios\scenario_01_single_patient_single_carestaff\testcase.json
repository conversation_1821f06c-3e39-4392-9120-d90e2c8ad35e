{"name": "Single Patient, Single Carestaff - Basic Matching", "description": "Basic scenario testing fundamental matching logic with one patient and one carestaff. Tests skill matching, geographic constraints, and availability overlap.", "service_requests": [{"request_id": "req-001", "patient_id": "patient-001", "status": "pending", "required_skill_ids": ["skill-wound-care", "skill-medication-mgmt"], "required_certification_ids": ["cert-rn"], "time_window": {"arrival_window_start": "2025-06-01T09:00:00", "arrival_window_end": "2025-06-01T11:00:00", "visit_duration_minutes": 90, "preferred_start_time": "2025-06-01T09:30:00", "latest_end_time": "2025-06-01T11:00:00"}, "visit_type": "routine_care", "priority": 2, "workload_points": 3, "special_instructions": "Patient prefers morning visits"}], "patients": [{"patient_id": "patient-001", "enc_first_name": "<PERSON>", "enc_last_name": "<PERSON><PERSON>", "date_of_birth": "1975-03-15", "gender": "male", "location": {"location_id": "loc-001", "address": "123 Main St, Beverly Hills, CA 90210", "postal_code": "90210", "coordinates": {"latitude": 34.0522, "longitude": -118.2437}}, "preferred_language": "english", "preferred_care_staff_ids": [], "barred_care_staff_ids": [], "medical_conditions": "diabetes, hypertension", "special_instructions": "Prefers female caregivers when possible", "is_active": true}], "carestaff": [{"care_staff_id": "carestaff-001", "name": "<PERSON>", "base_location": {"latitude": 34.0548, "longitude": -118.25}, "skill_ids": ["skill-wound-care", "skill-medication-mgmt", "skill-physical-therapy"], "certification_ids": ["cert-rn", "cert-cpr"], "languages": ["english", "spanish"], "experience_years": 5, "is_active": true, "operating_zones": ["90210", "90211", "90212"], "availability_windows": [{"start_time": "2025-06-01T08:00:00", "end_time": "2025-06-01T17:00:00"}], "current_appointments": [{"start_time": "2025-06-01T08:00:00", "end_time": "2025-06-01T09:00:00", "patient_id": "other-patient-001"}, {"start_time": "2025-06-01T14:00:00", "end_time": "2025-06-01T15:30:00", "patient_id": "other-patient-002"}], "max_daily_appointments": 5}], "matching_criteria": {"filters": {"required_skills_must_match_all": false, "proximity_search_radius_km": 50.0, "must_respect_geo_service_area": true, "must_respect_availability": true, "must_not_be_barred": true, "min_score_threshold": 0.0}, "scoring_weights": {"skill_match_bonus_per_required_skill": 10.0, "skill_match_all_bonus": 50.0, "proximity_km_penalty": -1.0, "geo_service_area_bonus": 20.0, "availability_window_fit_penalty_per_minute_deviation": -0.5, "preferred_care_staff_bonus": 15.0, "continuity_bonus_per_recent_visit": 5.0, "language_match_bonus": 8.0, "experience_level_bonus_per_year": 2.0}, "geography": {"staff_service_geofence_types": ["service_area", "county_area"], "geofence_strict_containment_only": true}, "availability": {"overlap_threshold_minutes": 1, "min_time_before_visit_minutes": 30, "min_time_after_visit_minutes": 30}}, "expected_matches": {"req-001": ["carestaff-001"]}}