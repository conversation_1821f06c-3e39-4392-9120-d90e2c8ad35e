{"scenario": "Skill Hierarchy Fallback Matching with Workload Prioritization", "timestamp": "2025-05-19T23:22:24.500419", "matches": {"patient-001": [{"patient_id": "patient-001", "carestaff_id": "carestaff-001", "score": 0.8360928237438202, "rationales": [{"rule_id": "preferred-carestaff", "satisfied": false, "rationale": "Carestaff is not preferred by patient"}, {"rule_id": "gender-preference", "satisfied": true, "rationale": "Carestaff gender matches patient preference"}, {"rule_id": "language-match", "satisfied": true, "rationale": "Carestaff speaks patient's preferred language"}, {"rule_id": "experience-match", "satisfied": true, "rationale": "<PERSON><PERSON><PERSON> has required experience"}, {"rule_id": "continuity-of-care", "satisfied": false, "rationale": "Carestaff has not previously cared for patient"}, {"rule_id": "carestaff-workload", "satisfied": true, "rationale": "Carestaff has capacity for more appointments"}], "hard_rationales": [{"rule_id": "skill-match", "satisfied": true, "rationale": "Carestaff has all required skills or higher level skills"}, {"rule_id": "service-area", "satisfied": true, "rationale": "Patient is in carestaff's service area"}, {"rule_id": "date-availability-match", "satisfied": true, "rationale": "Carestaff is available during required time slots"}, {"rule_id": "certification-match", "satisfied": true, "rationale": "Carestaff has all required certifications"}, {"rule_id": "patient-exclusion", "satisfied": true, "rationale": "Carestaff is not in patient's exclusion list"}]}, {"patient_id": "patient-001", "carestaff_id": "carestaff-005", "score": 0.8360928237438202, "rationales": [{"rule_id": "preferred-carestaff", "satisfied": false, "rationale": "Carestaff is not preferred by patient"}, {"rule_id": "gender-preference", "satisfied": true, "rationale": "Carestaff gender matches patient preference"}, {"rule_id": "language-match", "satisfied": true, "rationale": "Carestaff speaks patient's preferred language"}, {"rule_id": "experience-match", "satisfied": true, "rationale": "<PERSON><PERSON><PERSON> has required experience"}, {"rule_id": "continuity-of-care", "satisfied": false, "rationale": "Carestaff has not previously cared for patient"}, {"rule_id": "carestaff-workload", "satisfied": true, "rationale": "Carestaff has capacity for more appointments"}], "hard_rationales": [{"rule_id": "skill-match", "satisfied": true, "rationale": "Carestaff has all required skills or higher level skills"}, {"rule_id": "service-area", "satisfied": true, "rationale": "Patient is in carestaff's service area"}, {"rule_id": "date-availability-match", "satisfied": true, "rationale": "Carestaff is available during required time slots"}, {"rule_id": "certification-match", "satisfied": true, "rationale": "Carestaff has all required certifications"}, {"rule_id": "patient-exclusion", "satisfied": true, "rationale": "Carestaff is not in patient's exclusion list"}]}], "patient-002": [{"patient_id": "patient-002", "carestaff_id": "carestaff-002", "score": 0.8360928237438202, "rationales": [{"rule_id": "preferred-carestaff", "satisfied": false, "rationale": "Carestaff is not preferred by patient"}, {"rule_id": "gender-preference", "satisfied": true, "rationale": "Carestaff gender matches patient preference"}, {"rule_id": "language-match", "satisfied": true, "rationale": "Carestaff speaks patient's preferred language"}, {"rule_id": "experience-match", "satisfied": true, "rationale": "<PERSON><PERSON><PERSON> has required experience"}, {"rule_id": "continuity-of-care", "satisfied": false, "rationale": "Carestaff has not previously cared for patient"}, {"rule_id": "carestaff-workload", "satisfied": true, "rationale": "Carestaff has capacity for more appointments"}], "hard_rationales": [{"rule_id": "skill-match", "satisfied": true, "rationale": "Carestaff has all required skills or higher level skills"}, {"rule_id": "service-area", "satisfied": true, "rationale": "Patient is in carestaff's service area"}, {"rule_id": "date-availability-match", "satisfied": true, "rationale": "Carestaff is available during required time slots"}, {"rule_id": "certification-match", "satisfied": true, "rationale": "Carestaff has all required certifications"}, {"rule_id": "patient-exclusion", "satisfied": true, "rationale": "Carestaff is not in patient's exclusion list"}]}, {"patient_id": "patient-002", "carestaff_id": "carestaff-006", "score": 0.8360928237438202, "rationales": [{"rule_id": "preferred-carestaff", "satisfied": false, "rationale": "Carestaff is not preferred by patient"}, {"rule_id": "gender-preference", "satisfied": true, "rationale": "Carestaff gender matches patient preference"}, {"rule_id": "language-match", "satisfied": true, "rationale": "Carestaff speaks patient's preferred language"}, {"rule_id": "experience-match", "satisfied": true, "rationale": "<PERSON><PERSON><PERSON> has required experience"}, {"rule_id": "continuity-of-care", "satisfied": false, "rationale": "Carestaff has not previously cared for patient"}, {"rule_id": "carestaff-workload", "satisfied": true, "rationale": "Carestaff has capacity for more appointments"}], "hard_rationales": [{"rule_id": "skill-match", "satisfied": true, "rationale": "Carestaff has all required skills or higher level skills"}, {"rule_id": "service-area", "satisfied": true, "rationale": "Patient is in carestaff's service area"}, {"rule_id": "date-availability-match", "satisfied": true, "rationale": "Carestaff is available during required time slots"}, {"rule_id": "certification-match", "satisfied": true, "rationale": "Carestaff has all required certifications"}, {"rule_id": "patient-exclusion", "satisfied": true, "rationale": "Carestaff is not in patient's exclusion list"}]}], "patient-003": [{"patient_id": "patient-003", "carestaff_id": "carestaff-001", "score": 0.8360928237438202, "rationales": [{"rule_id": "preferred-carestaff", "satisfied": false, "rationale": "Carestaff is not preferred by patient"}, {"rule_id": "gender-preference", "satisfied": true, "rationale": "Carestaff gender matches patient preference"}, {"rule_id": "language-match", "satisfied": true, "rationale": "Carestaff speaks patient's preferred language"}, {"rule_id": "experience-match", "satisfied": true, "rationale": "<PERSON><PERSON><PERSON> has required experience"}, {"rule_id": "continuity-of-care", "satisfied": false, "rationale": "Carestaff has not previously cared for patient"}, {"rule_id": "carestaff-workload", "satisfied": true, "rationale": "Carestaff has capacity for more appointments"}], "hard_rationales": [{"rule_id": "skill-match", "satisfied": true, "rationale": "Carestaff has all required skills or higher level skills"}, {"rule_id": "service-area", "satisfied": true, "rationale": "Patient is in carestaff's service area"}, {"rule_id": "date-availability-match", "satisfied": true, "rationale": "Carestaff is available during required time slots"}, {"rule_id": "certification-match", "satisfied": true, "rationale": "Carestaff has all required certifications"}, {"rule_id": "patient-exclusion", "satisfied": true, "rationale": "Carestaff is not in patient's exclusion list"}]}]}, "validation": {"overall_success": false, "patient_validations": {"patient-001": {"success": false, "missing_carestaff": ["carestaff-003"], "order_correct": false, "expected_first": "carestaff-005", "actual_first": "carestaff-001"}, "patient-002": {"success": false, "missing_carestaff": ["carestaff-004"], "order_correct": false, "expected_first": "carestaff-006", "actual_first": "carestaff-002"}, "patient-003": {"success": false, "missing_carestaff": ["carestaff-005"], "order_correct": false, "expected_first": "carestaff-005", "actual_first": "carestaff-001"}}}}