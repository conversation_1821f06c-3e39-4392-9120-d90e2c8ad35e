package com.caxl.assignment.application.carestaff.port.out.persistence;

import org.locationtech.jts.geom.Point;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Port interface for patient persistence operations.
 * Driving port in hexagonal architecture.
 */
public interface PatientPort {

    /**
     * Find patient by ID with location data loaded.
     * 
     * @param patientId Patient ID
     * @return Patient domain representation if exists
     */
    Optional<PatientDomain> findByIdWithLocation(UUID patientId);

    /**
     * Domain representation of patient for matching operations.
     */
    record PatientDomain(
        UUID patientId,
        String name,
        Point locationCoordinates,
        String preferredLanguage,
        List<UUID> preferredCareStaffIds,
        List<UUID> barredCareStaffIds,
        String medicalConditions,
        String specialInstructions
    ) {}
}

