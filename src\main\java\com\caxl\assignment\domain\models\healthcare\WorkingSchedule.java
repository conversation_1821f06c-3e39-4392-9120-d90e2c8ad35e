package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Working schedule configuration for care staff.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkingSchedule {

    @Valid
    @NotNull(message = "Availability windows are required")
    @JsonProperty("availability_windows")
    private List<AvailabilityWindow> availabilityWindows;

    @Positive(message = "Max daily hours must be positive")
    @JsonProperty("max_daily_hours")
    private Double maxDailyHours = 8.0;

    @Positive(message = "Max weekly hours must be positive")
    @JsonProperty("max_weekly_hours")
    private Double maxWeeklyHours = 40.0;

    @JsonProperty("overtime_allowed")
    private boolean overtimeAllowed = false;

    @Positive(message = "Max overtime hours must be positive")
    @JsonProperty("max_overtime_hours")
    private Double maxOvertimeHours = 0.0;

    @JsonProperty("flexible_scheduling")
    private boolean flexibleScheduling = true;

    /**
     * Check if the staff is available at the given time.
     */
    public boolean isAvailableAt(java.time.LocalDateTime time) {
        return availabilityWindows.stream()
                .anyMatch(window -> window.containsTime(time));
    }
}
