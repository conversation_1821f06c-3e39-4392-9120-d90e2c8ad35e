package com.caxl.assignment.api.controllers.healthcare;

import com.caxl.assignment.application.services.healthcare.*;
import com.caxl.assignment.domain.models.healthcare.*;
import com.caxl.assignment.application.services.common.ValidationUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * REST API controller for enhanced homecare scheduling with state compliance
 * and geofencing capabilities.
 */
@RestController
@RequestMapping("/api/v1/homecare/scheduling")
@CrossOrigin(origins = "*", maxAge = 3600)
public class EnhancedHomecareSchedulingController {

    private final EnhancedHomecareSchedulingService schedulingService;
    private final StateComplianceService stateComplianceService;
    private final GeofencingService geofencingService;

    @Autowired
    public EnhancedHomecareSchedulingController(
            EnhancedHomecareSchedulingService schedulingService,
            StateComplianceService stateComplianceService,
            GeofencingService geofencingService) {
        this.schedulingService = schedulingService;
        this.stateComplianceService = stateComplianceService;
        this.geofencingService = geofencingService;
    }

    @Operation(summary = "Schedule homecare visits", 
               description = "Create optimized schedules for homecare visits with state compliance and geofencing validation")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduling completed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid scheduling request"),
        @ApiResponse(responseCode = "500", description = "Internal server error during scheduling")
    })
    @PostMapping("/schedule")
    public CompletableFuture<ResponseEntity<EnhancedHomecareSchedulingService.SchedulingResult>> scheduleVisits(
            @Valid @RequestBody EnhancedHomecareSchedulingService.SchedulingRequest request) {
        
        return schedulingService.scheduleVisits(request)
                .thenApply(result -> {
                    if (result.isSuccess()) {
                        return ResponseEntity.ok(result);
                    } else {
                        return ResponseEntity.badRequest().body(result);
                    }
                })
                .exceptionally(throwable -> 
                    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(EnhancedHomecareSchedulingService.SchedulingResult.failed(
                            List.of("Scheduling failed: " + throwable.getMessage()))));
    }    @Operation(summary = "Validate scheduling request", 
               description = "Validate a scheduling request for compliance and feasibility before solving")
    @PostMapping("/validate")
    public ResponseEntity<ValidationUtils.ValidationResult> validateSchedulingRequest(
            @Valid @RequestBody EnhancedHomecareSchedulingService.SchedulingRequest request) {
        
        ValidationUtils.ValidationResult result = 
            schedulingService.validateSchedulingRequest(request);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Get scheduling recommendations", 
               description = "Get optimization recommendations based on historical data and patterns")
    @PostMapping("/recommendations")
    public ResponseEntity<EnhancedHomecareSchedulingService.SchedulingRecommendations> getSchedulingRecommendations(
            @Valid @RequestBody EnhancedHomecareSchedulingService.SchedulingRequest request) {
        
        EnhancedHomecareSchedulingService.SchedulingRecommendations recommendations = 
            schedulingService.getSchedulingRecommendations(request);
        
        return ResponseEntity.ok(recommendations);
    }

    @Operation(summary = "Reschedule visits", 
               description = "Reschedule visits due to emergency or staff unavailability")
    @PostMapping("/reschedule")
    public CompletableFuture<ResponseEntity<EnhancedHomecareSchedulingService.SchedulingResult>> rescheduleVisits(
            @Valid @RequestBody EnhancedHomecareSchedulingService.RescheduleRequest request) {
        
        return schedulingService.rescheduleVisits(request)
                .thenApply(ResponseEntity::ok)
                .exceptionally(throwable -> 
                    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(EnhancedHomecareSchedulingService.SchedulingResult.failed(
                            List.of("Rescheduling failed: " + throwable.getMessage()))));
    }

    // === STATE COMPLIANCE ENDPOINTS ===

    @Operation(summary = "Validate staff compliance for state", 
               description = "Validate care staff compliance for a specific state's regulations")
    @PostMapping("/compliance/staff/{stateCode}")
    public ResponseEntity<StateComplianceService.ComplianceValidationResult> validateStaffCompliance(
            @Parameter(description = "State code (e.g., CA, TX, NY)") @PathVariable String stateCode,
            @Valid @RequestBody CareStaff careStaff) {
        
        StateComplianceService.ComplianceValidationResult result = 
            stateComplianceService.validateStaffCompliance(careStaff, stateCode);
        
        return ResponseEntity.ok(result);
    }

    @Operation(summary = "Check working hours compliance", 
               description = "Check if proposed working hours comply with state regulations")
    @GetMapping("/compliance/working-hours/{stateCode}")
    public ResponseEntity<Boolean> checkWorkingHoursCompliance(
            @Parameter(description = "State code") @PathVariable String stateCode,
            @Parameter(description = "Care staff ID") @RequestParam String staffId,
            @Parameter(description = "Proposed hours") @RequestParam int proposedHours) {
        
        // This would typically look up the staff from a repository
        // For demonstration, we'll return a simplified response
        boolean isCompliant = stateComplianceService.isWorkingHoursCompliant(null, stateCode, proposedHours);
        
        return ResponseEntity.ok(isCompliant);
    }

    @Operation(summary = "Check overtime allowance", 
               description = "Check if overtime is allowed for given hours in a specific state")
    @GetMapping("/compliance/overtime/{stateCode}")
    public ResponseEntity<Boolean> checkOvertimeAllowance(
            @Parameter(description = "State code") @PathVariable String stateCode,
            @Parameter(description = "Current hours") @RequestParam int currentHours,
            @Parameter(description = "Additional hours") @RequestParam int additionalHours) {
        
        boolean isAllowed = stateComplianceService.isOvertimeAllowed(stateCode, currentHours, additionalHours);
        
        return ResponseEntity.ok(isAllowed);
    }

    // === GEOFENCING ENDPOINTS ===

    @Operation(summary = "Check if staff can serve patient", 
               description = "Check if a care staff member can serve a patient based on geographic zones")
    @PostMapping("/geofencing/can-serve")
    public ResponseEntity<Boolean> canStaffServePatient(
            @Valid @RequestBody CanServeRequest request) {
        
        boolean canServe = geofencingService.canServePatient(request.getCareStaff(), request.getPatient());
        
        return ResponseEntity.ok(canServe);
    }

    @Operation(summary = "Find eligible staff for patient", 
               description = "Find all care staff members who can serve a specific patient")
    @PostMapping("/geofencing/eligible-staff")
    public ResponseEntity<List<CareStaff>> findEligibleStaffForPatient(
            @Valid @RequestBody EligibleStaffRequest request) {
        
        List<CareStaff> eligibleStaff = geofencingService.findEligibleStaffForPatient(
            request.getAllStaff(), request.getPatient());
        
        return ResponseEntity.ok(eligibleStaff);
    }

    @Operation(summary = "Calculate travel distance", 
               description = "Calculate travel distance between two geographic points")
    @PostMapping("/geofencing/travel-distance")
    public ResponseEntity<TravelDistanceResponse> calculateTravelDistance(
            @Valid @RequestBody TravelDistanceRequest request) {
        
        double distance = geofencingService.calculateTravelDistance(request.getFrom(), request.getTo());
        int travelTime = geofencingService.calculateTravelTimeMinutes(request.getFrom(), request.getTo());
        
        TravelDistanceResponse response = new TravelDistanceResponse(distance, travelTime);
        
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Validate staff geographic assignments", 
               description = "Validate that all visits for a care staff member are within their operating zones")
    @PostMapping("/geofencing/validate-assignments")
    public ResponseEntity<GeofencingService.GeofencingValidationResult> validateStaffAssignments(
            @Valid @RequestBody ValidateAssignmentsRequest request) {
        
        GeofencingService.GeofencingValidationResult result = 
            geofencingService.validateStaffAssignments(request.getCareStaff(), request.getVisits());
        
        return ResponseEntity.ok(result);
    }

    // === REQUEST/RESPONSE CLASSES ===

    public static class CanServeRequest {
        private CareStaff careStaff;
        private HomePatient patient;

        // Getters and setters
        public CareStaff getCareStaff() { return careStaff; }
        public void setCareStaff(CareStaff careStaff) { this.careStaff = careStaff; }
        public HomePatient getPatient() { return patient; }
        public void setPatient(HomePatient patient) { this.patient = patient; }
    }

    public static class EligibleStaffRequest {
        private List<CareStaff> allStaff;
        private HomePatient patient;

        // Getters and setters
        public List<CareStaff> getAllStaff() { return allStaff; }
        public void setAllStaff(List<CareStaff> allStaff) { this.allStaff = allStaff; }
        public HomePatient getPatient() { return patient; }
        public void setPatient(HomePatient patient) { this.patient = patient; }
    }

    public static class TravelDistanceRequest {
        private GeoZone.GeoPoint from;
        private GeoZone.GeoPoint to;

        // Getters and setters
        public GeoZone.GeoPoint getFrom() { return from; }
        public void setFrom(GeoZone.GeoPoint from) { this.from = from; }
        public GeoZone.GeoPoint getTo() { return to; }
        public void setTo(GeoZone.GeoPoint to) { this.to = to; }
    }

    public static class TravelDistanceResponse {
        private final double distanceMiles;
        private final int travelTimeMinutes;

        public TravelDistanceResponse(double distanceMiles, int travelTimeMinutes) {
            this.distanceMiles = distanceMiles;
            this.travelTimeMinutes = travelTimeMinutes;
        }

        public double getDistanceMiles() { return distanceMiles; }
        public int getTravelTimeMinutes() { return travelTimeMinutes; }
    }

    public static class ValidateAssignmentsRequest {
        private CareStaff careStaff;
        private List<HomecareVisit> visits;

        // Getters and setters
        public CareStaff getCareStaff() { return careStaff; }
        public void setCareStaff(CareStaff careStaff) { this.careStaff = careStaff; }
        public List<HomecareVisit> getVisits() { return visits; }
        public void setVisits(List<HomecareVisit> visits) { this.visits = visits; }
    }
}
