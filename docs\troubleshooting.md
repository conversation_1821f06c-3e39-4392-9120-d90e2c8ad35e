# Troubleshooting Guide - CareStaff Matching Service

## Overview

This guide helps you diagnose and resolve common issues with the CareStaff Matching Service.

## Common Issues

### 1. Application Startup Issues

#### Issue: Application fails to start with database connection error

**Symptoms:**
```
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource'
Caused by: org.postgresql.util.PSQLException: Connection to localhost:5432 refused
```

**Solutions:**

1. **Check PostgreSQL is running:**
   ```bash
   # For Docker
   docker ps | grep postgres
   
   # For system service
   systemctl status postgresql
   ```

2. **Verify connection parameters:**
   ```yaml
   # Check application.yml
   spring:
     datasource:
       url: ****************************************************
       username: caxl_user
       password: caxl_password
   ```

3. **Test database connectivity:**
   ```bash
   psql -h localhost -U caxl_user -d homecare_scheduling
   ```

#### Issue: PostGIS extension not found

**Symptoms:**
```
org.postgresql.util.PSQLException: ERROR: function st_dwithin(geometry, geometry, double precision) does not exist
```

**Solutions:**

1. **Enable PostGIS extension:**
   ```sql
   -- Connect to database
   psql -h localhost -U caxl_user -d homecare_scheduling
   
   -- Enable PostGIS
   CREATE EXTENSION IF NOT EXISTS postgis;
   
   -- Verify installation
   SELECT PostGIS_Version();
   ```

2. **Check PostGIS installation:**
   ```bash
   # For Docker
   docker exec -it postgres-container psql -U caxl_user -d homecare_scheduling -c "SELECT PostGIS_Version();"
   ```

#### Issue: Port already in use

**Symptoms:**
```
org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
```

**Solutions:**

1. **Change application port:**
   ```yaml
   # application.yml
   server:
     port: 8081
   ```

2. **Kill existing process:**
   ```bash
   # Find process using port
   lsof -ti:8080
   
   # Kill the process
   kill -9 $(lsof -ti:8080)
   ```

3. **Use different port temporarily:**
   ```bash
   mvn spring-boot:run -Dspring-boot.run.arguments="--server.port=8081"
   ```

### 2. API Issues

#### Issue: No match suggestions generated

**Symptoms:**
```json
{
  "success": true,
  "message": "Generated 0 match suggestions",
  "data": [],
  "timestamp": "2024-01-14T10:30:00"
}
```

**Diagnosis Steps:**

1. **Check active configuration:**
   ```sql
   SELECT config_name, is_active FROM matching_configuration WHERE is_active = true;
   ```

2. **Verify service request exists:**
   ```sql
   SELECT * FROM service_request WHERE request_id = 'your-request-id';
   ```

3. **Check for available carestaff:**
   ```sql
   SELECT COUNT(*) FROM caresstaff_pe WHERE active = true;
   ```

4. **Review configuration thresholds:**
   ```sql
   SELECT criteria_json->'filters'->>'minScoreThreshold' as min_threshold
   FROM matching_configuration WHERE is_active = true;
   ```

**Solutions:**

1. **Lower minimum score threshold:**
   ```sql
   UPDATE matching_configuration 
   SET criteria_json = jsonb_set(criteria_json, '{filters,minScoreThreshold}', '0.0')
   WHERE is_active = true;
   ```

2. **Increase search radius:**
   ```sql
   UPDATE matching_configuration 
   SET criteria_json = jsonb_set(criteria_json, '{filters,proximitySearchRadiusKm}', '100.0')
   WHERE is_active = true;
   ```

3. **Add test data:**
   ```sql
   -- Add test carestaff
   INSERT INTO caresstaff_pe (carestaff_id, enc_first_name, enc_last_name, location_id, active)
   VALUES (uuid_generate_v4(), 'Test', 'Staff', 
           (SELECT location_id FROM location LIMIT 1), true);
   ```

#### Issue: Configuration not found error

**Symptoms:**
```
ConfigurationException: No active matching configuration found
```

**Solutions:**

1. **Check for active configuration:**
   ```sql
   SELECT * FROM matching_configuration WHERE is_active = true;
   ```

2. **Create default configuration:**
   ```sql
   INSERT INTO matching_configuration (config_id, config_name, criteria_json, is_active, created_by)
   VALUES (
     uuid_generate_v4(),
     'default_config',
     '{"filters": {"proximitySearchRadiusKm": 50.0, "minScoreThreshold": 0.0}, "scoring_weights": {"skillMatchBonusPerRequiredSkill": 10.0}}',
     true,
     'system'
   );
   ```

#### Issue: Spatial query errors

**Symptoms:**
```
org.postgresql.util.PSQLException: ERROR: Geometry SRID (0) does not match column SRID (4326)
```

**Solutions:**

1. **Check SRID consistency:**
   ```sql
   -- Check location table SRID
   SELECT Find_SRID('public', 'location', 'coordinates');
   
   -- Update geometry SRID if needed
   UPDATE location SET coordinates = ST_SetSRID(coordinates, 4326) WHERE ST_SRID(coordinates) != 4326;
   ```

2. **Verify spatial indexes:**
   ```sql
   -- Check spatial indexes
   SELECT schemaname, tablename, indexname 
   FROM pg_indexes 
   WHERE indexdef LIKE '%GIST%';
   
   -- Create missing spatial index
   CREATE INDEX CONCURRENTLY idx_location_coordinates 
   ON location USING GIST(coordinates);
   ```

### 3. Performance Issues

#### Issue: Slow match suggestion generation

**Symptoms:**
- API responses taking > 5 seconds
- High CPU usage during matching
- Database query timeouts

**Diagnosis:**

1. **Enable SQL logging:**
   ```yaml
   logging:
     level:
       org.hibernate.SQL: DEBUG
       org.hibernate.type.descriptor.sql.BasicBinder: TRACE
   ```

2. **Check database performance:**
   ```sql
   -- Check slow queries
   SELECT query, mean_exec_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_exec_time DESC 
   LIMIT 10;
   
   -- Check index usage
   SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
   FROM pg_stat_user_indexes
   ORDER BY idx_scan DESC;
   ```

**Solutions:**

1. **Optimize database indexes:**
   ```sql
   -- Create performance indexes
   CREATE INDEX CONCURRENTLY idx_carestaff_active ON caresstaff_pe(active) WHERE active = true;
   CREATE INDEX CONCURRENTLY idx_appointment_staff_time ON appointment(carestaff_id, scheduled_start_time);
   CREATE INDEX CONCURRENTLY idx_service_request_status ON service_request(status);
   ```

2. **Tune configuration:**
   ```sql
   -- Reduce search radius
   UPDATE matching_configuration 
   SET criteria_json = jsonb_set(criteria_json, '{filters,proximitySearchRadiusKm}', '25.0')
   WHERE is_active = true;
   
   -- Increase minimum score threshold
   UPDATE matching_configuration 
   SET criteria_json = jsonb_set(criteria_json, '{filters,minScoreThreshold}', '10.0')
   WHERE is_active = true;
   ```

3. **Optimize JVM settings:**
   ```bash
   # Increase heap size
   export JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC"
   
   # Run with optimized settings
   java $JAVA_OPTS -jar target/carestaff-matching-service-1.0.0.jar
   ```

#### Issue: Memory issues

**Symptoms:**
```
java.lang.OutOfMemoryError: Java heap space
```

**Solutions:**

1. **Increase heap size:**
   ```bash
   export JAVA_OPTS="-Xmx4g -Xms2g"
   mvn spring-boot:run -Dspring-boot.run.jvmArguments="$JAVA_OPTS"
   ```

2. **Monitor memory usage:**
   ```bash
   # Check memory usage
   jstat -gc <pid> 5s
   
   # Generate heap dump
   jmap -dump:format=b,file=heapdump.hprof <pid>
   ```

3. **Optimize queries:**
   ```java
   // Use pagination for large result sets
   Pageable pageable = PageRequest.of(0, 100);
   Page<MatchSuggestion> suggestions = repository.findByServiceRequestId(requestId, pageable);
   ```

### 4. Data Issues

#### Issue: Invalid coordinates or spatial data

**Symptoms:**
```
org.locationtech.jts.io.ParseException: Invalid geometry
```

**Solutions:**

1. **Validate spatial data:**
   ```sql
   -- Check for invalid geometries
   SELECT location_id, ST_IsValid(coordinates) as is_valid
   FROM location 
   WHERE NOT ST_IsValid(coordinates);
   
   -- Fix invalid geometries
   UPDATE location 
   SET coordinates = ST_MakeValid(coordinates) 
   WHERE NOT ST_IsValid(coordinates);
   ```

2. **Check coordinate systems:**
   ```sql
   -- Verify SRID consistency
   SELECT DISTINCT ST_SRID(coordinates) FROM location;
   SELECT DISTINCT ST_SRID(boundary) FROM geofences;
   ```

#### Issue: Missing or inconsistent test data

**Solutions:**

1. **Add comprehensive test data:**
   ```sql
   -- Add test locations
   INSERT INTO location (location_id, coordinates) VALUES
   (uuid_generate_v4(), ST_SetSRID(ST_Point(-89.6501, 39.7817), 4326)),
   (uuid_generate_v4(), ST_SetSRID(ST_Point(-89.6401, 39.7917), 4326));
   
   -- Add test patients
   INSERT INTO patient (patient_id, enc_first_name, location_id, is_active)
   SELECT uuid_generate_v4(), 'Test Patient', location_id, true
   FROM location LIMIT 1;
   
   -- Add test carestaff
   INSERT INTO caresstaff_pe (carestaff_id, enc_first_name, location_id, active)
   SELECT uuid_generate_v4(), 'Test Staff', location_id, true
   FROM location LIMIT 1;
   ```

### 5. Configuration Issues

#### Issue: Configuration changes not taking effect

**Solutions:**

1. **Verify configuration is active:**
   ```sql
   SELECT config_name, is_active, updated_at 
   FROM matching_configuration 
   ORDER BY updated_at DESC;
   ```

2. **Check configuration cache:**
   ```bash
   # Restart application to clear cache
   curl -X POST http://localhost:8080/actuator/restart
   ```

3. **Validate JSON format:**
   ```sql
   -- Test JSON validity
   SELECT config_name, 
          CASE WHEN criteria_json::json IS NOT NULL THEN 'Valid' ELSE 'Invalid' END as json_status
   FROM matching_configuration;
   ```

## Debugging Tools

### 1. Application Logs

**Enable debug logging:**
```yaml
logging:
  level:
    com.caxl.assignment: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
```

**Key log patterns to search for:**
```bash
# Matching process
grep "Starting match suggestion process" logs/application.log

# Configuration loading
grep "MatchingCriteria" logs/application.log

# Database queries
grep "Hibernate:" logs/application.log

# Errors
grep "ERROR" logs/application.log
```

### 2. Database Queries

**Check system status:**
```sql
-- Active connections
SELECT count(*) FROM pg_stat_activity;

-- Database size
SELECT pg_size_pretty(pg_database_size('homecare_scheduling'));

-- Table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename))
FROM pg_tables WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 3. Health Checks

**Application health endpoints:**
```bash
# Overall health
curl http://localhost:8080/actuator/health

# Database health
curl http://localhost:8080/actuator/health/db

# Application metrics
curl http://localhost:8080/actuator/metrics

# Application info
curl http://localhost:8080/actuator/info
```

### 4. Performance Monitoring

**JVM monitoring:**
```bash
# Memory usage
jstat -gc <pid>

# Thread dump
jstack <pid>

# CPU usage
top -p <pid>
```

**Database monitoring:**
```sql
-- Current queries
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';

-- Lock information
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement,
       blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

## Getting Help

### 1. Log Analysis

When reporting issues, include:
- Application logs (last 100 lines)
- Database logs (if available)
- Configuration details
- Steps to reproduce

### 2. System Information

Collect system information:
```bash
# Java version
java -version

# Application version
curl http://localhost:8080/actuator/info

# Database version
psql -h localhost -U caxl_user -d homecare_scheduling -c "SELECT version();"

# PostGIS version
psql -h localhost -U caxl_user -d homecare_scheduling -c "SELECT PostGIS_Version();"
```

### 3. Configuration Export

Export current configuration:
```sql
-- Export active configuration
SELECT config_name, criteria_json, created_at, updated_at
FROM matching_configuration 
WHERE is_active = true;
```

### 4. Support Channels

1. Check this troubleshooting guide first
2. Review the [Installation Guide](installation.md) for setup issues
3. Check the [Configuration Guide](configuration.md) for configuration problems
4. Create an issue with detailed information including logs and system details

## Prevention

### 1. Regular Maintenance

- Monitor application and database logs regularly
- Keep dependencies updated
- Regular database maintenance (VACUUM, ANALYZE)
- Monitor disk space and memory usage

### 2. Testing

- Test configuration changes in development first
- Regular backup and restore testing
- Performance testing with realistic data volumes
- Integration testing after updates

### 3. Monitoring

- Set up application monitoring (metrics, alerts)
- Database performance monitoring
- Log aggregation and analysis
- Health check automation
