package com.caxl.carestaff;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Spring Boot application class for CareStaff Matching Service.
 * 
 * This application implements a configurable, geo-filtered & constraint-aware 
 * carestaff matching service using hexagonal architecture with programmed heuristics.
 */
@SpringBootApplication
@EnableJpaRepositories
@EnableTransactionManagement
public class CareStaffMatchingServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(CareStaffMatchingServiceApplication.class, args);
    }
}
