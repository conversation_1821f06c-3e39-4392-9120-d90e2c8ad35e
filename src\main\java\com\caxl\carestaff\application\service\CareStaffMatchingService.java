package com.caxl.carestaff.application.service;

import com.caxl.carestaff.application.port.in.*;
import com.caxl.carestaff.application.port.out.persistence.*;
import com.caxl.carestaff.domain.entities.*;
import com.caxl.carestaff.domain.exceptions.*;
import com.caxl.carestaff.domain.valueobjects.MatchingCriteria;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Polygon;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Core service implementing carestaff matching logic using configurable constraints.
 * 
 * Note: This service implements a heuristic matching algorithm programmatically 
 * based on configurable constraints.
 * 
 * TODO: Consider integrating a dedicated planning optimizer like Timefold for 
 * optimal assignments across multiple service requests simultaneously.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CareStaffMatchingService implements 
    SuggestMatchesUseCase, 
    OverrideMatchUseCase, 
    AssignBulkMatchesUseCase, 
    RetrieveSuggestionsUseCase {

    private final MatchingConfigurationPort matchingConfigPort;
    private final ServiceRequestPort serviceRequestPort;
    private final PatientPort patientPort;
    private final CareStaffPort careStaffPort;
    private final MatchSuggestionPort matchSuggestionPort;
    private final MatchOverridePort matchOverridePort;
    private final AppointmentPort appointmentPort;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public List<MatchSuggestion> suggestMatches(UUID requestId) {
        log.info("Starting match suggestion process for request: {}", requestId);

        // Fetch active matching configuration
        MatchingCriteria criteria = matchingConfigPort.findActiveConfiguration()
                .orElseThrow(ConfigurationException::noActiveConfiguration);

        // Fetch service request with relations
        ServiceRequest serviceRequest = serviceRequestPort.findByIdWithRelations(requestId)
                .orElseThrow(() -> ResourceNotFoundException.serviceRequest(requestId.toString()));

        // Fetch patient with location
        PatientPort.PatientDomain patient = patientPort.findByIdWithLocation(serviceRequest.getPatientId())
                .orElseThrow(() -> ResourceNotFoundException.patient(serviceRequest.getPatientId().toString()));

        Point patientLocationCoords = patient.locationCoordinates();
        if (patientLocationCoords == null) {
            throw new MatchingException("Patient location coordinates not available for request: " + requestId);
        }

        // Extract required skills
        List<UUID> requiredSkillIds = extractRequiredSkills(serviceRequest, patient);
        log.debug("Required skills for request {}: {}", requestId, requiredSkillIds);

        // Find initial candidates using proximity and skill filters
        List<CareStaffPort.CareStaffDomain> potentialStaff = careStaffPort.findPotentialStaffBySkillsAndProximity(
                requiredSkillIds,
                patientLocationCoords,
                criteria.getFilters().getProximitySearchRadiusKm(),
                criteria.getFilters().isRequiredSkillsMustMatchAll()
        );

        log.debug("Found {} potential staff candidates", potentialStaff.size());

        // Apply hard filters and calculate scores
        List<MatchSuggestion> suggestions = new ArrayList<>();
        
        for (CareStaffPort.CareStaffDomain staff : potentialStaff) {
            try {
                // Apply hard filters
                if (!passesHardFilters(staff, serviceRequest, patient, criteria)) {
                    log.debug("Staff {} failed hard filters", staff.careStaffId());
                    continue;
                }

                // Calculate match score
                ScoreResult scoreResult = calculateMatchScore(
                        serviceRequest, patient, staff, criteria, patientLocationCoords);

                // Check minimum score threshold
                if (scoreResult.score() < criteria.getFilters().getMinScoreThreshold()) {
                    log.debug("Staff {} score {} below threshold {}", 
                            staff.careStaffId(), scoreResult.score(), criteria.getFilters().getMinScoreThreshold());
                    continue;
                }

                // Create suggestion
                MatchSuggestion suggestion = MatchSuggestion.create(
                        requestId,
                        staff.careStaffId(),
                        scoreResult.score(),
                        scoreResult.rationale(),
                        serviceRequest.getTimeWindow().getArrivalWindowStart()
                );

                suggestions.add(suggestion);
                log.debug("Created suggestion for staff {} with score {}", staff.careStaffId(), scoreResult.score());

            } catch (Exception e) {
                log.warn("Error processing staff {}: {}", staff.careStaffId(), e.getMessage());
            }
        }

        // Sort by score (highest first) and save
        suggestions.sort((s1, s2) -> Double.compare(s2.getScore(), s1.getScore()));
        
        List<MatchSuggestion> savedSuggestions = matchSuggestionPort.saveAll(suggestions);
        
        log.info("Generated {} match suggestions for request {}", savedSuggestions.size(), requestId);
        return savedSuggestions;
    }

    /**
     * Extract required skills from service request and patient profile.
     * TODO: Refine extraction of required skill IDs from Patient/Service Request based on specific business rules.
     */
    private List<UUID> extractRequiredSkills(ServiceRequest serviceRequest, PatientPort.PatientDomain patient) {
        List<UUID> requiredSkills = new ArrayList<>();
        
        // Add skills from service request
        if (serviceRequest.getRequiredSkillIds() != null) {
            requiredSkills.addAll(serviceRequest.getRequiredSkillIds());
        }
        
        // TODO: Add logic to derive skills from patient medical conditions
        // TODO: Add logic to derive skills from visit type
        
        // If no specific skills found, return empty list (will match all staff)
        return requiredSkills;
    }

    /**
     * Apply hard constraint filters to determine if staff is eligible.
     */
    private boolean passesHardFilters(CareStaffPort.CareStaffDomain staff, 
                                    ServiceRequest serviceRequest, 
                                    PatientPort.PatientDomain patient, 
                                    MatchingCriteria criteria) {
        
        // Geofence service area filter
        if (criteria.getFilters().isMustRespectGeoServiceArea()) {
            List<Polygon> serviceGeofences = careStaffPort.findServiceGeofencesForStaff(
                    staff.careStaffId(), 
                    criteria.getGeography().getStaffServiceGeofenceTypes()
            );
            
            if (!serviceGeofences.isEmpty()) {
                boolean withinServiceArea = serviceGeofences.stream()
                        .anyMatch(geofence -> criteria.getGeography().isGeofenceStrictContainmentOnly() 
                                ? geofence.contains(patient.locationCoordinates())
                                : geofence.intersects(patient.locationCoordinates()));
                
                if (!withinServiceArea) {
                    log.debug("Staff {} failed geofence filter", staff.careStaffId());
                    return false;
                }
            }
        }

        // Availability filter
        if (criteria.getFilters().isMustRespectAvailability()) {
            CareStaffPort.ServiceRequestTimeWindow timeWindow = new CareStaffPort.ServiceRequestTimeWindow(
                    serviceRequest.getTimeWindow().getArrivalWindowStart(),
                    serviceRequest.getTimeWindow().getArrivalWindowEnd()
            );
            
            List<Appointment> overlappingAppointments = careStaffPort.findOverlappingAppointments(
                    staff.careStaffId(), 
                    timeWindow, 
                    criteria.getAvailability().getOverlapThresholdMinutes()
            );
            
            if (!overlappingAppointments.isEmpty()) {
                log.debug("Staff {} failed availability filter", staff.careStaffId());
                return false;
            }
        }

        // Barred staff filter
        if (criteria.getFilters().isMustNotBeBarred()) {
            if (patient.barredCareStaffIds() != null && 
                patient.barredCareStaffIds().contains(staff.careStaffId())) {
                log.debug("Staff {} is barred by patient", staff.careStaffId());
                return false;
            }
        }

        // TODO: Implement check against Patient's barred staff list
        // TODO: Add other hard filters (minimum experience, required certifications)

        return true;
    }

    /**
     * Calculate match score based on soft constraints.
     */
    private ScoreResult calculateMatchScore(ServiceRequest serviceRequest,
                                          PatientPort.PatientDomain patient,
                                          CareStaffPort.CareStaffDomain staff,
                                          MatchingCriteria criteria,
                                          Point patientLocationCoords) {
        
        double score = 0.0;
        StringBuilder rationale = new StringBuilder("Score breakdown: ");
        
        MatchingCriteria.ScoringWeights weights = criteria.getScoringWeights();
        
        // Skill match scoring
        if (serviceRequest.getRequiredSkillIds() != null && !serviceRequest.getRequiredSkillIds().isEmpty()) {
            long matchedSkills = serviceRequest.getRequiredSkillIds().stream()
                    .mapToLong(skillId -> staff.skillIds().contains(skillId) ? 1 : 0)
                    .sum();
            
            if (criteria.getFilters().isRequiredSkillsMustMatchAll() && 
                matchedSkills == serviceRequest.getRequiredSkillIds().size()) {
                score += weights.getSkillMatchAllBonus();
                rationale.append("Skills(+").append(weights.getSkillMatchAllBonus()).append(") ");
            } else {
                double skillBonus = matchedSkills * weights.getSkillMatchBonusPerRequiredSkill();
                score += skillBonus;
                rationale.append("Skills(+").append(skillBonus).append(") ");
            }
        }
        
        // Proximity scoring
        double distance = staff.baseLocation().distance(patientLocationCoords) * 111.0; // Convert to km
        double proximityPenalty = distance * weights.getProximityKmPenalty();
        score += proximityPenalty;
        rationale.append("Proximity(").append(String.format("%.1f", proximityPenalty)).append(") ");
        
        // Geofence bonus (if passed the hard filter)
        score += weights.getGeoServiceAreaBonus();
        rationale.append("Geo(+").append(weights.getGeoServiceAreaBonus()).append(") ");
        
        // Preferred staff bonus
        if (patient.preferredCareStaffIds() != null && 
            patient.preferredCareStaffIds().contains(staff.careStaffId())) {
            score += weights.getPreferredCareStaffBonus();
            rationale.append("Preferred(+").append(weights.getPreferredCareStaffBonus()).append(") ");
        }
        
        // Language match bonus
        if (patient.preferredLanguage() != null && 
            staff.languages().contains(patient.preferredLanguage())) {
            score += weights.getLanguageMatchBonus();
            rationale.append("Language(+").append(weights.getLanguageMatchBonus()).append(") ");
        }
        
        // Experience bonus
        double experienceBonus = staff.experienceYears() * weights.getExperienceLevelBonusPerYear();
        score += experienceBonus;
        rationale.append("Experience(+").append(String.format("%.1f", experienceBonus)).append(") ");
        
        // TODO: Implement refined availability window fit scoring logic
        // TODO: Implement continuity check
        
        rationale.append("= ").append(String.format("%.1f", score));
        
        return new ScoreResult(score, rationale.toString());
    }

    /**
     * Score calculation result.
     */
    private record ScoreResult(double score, String rationale) {}

    @Override
    @Transactional
    public boolean overrideMatch(UUID requestId, OverrideRequestDto overrideDetails, UUID schedulerId) {
        log.info("Processing match override for request: {}", requestId);

        // Fetch and validate service request
        ServiceRequest serviceRequest = serviceRequestPort.findByIdWithRelations(requestId)
                .orElseThrow(() -> ResourceNotFoundException.serviceRequest(requestId.toString()));

        if (!serviceRequest.isPending()) {
            throw MatchingException.invalidRequestStatus(requestId.toString(), serviceRequest.getStatus());
        }

        // TODO: Get schedulerId from authenticated user
        UUID actualSchedulerId = schedulerId != null ? schedulerId : UUID.randomUUID();

        // Record the override
        MatchOverride matchOverride = MatchOverride.create(
                requestId,
                overrideDetails.selectedCareStaffId(),
                actualSchedulerId,
                overrideDetails.reason(),
                overrideDetails.explanation()
        );
        matchOverridePort.save(matchOverride);

        // Create appointment
        // TODO: Refine appointment time calculation based on scheduler input/TimeWindow details
        LocalDateTime appointmentStart = serviceRequest.getTimeWindow().getArrivalWindowStart();
        LocalDateTime appointmentEnd = appointmentStart.plusMinutes(serviceRequest.getTimeWindow().getVisitDurationMinutes());

        Appointment appointment = Appointment.create(
                requestId,
                overrideDetails.selectedCareStaffId(),
                serviceRequest.getPatientId(),
                appointmentStart,
                appointmentEnd,
                actualSchedulerId
        );
        appointmentPort.save(appointment);

        // Update service request status
        serviceRequestPort.updateStatus(requestId, "scheduled");

        log.info("Successfully overrode match for request: {}", requestId);
        return true;
    }

    @Override
    @Transactional
    public BulkAssignmentResult assignBulk(List<UUID> suggestionIds, UUID schedulerId) {
        log.info("Processing bulk assignment for {} suggestions", suggestionIds.size());

        List<MatchSuggestion> suggestions = matchSuggestionPort.findAllById(suggestionIds);
        List<String> errors = new ArrayList<>();
        List<Appointment> appointmentsToSave = new ArrayList<>();
        List<UUID> requestIdsToUpdate = new ArrayList<>();

        // TODO: Get schedulerId from authenticated user
        UUID actualSchedulerId = schedulerId != null ? schedulerId : UUID.randomUUID();

        for (MatchSuggestion suggestion : suggestions) {
            try {
                // Validate suggestion and request status
                ServiceRequest serviceRequest = serviceRequestPort.findByIdWithRelations(suggestion.getServiceRequestId())
                        .orElse(null);

                if (serviceRequest == null) {
                    errors.add("Service request not found for suggestion: " + suggestion.getSuggestionId());
                    continue;
                }

                if (!serviceRequest.isPending()) {
                    errors.add("Service request already processed: " + suggestion.getServiceRequestId());
                    continue;
                }

                // TODO: Implement robust conflict detection for bulk assignments
                
                // Create appointment
                LocalDateTime appointmentStart = suggestion.getSuggestedDateTime();
                LocalDateTime appointmentEnd = appointmentStart.plusMinutes(serviceRequest.getTimeWindow().getVisitDurationMinutes());

                Appointment appointment = Appointment.create(
                        suggestion.getServiceRequestId(),
                        suggestion.getSuggestedCareStaffId(),
                        serviceRequest.getPatientId(),
                        appointmentStart,
                        appointmentEnd,
                        actualSchedulerId
                );

                appointmentsToSave.add(appointment);
                requestIdsToUpdate.add(suggestion.getServiceRequestId());

            } catch (Exception e) {
                errors.add("Error processing suggestion " + suggestion.getSuggestionId() + ": " + e.getMessage());
                log.warn("Error processing suggestion {}: {}", suggestion.getSuggestionId(), e.getMessage());
            }
        }

        // Save appointments and update request statuses
        if (!appointmentsToSave.isEmpty()) {
            appointmentPort.saveAll(appointmentsToSave);
            serviceRequestPort.updateStatus(requestIdsToUpdate, "scheduled");
        }

        BulkAssignmentResult result = new BulkAssignmentResult(
                suggestionIds.size(),
                appointmentsToSave.size(),
                suggestionIds.size() - appointmentsToSave.size(),
                errors
        );

        log.info("Bulk assignment completed: {} successful, {} failed", 
                result.successfulAssignments(), result.failedAssignments());

        return result;
    }

    @Override
    public List<MatchSuggestion> getSuggestions(UUID requestId) {
        log.debug("Retrieving suggestions for request: {}", requestId);
        return matchSuggestionPort.findByServiceRequestId(requestId);
    }
}
