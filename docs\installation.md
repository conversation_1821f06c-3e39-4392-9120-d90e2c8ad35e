# Installation Guide - CareStaff Matching Service

## Overview

This guide provides detailed instructions for installing and setting up the CareStaff Matching Service in various environments.

## Prerequisites

### System Requirements

- **Java**: 17 or higher (OpenJDK or Oracle JDK)
- **Maven**: 3.6 or higher
- **Docker**: 20.10 or higher (for containerized deployment)
- **Docker Compose**: 1.29 or higher
- **Memory**: Minimum 4GB RAM, recommended 8GB
- **Storage**: Minimum 10GB free space

### Database Requirements

- **PostgreSQL**: 13 or higher
- **PostGIS**: 3.1 or higher (for spatial operations)

## Installation Methods

### Method 1: Docker Compose (Recommended)

This is the easiest way to get started with all dependencies included.

#### 1. Clone the Repository

```bash
git clone <repository-url>
cd caxl-optaplanner-assignment-service
```

#### 2. Start All Services

```bash
# Start PostgreSQL with PostGIS
docker-compose -f docker-compose-carestaff.yml up postgres-carestaff -d

# Wait for database to be ready (about 30 seconds)
docker-compose -f docker-compose-carestaff.yml logs postgres-carestaff

# Start the complete stack
docker-compose -f docker-compose-carestaff.yml up
```

#### 3. Verify Installation

```bash
# Check application health
curl http://localhost:8081/actuator/health

# Test the API
curl -X POST http://localhost:8081/api/v1/service-requests/{request_id}/suggest-matches
```

### Method 2: Local Development Setup

For development and customization.

#### 1. Install Java 17

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install openjdk-17-jdk
```

**macOS (using Homebrew):**
```bash
brew install openjdk@17
```

**Windows:**
Download and install from [Oracle JDK](https://www.oracle.com/java/technologies/downloads/) or [OpenJDK](https://adoptium.net/)

#### 2. Install Maven

**Ubuntu/Debian:**
```bash
sudo apt install maven
```

**macOS (using Homebrew):**
```bash
brew install maven
```

**Windows:**
Download from [Apache Maven](https://maven.apache.org/download.cgi)

#### 3. Install PostgreSQL with PostGIS

**Using Docker (Recommended):**
```bash
docker run --name postgres-carestaff \
  -e POSTGRES_DB=homecare_scheduling \
  -e POSTGRES_USER=caxl_user \
  -e POSTGRES_PASSWORD=caxl_password \
  -p 5432:5432 \
  -d postgis/postgis:16-3.4-alpine
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib postgis
sudo -u postgres createdb homecare_scheduling
sudo -u postgres psql -c "CREATE EXTENSION postgis;" homecare_scheduling
```

#### 4. Configure Database

Create user and set permissions:

```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create user and database
CREATE USER caxl_user WITH PASSWORD 'caxl_password';
CREATE DATABASE homecare_scheduling OWNER caxl_user;

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE homecare_scheduling TO caxl_user;

-- Connect to the database and enable PostGIS
\c homecare_scheduling
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

#### 5. Build and Run Application

```bash
# Clone the repository
git clone <repository-url>
cd caxl-optaplanner-assignment-service

# Build the application
mvn clean compile

# Run the application
mvn spring-boot:run
```

#### 6. Initialize Database Schema

The application will automatically create the database schema on first run. You can also run the initialization script manually:

```bash
# Run the database initialization script
psql -h localhost -U caxl_user -d homecare_scheduling -f docker/init-scripts-carestaff/01-init-carestaff-schema.sql
```

### Method 3: Production Deployment

For production environments with external database.

#### 1. Prepare Production Database

Set up PostgreSQL with PostGIS on your production database server:

```sql
-- Create database and user
CREATE DATABASE homecare_scheduling;
CREATE USER caxl_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE homecare_scheduling TO caxl_user;

-- Enable extensions
\c homecare_scheduling
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

#### 2. Build Production JAR

```bash
# Build the application
mvn clean package -DskipTests

# The JAR will be in target/carestaff-matching-service-1.0.0.jar
```

#### 3. Configure Production Settings

Create `application-prod.yml`:

```yaml
spring:
  datasource:
    url: *******************************************************
    username: caxl_user
    password: ${DB_PASSWORD}
  jpa:
    hibernate:
      ddl-auto: validate  # Use 'update' for first deployment
    show-sql: false

logging:
  level:
    root: INFO
    com.caxl.carestaff: INFO

server:
  port: 8080

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

#### 4. Deploy and Run

```bash
# Set environment variables
export DB_PASSWORD=your_secure_password
export SPRING_PROFILES_ACTIVE=prod

# Run the application
java -jar target/carestaff-matching-service-1.0.0.jar
```

## Configuration

### Environment Variables

Key environment variables for configuration:

```bash
# Database Configuration
SPRING_DATASOURCE_URL=****************************************************
SPRING_DATASOURCE_USERNAME=caxl_user
SPRING_DATASOURCE_PASSWORD=caxl_password

# Application Configuration
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080

# Logging Configuration
LOGGING_LEVEL_COM_CAXL_CARESTAFF=INFO
LOGGING_LEVEL_ROOT=INFO

# JVM Configuration
JAVA_OPTS="-Xmx2g -Xms1g"
```

### Application Properties

Key configuration options in `application.yml`:

```yaml
carestaff:
  matching:
    default-search-radius-km: 50.0
    min-score-threshold: 0.0
    overlap-threshold-minutes: 1
    geofence-strict-containment: true
```

## Verification

### Health Checks

```bash
# Application health
curl http://localhost:8080/actuator/health

# Database connectivity
curl http://localhost:8080/actuator/health/db

# Application info
curl http://localhost:8080/actuator/info
```

### API Testing

```bash
# Test match suggestion endpoint
curl -X POST http://localhost:8080/api/v1/service-requests/550e8400-e29b-41d4-a716-446655440001/suggest-matches

# Test configuration retrieval
curl http://localhost:8080/api/v1/configuration/active
```

## Troubleshooting

### Common Issues

#### Database Connection Failed

**Symptoms:** Application fails to start with database connection errors

**Solutions:**
1. Verify PostgreSQL is running: `docker ps` or `systemctl status postgresql`
2. Check connection parameters in `application.yml`
3. Verify user permissions: `psql -h localhost -U caxl_user -d homecare_scheduling`

#### PostGIS Extension Missing

**Symptoms:** Spatial queries fail with "function ST_DWithin does not exist"

**Solutions:**
1. Connect to database: `psql -h localhost -U caxl_user -d homecare_scheduling`
2. Enable PostGIS: `CREATE EXTENSION IF NOT EXISTS postgis;`
3. Verify installation: `SELECT PostGIS_Version();`

#### Port Already in Use

**Symptoms:** Application fails to start with "Port 8080 already in use"

**Solutions:**
1. Change port in `application.yml`: `server.port: 8081`
2. Kill existing process: `lsof -ti:8080 | xargs kill -9`
3. Use different port: `java -jar app.jar --server.port=8081`

#### Insufficient Memory

**Symptoms:** Application crashes with OutOfMemoryError

**Solutions:**
1. Increase JVM heap size: `java -Xmx4g -jar app.jar`
2. Monitor memory usage: `jstat -gc <pid>`
3. Optimize database queries and caching

### Log Analysis

Check application logs for detailed error information:

```bash
# View application logs
tail -f logs/application.log

# View specific logger
grep "com.caxl.carestaff" logs/application.log

# View database queries
grep "org.hibernate.SQL" logs/application.log
```

## Performance Tuning

### Database Optimization

```sql
-- Create indexes for performance
CREATE INDEX CONCURRENTLY idx_carestaff_location ON caresstaff_pe USING GIST(location_coordinates);
CREATE INDEX CONCURRENTLY idx_patient_location ON patient USING GIST(location_coordinates);
CREATE INDEX CONCURRENTLY idx_appointment_staff_time ON appointment(carestaff_id, scheduled_start_time);
```

### JVM Tuning

```bash
# Production JVM settings
JAVA_OPTS="-Xmx4g -Xms2g -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxGCPauseMillis=200"
```

## Security Considerations

### Database Security

1. Use strong passwords for database users
2. Restrict database access to application servers only
3. Enable SSL/TLS for database connections
4. Regular security updates for PostgreSQL

### Application Security

1. Use HTTPS in production
2. Implement proper authentication and authorization
3. Regular security updates for dependencies
4. Monitor for security vulnerabilities

## Backup and Recovery

### Database Backup

```bash
# Create backup
pg_dump -h localhost -U caxl_user homecare_scheduling > backup.sql

# Restore backup
psql -h localhost -U caxl_user homecare_scheduling < backup.sql
```

### Application Backup

1. Backup application configuration files
2. Backup custom matching configurations
3. Document deployment procedures
4. Test recovery procedures regularly

## Support

For installation support:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review application logs for error details
3. Verify all prerequisites are met
4. Create an issue with installation details
