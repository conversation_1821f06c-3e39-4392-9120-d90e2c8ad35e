package com.caxl.assignment.application.services.assignment;

import ai.timefold.solver.core.api.solver.Solver;
import ai.timefold.solver.core.api.solver.SolverFactory;
import ai.timefold.solver.core.config.solver.SolverConfig;
import com.caxl.assignment.domain.constraints.assignment.DynamicMultiLevelConstraintProvider;
import com.caxl.assignment.domain.models.Clinician;
import com.caxl.assignment.domain.models.assignment.*;
import com.caxl.assignment.domain.models.healthcare.CareStaff;
import com.caxl.assignment.domain.services.assignment.DynamicRuleFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Main service for dynamic multi-level clinician assignment system.
 * Orchestrates the three-level filtering process with dynamic rule evaluation and relaxation strategies.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DynamicMultiLevelAssignmentService {

    private final DynamicRuleFactory ruleFactory;
    private final SolverFactory<MultiLevelAssignmentSolution> solverFactory;

    /**
     * Process assignment requests through multi-level filtering system.
     * 
     * @param requests List of assignment requests to process
     * @param clinicians Available clinicians
     * @param careStaff Available care staff
     * @param dynamicRules Dynamic rules loaded from configuration
     * @return Optimized assignment solution
     */
    public MultiLevelAssignmentSolution processAssignments(
            List<ClinicianAssignmentRequest> requests,
            List<Clinician> clinicians,
            List<CareStaff> careStaff,
            List<DynamicRule> dynamicRules) {
        
        log.info("Starting multi-level assignment processing for {} requests", requests.size());
        
        // Initialize rules with evaluation functions
        List<DynamicRule> processedRules = initializeRules(dynamicRules);
        
        // Create initial solution
        MultiLevelAssignmentSolution solution = createInitialSolution(
            requests, clinicians, careStaff, processedRules);
        
        // Level 1: Mandatory Assignment Criteria
        solution = processLevel1Assignments(solution);
        log.info("Level 1 processing completed. Success rate: {:.1f}%", 
                solution.getSuccessRateForLevel(1) * 100);
        
        // Level 2: Resource Capacity and Load Management
        if (solution.needsLevel3Processing()) {
            solution = processLevel2Assignments(solution);
            log.info("Level 2 processing completed. Success rate: {:.1f}%", 
                    solution.getSuccessRateForLevel(2) * 100);
        }
        
        // Level 3: Intelligent Alternative Assignment Pathways
        if (solution.needsLevel3Processing()) {
            solution = processLevel3Assignments(solution);
            log.info("Level 3 processing completed. Success rate: {:.1f}%", 
                    solution.getSuccessRateForLevel(3) * 100);
        }
        
        // Update final statistics
        solution.updateStatistics();
        
        log.info("Multi-level assignment processing completed. Overall completion rate: {:.1f}%",
                solution.getCompletionRate() * 100);
        
        return solution;
    }

    /**
     * Initialize dynamic rules with evaluation functions.
     */
    private List<DynamicRule> initializeRules(List<DynamicRule> dynamicRules) {
        return dynamicRules.stream()
                .map(ruleFactory::createRule)
                .collect(Collectors.toList());
    }

    /**
     * Create initial solution with unassigned assignments.
     */
    private MultiLevelAssignmentSolution createInitialSolution(
            List<ClinicianAssignmentRequest> requests,
            List<Clinician> clinicians,
            List<CareStaff> careStaff,
            List<DynamicRule> processedRules) {
        
        // Create assignments for each request
        List<DynamicClinicianAssignment> assignments = requests.stream()
                .map(request -> DynamicClinicianAssignment.builder()
                        .id(UUID.randomUUID().toString())
                        .assignmentRequest(request)
                        .processingLevel(1)
                        .build())
                .collect(Collectors.toList());
        
        // Categorize rules by level
        List<DynamicRule> level1Rules = processedRules.stream()
                .filter(DynamicRule::isLevel1Rule)
                .collect(Collectors.toList());
        
        List<DynamicRule> level2Rules = processedRules.stream()
                .filter(DynamicRule::isLevel2Rule)
                .collect(Collectors.toList());
        
        List<DynamicRule> level3Rules = processedRules.stream()
                .filter(DynamicRule::isLevel3Rule)
                .collect(Collectors.toList());
        
        return MultiLevelAssignmentSolution.builder()
                .assignments(assignments)
                .clinicians(clinicians)
                .careStaff(careStaff)
                .assignmentRequests(requests)
                .dynamicRules(processedRules)
                .level1Rules(level1Rules)
                .level2Rules(level2Rules)
                .level3Rules(level3Rules)
                .currentProcessingLevel(1)
                .skillHierarchy(createSkillHierarchy())
                .distanceMatrix(createDistanceMatrix())
                .clinicianWorkloads(initializeWorkloads(clinicians, careStaff))
                .assignmentHistory(createAssignmentHistory())
                .relaxationConfig(createDefaultRelaxationConfig())
                .build();
    }

    /**
     * Process Level 1 assignments (Mandatory Assignment Criteria).
     */
    private MultiLevelAssignmentSolution processLevel1Assignments(MultiLevelAssignmentSolution solution) {
        log.info("Processing Level 1 assignments (Mandatory Assignment Criteria)");
        
        // Configure solver for Level 1 (strict constraints)
        SolverConfig level1Config = createLevel1SolverConfig();
        Solver<MultiLevelAssignmentSolution> solver = SolverFactory.create(level1Config).buildSolver();
        
        // Solve with Level 1 constraints only
        MultiLevelAssignmentSolution level1Solution = solver.solve(solution);
        level1Solution.setCurrentProcessingLevel(1);
        
        return level1Solution;
    }

    /**
     * Process Level 2 assignments (Resource Capacity and Load Management).
     */
    private MultiLevelAssignmentSolution processLevel2Assignments(MultiLevelAssignmentSolution solution) {
        log.info("Processing Level 2 assignments (Resource Capacity and Load Management)");
        
        // Create assignments for unassigned requests at Level 2
        List<DynamicClinicianAssignment> level2Assignments = solution.getUnassignedRequests().stream()
                .map(request -> DynamicClinicianAssignment.builder()
                        .id(UUID.randomUUID().toString())
                        .assignmentRequest(request)
                        .processingLevel(2)
                        .build())
                .collect(Collectors.toList());
        
        // Add Level 2 assignments to existing solution
        List<DynamicClinicianAssignment> allAssignments = new ArrayList<>(solution.getAssignments());
        allAssignments.addAll(level2Assignments);
        solution.setAssignments(allAssignments);
        solution.setCurrentProcessingLevel(2);
        
        // Configure solver for Level 2 (capacity constraints)
        SolverConfig level2Config = createLevel2SolverConfig();
        Solver<MultiLevelAssignmentSolution> solver = SolverFactory.create(level2Config).buildSolver();
        
        return solver.solve(solution);
    }

    /**
     * Process Level 3 assignments (Intelligent Alternative Assignment Pathways).
     */
    private MultiLevelAssignmentSolution processLevel3Assignments(MultiLevelAssignmentSolution solution) {
        log.info("Processing Level 3 assignments (Intelligent Alternative Assignment Pathways)");
        
        // Apply relaxation strategies for unassigned requests
        List<DynamicClinicianAssignment> level3Assignments = solution.getUnassignedRequests().stream()
                .map(request -> createRelaxedAssignment(request, solution.getRelaxationConfig()))
                .collect(Collectors.toList());
        
        // Add Level 3 assignments to existing solution
        List<DynamicClinicianAssignment> allAssignments = new ArrayList<>(solution.getAssignments());
        allAssignments.addAll(level3Assignments);
        solution.setAssignments(allAssignments);
        solution.setCurrentProcessingLevel(3);
        
        // Configure solver for Level 3 (relaxed constraints)
        SolverConfig level3Config = createLevel3SolverConfig();
        Solver<MultiLevelAssignmentSolution> solver = SolverFactory.create(level3Config).buildSolver();
        
        return solver.solve(solution);
    }

    /**
     * Create relaxed assignment for Level 3 processing.
     */
    private DynamicClinicianAssignment createRelaxedAssignment(
            ClinicianAssignmentRequest request,
            MultiLevelAssignmentSolution.RelaxationConfiguration relaxationConfig) {
        
        DynamicClinicianAssignment.RelaxationDetails relaxationDetails = 
            DynamicClinicianAssignment.RelaxationDetails.builder()
                .skillSubstitutionUsed(relaxationConfig.isEnableSkillSubstitution())
                .extendedRadiusUsed(relaxationConfig.isEnableExtendedRadius())
                .overtimeAssignmentUsed(relaxationConfig.isEnableOvertimeAssignment())
                .visitReschedulingUsed(relaxationConfig.isEnableVisitRescheduling())
                .relaxationReason("Level 3 alternative pathway")
                .relaxationPenalty(calculateRelaxationPenalty(relaxationConfig))
                .build();
        
        return DynamicClinicianAssignment.builder()
                .id(UUID.randomUUID().toString())
                .assignmentRequest(request)
                .processingLevel(3)
                .relaxationApplied(true)
                .relaxationDetails(relaxationDetails)
                .build();
    }

    /**
     * Calculate relaxation penalty based on applied strategies.
     */
    private double calculateRelaxationPenalty(MultiLevelAssignmentSolution.RelaxationConfiguration config) {
        double penalty = 0.0;
        
        if (config.isEnableSkillSubstitution()) {
            penalty += config.getSkillSubstitutionPenalty();
        }
        if (config.isEnableExtendedRadius()) {
            penalty += config.getExtendedRadiusPenalty();
        }
        if (config.isEnableOvertimeAssignment()) {
            penalty += config.getOvertimePenalty();
        }
        if (config.isEnableVisitRescheduling()) {
            penalty += config.getReschedulePenalty();
        }
        
        return penalty;
    }

    /**
     * Create solver configuration for Level 1 processing.
     */
    private SolverConfig createLevel1SolverConfig() {
        return new SolverConfig()
                .withSolutionClass(MultiLevelAssignmentSolution.class)
                .withEntityClasses(DynamicClinicianAssignment.class)
                .withConstraintProviderClass(DynamicMultiLevelConstraintProvider.class)
                .withTerminationSpentLimit(Duration.ofSeconds(30));
    }

    /**
     * Create solver configuration for Level 2 processing.
     */
    private SolverConfig createLevel2SolverConfig() {
        return new SolverConfig()
                .withSolutionClass(MultiLevelAssignmentSolution.class)
                .withEntityClasses(DynamicClinicianAssignment.class)
                .withConstraintProviderClass(DynamicMultiLevelConstraintProvider.class)
                .withTerminationSpentLimit(Duration.ofSeconds(45));
    }

    /**
     * Create solver configuration for Level 3 processing.
     */
    private SolverConfig createLevel3SolverConfig() {
        return new SolverConfig()
                .withSolutionClass(MultiLevelAssignmentSolution.class)
                .withEntityClasses(DynamicClinicianAssignment.class)
                .withConstraintProviderClass(DynamicMultiLevelConstraintProvider.class)
                .withTerminationSpentLimit(Duration.ofSeconds(60));
    }

    // Helper methods for initialization (placeholder implementations)
    
    private Map<String, Set<String>> createSkillHierarchy() {
        // Implementation would create skill hierarchy from configuration
        return new HashMap<>();
    }

    private Map<String, Map<String, Double>> createDistanceMatrix() {
        // Implementation would create distance matrix from geographic data
        return new HashMap<>();
    }

    private Map<String, Integer> initializeWorkloads(List<Clinician> clinicians, List<CareStaff> careStaff) {
        // Implementation would initialize current workload tracking
        return new HashMap<>();
    }

    private Map<String, List<String>> createAssignmentHistory() {
        // Implementation would load historical assignment data
        return new HashMap<>();
    }

    private MultiLevelAssignmentSolution.RelaxationConfiguration createDefaultRelaxationConfig() {
        return MultiLevelAssignmentSolution.RelaxationConfiguration.builder()
                .enableSkillSubstitution(true)
                .enableExtendedRadius(true)
                .enableOvertimeAssignment(false)
                .enableVisitRescheduling(true)
                .maxExtendedRadiusKm(50.0)
                .maxOvertimeHours(2)
                .rescheduleWindowHours(24)
                .skillSubstitutionPenalty(0.5)
                .extendedRadiusPenalty(0.3)
                .overtimePenalty(0.8)
                .reschedulePenalty(0.2)
                .build();
    }
}
