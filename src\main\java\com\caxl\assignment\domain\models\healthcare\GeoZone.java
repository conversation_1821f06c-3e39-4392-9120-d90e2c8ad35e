package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * GeoZone represents a geographical operating zone for care staff assignments.
 * Used for geofencing-based clinician assignment within specific areas.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GeoZone {

    @NotBlank(message = "Zone ID is required")
    @JsonProperty("zone_id")
    private String zoneId;

    @NotBlank(message = "Zone name is required")
    @JsonProperty("zone_name")
    private String zoneName;

    @NotBlank(message = "State is required")
    @JsonProperty("state")
    private String state;

    @Valid
    @NotNull(message = "Zone boundary points are required")
    @JsonProperty("boundary_points")
    private List<GeoPoint> boundaryPoints;

    @JsonProperty("zone_type")
    private ZoneType zoneType;

    @JsonProperty("coverage_radius_miles")
    private Double coverageRadiusMiles;

    @JsonProperty("population_density")
    private PopulationDensity populationDensity;

    /**
     * Geographic point with latitude and longitude.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class GeoPoint {
        
        @NotNull(message = "Latitude is required")
        @JsonProperty("latitude")
        private Double latitude;

        @NotNull(message = "Longitude is required")
        @JsonProperty("longitude")
        private Double longitude;

        @JsonProperty("address")
        private String address;

        @JsonProperty("zip_code")
        private String zipCode;

        /**
         * Calculate distance to another point in miles.
         */
        public double distanceTo(GeoPoint other) {
            final int R = 3959; // Earth's radius in miles
            double latDistance = Math.toRadians(other.latitude - this.latitude);
            double lonDistance = Math.toRadians(other.longitude - this.longitude);
            double a = Math.sin(latDistance / 2) * Math.sin(latDistance / 2)
                    + Math.cos(Math.toRadians(this.latitude)) * Math.cos(Math.toRadians(other.latitude))
                    * Math.sin(lonDistance / 2) * Math.sin(lonDistance / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            return R * c;
        }
    }

    /**
     * Type of geographical zone.
     */
    public enum ZoneType {
        URBAN,
        SUBURBAN,
        RURAL,
        MIXED
    }

    /**
     * Population density classification.
     */
    public enum PopulationDensity {
        HIGH,
        MEDIUM,
        LOW
    }

    /**
     * Check if a point is within this zone's boundary.
     * Uses ray casting algorithm for polygon containment.
     */
    public boolean contains(GeoPoint point) {
        if (boundaryPoints == null || boundaryPoints.size() < 3) {
            return false;
        }

        int intersections = 0;
        for (int i = 0; i < boundaryPoints.size(); i++) {
            GeoPoint p1 = boundaryPoints.get(i);
            GeoPoint p2 = boundaryPoints.get((i + 1) % boundaryPoints.size());

            if (rayIntersectsSegment(point, p1, p2)) {
                intersections++;
            }
        }

        return (intersections % 2) == 1;
    }

    private boolean rayIntersectsSegment(GeoPoint point, GeoPoint p1, GeoPoint p2) {
        double px = point.getLongitude();
        double py = point.getLatitude();
        double x1 = p1.getLongitude();
        double y1 = p1.getLatitude();
        double x2 = p2.getLongitude();
        double y2 = p2.getLatitude();

        if (y1 > py != y2 > py) {
            double intersectX = (x2 - x1) * (py - y1) / (y2 - y1) + x1;
            if (px < intersectX) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get the center point of this zone.
     */
    public GeoPoint getCenterPoint() {
        if (boundaryPoints == null || boundaryPoints.isEmpty()) {
            return null;
        }

        double sumLat = 0;
        double sumLon = 0;
        for (GeoPoint point : boundaryPoints) {
            sumLat += point.getLatitude();
            sumLon += point.getLongitude();
        }

        return GeoPoint.builder()
                .latitude(sumLat / boundaryPoints.size())
                .longitude(sumLon / boundaryPoints.size())
                .build();
    }
}
