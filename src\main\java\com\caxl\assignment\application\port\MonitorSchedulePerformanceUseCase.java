package com.caxl.assignment.application.port;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Use case for monitoring and analyzing scheduling performance.
 * Provides insights for continuous improvement of the scheduling system.
 */
public interface MonitorSchedulePerformanceUseCase {

    /**
     * Get performance metrics for a specific date.
     * 
     * @param schedulingDate Date to analyze
     * @return Performance metrics
     */
    PerformanceMetrics getPerformanceMetrics(LocalDate schedulingDate);

    /**
     * Get performance trends over a date range.
     * 
     * @param startDate Start of analysis period
     * @param endDate End of analysis period
     * @return Performance trends
     */
    PerformanceTrends getPerformanceTrends(LocalDate startDate, LocalDate endDate);

    /**
     * Get real-time scheduling system health status.
     * 
     * @return System health status
     */
    SystemHealthStatus getSystemHealthStatus();

    /**
     * Generate performance report for management.
     * 
     * @param reportPeriod Period for the report
     * @param reportType Type of report (DAILY, WEEKLY, MONTHLY)
     * @return Performance report
     */
    PerformanceReport generatePerformanceReport(
        DateRange reportPeriod,
        ReportType reportType
    );

    /**
     * Get optimization recommendations based on performance analysis.
     * 
     * @param analysisDate Date to analyze
     * @return Optimization recommendations
     */
    List<OptimizationRecommendation> getOptimizationRecommendations(LocalDate analysisDate);

    /**
     * Date range for analysis.
     */
    record DateRange(LocalDate startDate, LocalDate endDate) {}

    /**
     * Report types.
     */
    enum ReportType {
        DAILY, WEEKLY, MONTHLY, QUARTERLY
    }

    /**
     * Performance metrics for a specific period.
     */
    record PerformanceMetrics(
        LocalDate date,
        double assignmentRate,
        double averageOptimizationScore,
        long averageSolvingTimeMs,
        int totalPatients,
        int totalClinicians,
        int successfulAssignments,
        int failedAssignments,
        double averageDistance,
        int constraintViolations,
        Map<String, Double> rulePerformance
    ) {}

    /**
     * Performance trends over time.
     */
    record PerformanceTrends(
        DateRange period,
        List<PerformanceMetrics> dailyMetrics,
        TrendAnalysis assignmentRateTrend,
        TrendAnalysis scoreTrend,
        TrendAnalysis solvingTimeTrend,
        List<String> insights
    ) {}

    /**
     * Trend analysis for a specific metric.
     */
    record TrendAnalysis(
        String metric,
        double currentValue,
        double previousValue,
        double changePercentage,
        String trend, // IMPROVING, DECLINING, STABLE
        String interpretation
    ) {}

    /**
     * System health status.
     */
    record SystemHealthStatus(
        LocalDateTime timestamp,
        String overallStatus, // HEALTHY, WARNING, CRITICAL
        double systemLoad,
        long averageResponseTimeMs,
        int activeSchedulingJobs,
        int queuedRequests,
        List<HealthIssue> issues
    ) {}

    /**
     * Health issue details.
     */
    record HealthIssue(
        String component,
        String severity,
        String description,
        String recommendation
    ) {}

    /**
     * Performance report.
     */
    record PerformanceReport(
        DateRange period,
        ReportType type,
        PerformanceMetrics summary,
        List<PerformanceMetrics> detailedMetrics,
        List<String> keyInsights,
        List<OptimizationRecommendation> recommendations,
        LocalDateTime generatedAt
    ) {}

    /**
     * Optimization recommendation.
     */
    record OptimizationRecommendation(
        String category,
        String title,
        String description,
        String impact,
        String priority,
        String implementation
    ) {}
}
