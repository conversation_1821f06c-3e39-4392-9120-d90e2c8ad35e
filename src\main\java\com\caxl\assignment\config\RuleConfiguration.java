package com.caxl.assignment.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.caxl.assignment.domain.models.Rule;
import com.caxl.assignment.infrastructure.adapters.RuleRepositoryAdapter;

/**
 * Configuration for loading rules and skill hierarchies.
 */
@Configuration
@RequiredArgsConstructor
public class RuleConfiguration {
    
    private final AssignmentProperties properties;
    private final ObjectMapper objectMapper;    @Bean
    public Map<String, Map<String, Integer>> skillHierarchies() throws IOException {
        Resource resource = new ClassPathResource(properties.getSkillHierarchiesFile());
        TypeReference<Map<String, Map<String, Integer>>> typeRef = 
            new TypeReference<Map<String, Map<String, Integer>>>() {};
        return objectMapper.readValue(resource.getInputStream(), typeRef);
    }

    @Bean
    public RuleRepositoryAdapter ruleRepository() throws IOException {
        Resource resource = new ClassPathResource(properties.getRulesFile());
        TypeReference<Map<String, List<Rule>>> typeRef = 
            new TypeReference<Map<String, List<Rule>>>() {};
        Map<String, List<Rule>> rulesMap = objectMapper.readValue(resource.getInputStream(), typeRef);
        return new RuleRepositoryAdapter(rulesMap.get("rules"));
    }
}
