package com.caxl.assignment.domain.models.healthcare;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Extended HomePatient domain model for US homecare scheduling.
 * Contains homecare-specific medical and compliance requirements.
 * 
 * REFACTORING NOTE: All nested classes follow the same pattern:
 * @Data @Builder @NoArgsConstructor @AllArgsConstructor @JsonIgnoreProperties(ignoreUnknown = true)
 * This pattern has been identified for consolidation to reduce duplication.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class HomePatient {

    // Basic patient information
    @NotBlank(message = "Patient ID is required")
    @JsonProperty("patient_id")
    private String id;
    
    @NotBlank(message = "Patient name is required")
    @JsonProperty("patient_name")
    private String name;
    
    @JsonProperty("medical_record_number")
    private String mrn;

    /**
     * Patient location and address information.
     */
    @Valid
    @NotNull(message = "Location information is required")
    @JsonProperty("location")
    private PatientLocation location;

    /**
     * Medical condition details and care requirements.
     */
    @Valid
    @NotNull(message = "Medical condition information is required")
    @JsonProperty("medical_condition")
    private MedicalCondition medicalCondition;

    /**
     * Insurance and payment information.
     */
    @Valid
    @NotNull(message = "Insurance information is required")
    @JsonProperty("insurance_info")
    private InsuranceInfo insuranceInfo;

    /**
     * Care plan and physician orders.
     */
    @Valid
    @NotNull(message = "Care plan is required")
    @JsonProperty("care_plan")
    private CarePlan carePlan;

    /**
     * Home environment and safety assessment.
     */
    @Valid
    @JsonProperty("home_environment")
    private HomeEnvironment homeEnvironment;

    /**
     * Emergency procedures and contacts.
     */
    @Valid
    @NotNull(message = "Emergency procedures are required")
    @JsonProperty("emergency_procedures")
    private EmergencyProcedures emergencyProcedures;

    /**
     * State-specific compliance requirements.
     */
    @Valid
    @JsonProperty("state_compliance")
    private StateCompliance stateCompliance;    // === NESTED CLASSES FOR US HOMECARE SPECIFICS ===

    /**
     * Medical condition details and care requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MedicalCondition {
        
        @NotNull(message = "Primary diagnosis is required")
        @JsonProperty("primary_diagnosis")
        private List<String> primaryDiagnosis; // ICD-10 codes

        @JsonProperty("secondary_diagnosis")
        private List<String> secondaryDiagnosis;

        @JsonProperty("chronic_conditions")
        private List<String> chronicConditions;

        @JsonProperty("functional_limitations")
        private List<String> functionalLimitations;

        @JsonProperty("cognitive_status")
        private CognitiveStatus cognitiveStatus;

        @JsonProperty("mobility_status")
        private MobilityStatus mobilityStatus;

        @JsonProperty("infection_precautions")
        private List<String> infectionPrecautions;

        @JsonProperty("isolation_requirements")
        @Builder.Default
        private boolean isolationRequirements = false;

        @Min(value = 1, message = "Acuity level must be at least 1")
        @Max(value = 5, message = "Acuity level must be at most 5")
        @JsonProperty("acuity_level")
        @Builder.Default
        private int acuityLevel = 1;

        @JsonProperty("fall_risk")
        @Builder.Default
        private boolean fallRisk = false;

        @JsonProperty("medication_management_needed")
        @Builder.Default
        private boolean medicationManagementNeeded = false;

        @JsonProperty("wound_care_needed")
        @Builder.Default
        private boolean woundCareNeeded = false;

        @JsonProperty("iv_therapy_needed")
        @Builder.Default
        private boolean ivTherapyNeeded = false;
    }

    /**
     * Insurance and payment information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InsuranceInfo {
        
        @NotBlank(message = "Primary insurance is required")
        @JsonProperty("primary_insurance")
        private String primaryInsurance;

        @JsonProperty("secondary_insurance")
        private String secondaryInsurance;

        @JsonProperty("medicare_beneficiary")
        @Builder.Default
        private boolean medicareBeneficiary = false;

        @JsonProperty("medicaid_beneficiary")
        @Builder.Default
        private boolean medicaidBeneficiary = false;

        @JsonProperty("insurance_authorization_number")
        private String insuranceAuthorizationNumber;

        @JsonProperty("authorized_visits")
        private Integer authorizedVisits;

        @JsonProperty("authorization_start_date")
        private LocalDate authorizationStartDate;

        @JsonProperty("authorization_end_date")
        private LocalDate authorizationEndDate;

        @JsonProperty("copay_amount")
        private Double copayAmount;

        @JsonProperty("private_pay")
        @Builder.Default
        private boolean privatePay = false;
    }

    /**
     * Care plan and physician orders.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CarePlan {
        
        @NotBlank(message = "Physician name is required")
        @JsonProperty("ordering_physician")
        private String orderingPhysician;

        @JsonProperty("physician_phone")
        private String physicianPhone;

        @JsonProperty("care_plan_start_date")
        private LocalDate carePlanStartDate;

        @JsonProperty("care_plan_end_date")
        private LocalDate carePlanEndDate;

        @JsonProperty("skilled_nursing_frequency")
        private String skilledNursingFrequency; // e.g., "3x/week"

        @JsonProperty("therapy_frequency")
        private Map<String, String> therapyFrequency; // therapy type -> frequency

        @JsonProperty("aide_frequency")
        private String aideFrequency;

        @JsonProperty("visit_duration_minutes")
        @Builder.Default
        private int visitDurationMinutes = 60;

        @JsonProperty("goals_and_objectives")
        private List<String> goalsAndObjectives;

        @JsonProperty("special_instructions")
        private List<String> specialInstructions;

        @JsonProperty("medication_reconciliation_needed")
        @Builder.Default
        private boolean medicationReconciliationNeeded = false;

        @JsonProperty("discharge_planning_needed")
        @Builder.Default
        private boolean dischargePlanningNeeded = false;
    }

    /**
     * Home environment and safety assessment.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HomeEnvironment {
        
        @JsonProperty("home_safety_assessment_date")
        private LocalDate homeSafetyAssessmentDate;

        @JsonProperty("accessibility_features")
        private List<String> accessibilityFeatures;

        @JsonProperty("safety_concerns")
        private List<String> safetyConcerns;

        @JsonProperty("pet_presence")
        @Builder.Default
        private boolean petPresence = false;

        @JsonProperty("pet_restrictions")
        private List<String> petRestrictions;

        @JsonProperty("smoking_household")
        @Builder.Default
        private boolean smokingHousehold = false;

        @JsonProperty("key_access_method")
        private String keyAccessMethod; // lockbox, family member, etc.

        @JsonProperty("parking_availability")
        @Builder.Default
        private boolean parkingAvailability = true;

        @JsonProperty("building_access_codes")
        private String buildingAccessCodes;

        @JsonProperty("special_entry_instructions")
        private String specialEntryInstructions;
    }

    /**
     * Emergency procedures and contacts.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EmergencyProcedures {
        
        @NotNull(message = "Emergency contacts are required")
        @JsonProperty("emergency_contacts")
        private List<EmergencyContact> emergencyContacts;

        @JsonProperty("hospital_preference")
        private String hospitalPreference;

        @JsonProperty("advance_directives")
        @Builder.Default
        private boolean advanceDirectives = false;

        @JsonProperty("dnr_order")
        @Builder.Default
        private boolean dnrOrder = false;

        @JsonProperty("emergency_medication_list")
        private List<String> emergencyMedicationList;

        @JsonProperty("allergies")
        private List<String> allergies;

        @JsonProperty("emergency_procedures")
        private List<String> emergencyProceduresList;
    }

    /**
     * Emergency contact information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EmergencyContact {
        
        @NotBlank(message = "Emergency contact name is required")
        @JsonProperty("name")
        private String name;

        @NotBlank(message = "Emergency contact phone is required")
        @JsonProperty("phone")
        private String phone;

        @NotBlank(message = "Relationship is required")
        @JsonProperty("relationship")
        private String relationship;

        @JsonProperty("alternate_phone")
        private String alternatePhone;

        @JsonProperty("can_make_medical_decisions")
        @Builder.Default
        private boolean canMakeMedicalDecisions = false;

        @Min(value = 1, message = "Priority must be at least 1")
        @JsonProperty("priority")
        @Builder.Default
        private int priority = 1;
    }

    /**
     * State-specific compliance requirements.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StateCompliance {
        
        @NotBlank(message = "Service state is required")
        @JsonProperty("service_state")
        private String serviceState;

        @JsonProperty("state_specific_requirements")
        private Map<String, Object> stateSpecificRequirements;

        @JsonProperty("regulatory_reporting_required")
        @Builder.Default
        private boolean regulatoryReportingRequired = false;

        @JsonProperty("oasis_assessment_required")
        @Builder.Default
        private boolean oasisAssessmentRequired = false;

        @JsonProperty("last_oasis_date")
        private LocalDate lastOasisDate;

        @JsonProperty("next_oasis_due")
        private LocalDate nextOasisDue;

        @JsonProperty("quality_measures_reporting")
        @Builder.Default
        private boolean qualityMeasuresReporting = false;
    }

    // === ENUMS FOR PATIENT STATUS ===

    public enum CognitiveStatus {
        NORMAL, MILD_IMPAIRMENT, MODERATE_IMPAIRMENT, SEVERE_IMPAIRMENT, DEMENTIA
    }

    public enum MobilityStatus {
        INDEPENDENT, ASSISTED, WHEELCHAIR_BOUND, BEDRIDDEN
    }

    /**
     * Patient location and address information.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PatientLocation {

        @NotBlank(message = "Home address is required")
        @JsonProperty("home_address")
        private String homeAddress;

        @NotBlank(message = "City is required")
        @JsonProperty("city")
        private String city;

        @NotBlank(message = "State is required")
        @JsonProperty("state")
        private String state;

        @NotBlank(message = "Postal code is required")
        @JsonProperty("postal_code")
        private String postalCode;

        @JsonProperty("latitude")
        private Double latitude;

        @JsonProperty("longitude")
        private Double longitude;

        @JsonProperty("apartment_unit")
        private String apartmentUnit;

        @JsonProperty("building_name")
        private String buildingName;

        @JsonProperty("floor_number")
        private String floorNumber;

        @JsonProperty("access_instructions")
        private String accessInstructions;
    }

    // === HELPER METHODS ===

    /**
     * Get patient ID (compatibility method).
     */
    public String getPatientId() {
        return this.id;
    }

    /**
     * Get home address (compatibility method).
     */
    public String getHomeAddress() {
        return location != null ? location.getHomeAddress() : null;
    }

    /**
     * Get state (compatibility method).
     */
    public String getState() {
        if (location != null && location.getState() != null) {
            return location.getState();
        }
        if (stateCompliance != null && stateCompliance.getServiceState() != null) {
            return stateCompliance.getServiceState();
        }
        return null;
    }

    /**
     * Get full address as a single string.
     */
    public String getFullAddress() {
        if (location == null) return null;

        StringBuilder address = new StringBuilder();
        if (location.getHomeAddress() != null) {
            address.append(location.getHomeAddress());
        }
        if (location.getApartmentUnit() != null) {
            address.append(", Unit ").append(location.getApartmentUnit());
        }
        if (location.getCity() != null) {
            address.append(", ").append(location.getCity());
        }
        if (location.getState() != null) {
            address.append(", ").append(location.getState());
        }
        if (location.getPostalCode() != null) {
            address.append(" ").append(location.getPostalCode());
        }

        return address.toString();
    }

    /**
     * Check if patient has geographic coordinates.
     */
    public boolean hasCoordinates() {
        return location != null &&
               location.getLatitude() != null &&
               location.getLongitude() != null;
    }
}
