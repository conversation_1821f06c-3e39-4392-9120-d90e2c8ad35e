package com.caxl.assignment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Configuration properties for the assignment service.
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "assignment.config")
public class AssignmentProperties {
    
    private String rulesFile;
    private String skillHierarchiesFile;
    private double baseScore;
    private int maxAssignmentsPerBatch;
    private RelaxationConfig relaxation;
    
    /**
     * Configuration for the relaxation process.
     */
    @Data
    public static class RelaxationConfig {
        private List<String> strategies;
        private int maxDateShiftDays;
        private int extendedRadiusPercent;
        private int overtimeCapacityPercent;
        private int maxRelaxationsPerPatient;
    }
}
