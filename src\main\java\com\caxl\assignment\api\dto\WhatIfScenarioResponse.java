package com.caxl.assignment.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Response DTO for what-if scenario analysis results.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhatIfScenarioResponse {
    private String scenarioName;
    private String status;
    private LocalDateTime executedAt;
    private long executionTimeMs;
    
    // Scenario results
    private ScenarioScore scenarioScore;
    private ScenarioScore baselineScore;
    private ScoreComparison comparison;
    
    // Impact analysis
    private List<ImpactMetric> impactMetrics;
    private List<String> warnings;
    private List<String> recommendations;
    
    // Additional data
    private Map<String, Object> additionalData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScenarioScore {
        private String scoreValue;
        private int hardScore;
        private int softScore;
        private boolean feasible;
        private int totalAssignments;
        private double utilizationRate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScoreComparison {
        private double improvementPercentage;
        private String betterWorse;
        private int hardScoreDifference;
        private int softScoreDifference;
        private String summary;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpactMetric {
        private String metricName;
        private double baselineValue;
        private double scenarioValue;
        private double changePercentage;
        private String unit;
        private String interpretation;
    }
}
